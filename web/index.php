<?php
ini_set('max_execution_time', 900);
$serverName = $_SERVER["SERVER_NAME"] ?? '';
$serverHost = $_SERVER["HTTP_HOST"] ?? '';


if (
    (!str_contains($_SERVER['REQUEST_URI'], '/foquz/api/poll/create-from-json'))
    && (
        $_SERVER['REMOTE_ADDR'] === "************"
          || $_SERVER['REMOTE_ADDR'] === "*************"
        || @$_SERVER['HTTP_X_ENVOY_EXTERNAL_ADDRESS'] === "************"
        || preg_match("@(doxswf\.ru|devfoquz.ru)$@", $serverName)
        || preg_match("@(localhost:8081)$@", $serverHost)
            || getenv('YII_DEBUG')
    )
) {
    defined('YII_DEBUG') or define('YII_DEBUG', true);
    defined('YII_ENV') or define('YII_ENV', 'dev');


} else {
    defined('YII_DEBUG') or define('YII_DEBUG', false);
    defined('YII_ENV') or define('YII_ENV', 'prod');
}

require(__DIR__ . '/../vendor/autoload.php');
require_once(__DIR__ . '/../vendor/yiisoft/yii2/Yii.php');

$config = require(__DIR__ . '/../config/web.php');

    (new yii\web\Application($config))->run();
