.external-app {
  .s-app__footer {
    padding-top: 30px;
    padding-bottom: 30px;

    .only-mobile({
      padding-top: 0px;
      padding-bottom: 0px;

      .s-app-footer {
        min-height: initial;
      }
    })
  }

  .s-app-header {
    .container-fluid {
      .only-tablet {
        padding-left: 20px;
        padding-right: 20px;
      }
    }
  }


  .question-statistics__content {
    min-width: 0;


    .question-statistics__card {
      .gallery-button,
      .question-statistics__question-media-wrapper:not(.question-statistics__question-media-wrapper_external) {
        //display: none !important;
      }

      .question-statistics__question-additional-button {
        display: none;
      }


      &.show-answers_show {
        .question-statistics__question-additional-button {
          display: block;
        }

        .question-statistics__dynamics {
          display: none;
        }
      }

      .highcharts-container {
        max-width: 100%;
      }

      .question-statistics__question-average-rating-wrapper,
      .question-statistics__variant-statistics-legend-table-row,
      .question-statistics__priority-statistics-legend-table-row,
      .question-statistics__question-dish-ratings-statistics-table-row {
        cursor: default;
      }


    }

    .mobile-and-tablet({
      .f-card {
        margin-left: -15px;
        margin-right: -15px;
        border-radius: 0;
        position: relative;

      }

      .foquz-stats-item {
        padding-left: 15px;
        padding-right: 15px;

        &:first-of-type {
          padding-left: 20px;
        }
      }

      .question-statistics__time-block {
        margin-left: 18px;
      }

      .s-app__content {
        padding-bottom: 0;
      }

      .question-statistics__variant-statistics-chart {
        max-width: 100%;
      }
      .question-statistics__variant-statistics {
        display: block;

        .question-statistics__variant-statistics-chart-wrapper {
          width: 350px;
          max-width: 100%;
          &.filials-statistics-chart-wrapper {
            width: 343px;
          }
          &.priority-chart-wrapper {
            width: 100%;
          }
        }

        .question-statistics__variant-statistics-legend {
          overflow: auto;
          width: 100%;
          margin-left: 0;

          .os-scrollbar {
            opacity: 0;
          }
          &.filials-bar-chart-legend {
            max-width: 333px;
            margin-left: 20px;
             th {
              font-size: 12px;
             }
          }
        }
      }
      .date-input-group {
        display: inline-block;
        width: auto;
      }
      .f-card__section {
        padding-left: 20px;
        padding-right: 20px;
      }
    });

    .only-mobile({
      .question-statistics__filials-chart-wrapper, .filials-bar-chart-legend {
        height: auto;
        max-height: 100%;
      }
      .question-statistics__variant-statistics {
        margin-top: 10px;

        .question-statistics__variant-statistics-legend {
          margin-left: -15px;
          margin-right: -15px;
          width: calc(100% + 30px);
          &.filials-bar-chart-legend {
            margin-left: -15px;
          }
        }
        .filials-statistics-chart-wrapper {
          width: calc(100% + 30px) !important;
        }
        .filials-bar-chart-legend{
          max-width: calc(100% + 30px) !important;
        }
        
      }
      .points-block + .question-statistics__question-chart {

        margin-top: 0;

      }
      .points-block + .question-statistics__variant-statistics {
        .question-statistics__variant-statistics-chart {
          margin-top: 0;
        }
      }
      .f-card__section {
        padding-left: 15px;
        padding-right: 15px;
      }
      .foquz-stats-item {
        padding-left: 15px !important;

        &:last-of-type {
          border-bottom: none;
        }
      }
      .question-statistics__variant-statistics {
        margin-bottom: -1px;
        flex-direction: column;
        &.filials-bar-chart {
          flex-direction: column;
        }
      }
      .question-statistics__question-average-rating .question-statistics__question-average-rating-star {
        width: 17.94px;
        height: 17.77px;

      }
      .question-statistics__question-average-rating .question-statistics__question-average-rating-star:not(:first-child) {
        margin-left: 4px;
      }
      .question-statistics__question-average-rating-wrapper {
        margin-top: 15px;
      }
      .question-statistics__question {
        padding: 32px 15px 0;

        &:last-of-type {
          padding-bottom: 50px;
        }

        &-header {
          margin-left: 0;
        }
      }
      .question-statistics__question-dish-ratings-statistics-header {
        display: flex;
        flex-direction: column;
      }
      .question-statistics__question-dish-ratings-statistics-filter {
        .hat-radio-group__radio-label {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          white-space: nowrap;
          text-align: center;
        }
      }
      .question-statistics__question-dish-ratings-statistics-search-form {
        margin-left: 0;
        margin-top: 16px;
        width: 100%;
        max-width: none;
        margin-bottom: 16px;
      }
      .question-statistics__question-dish-ratings-statistics-filter .hat-radio-group {
        height: auto;
        width: 100%;
      }
      .question-statistics__question-dish-ratings-statistics-table-vote-count-cell {
        text-align: left !important;
      }

      .question-statistics__question-dish-ratings-statistics {
        .question-statistics__question-dish-ratings-statistics-table-vote-count-cell {
          width: 70px;
        }
      }

      tr.dish-name + tr {
        td {
          border-top: 0;
          height: auto;
          min-height: 33px;
        }
      }
      .question-statistics__question-dish-ratings-statistics-table-average-cell {
        text-align: right !important;
      }


      th.question-statistics__question-dish-ratings-statistics-table-average-rating-cell {
        .text-center {
          text-align: right !important;
        }
      }

      .question-statistics__rating-statistics-table-rating .question-statistics__rating-statistics-table-rating-star:not(:first-child) {
        margin-left: 4px;
      }

      table tr {
        td, th {
          &:first-child {
            padding-left: 15px !important;
          }

          &:last-child {
            padding-right: 15px !important;
          }
        }

      }

      .question-statistics__variant-statistics-legend-table th {
        white-space: normal;
        vertical-align: top;
      }

      .question-statistics__variant-statistics-legend-table-vote-count-head-cell {
        min-width: 90px;
      }

      .question-statistics__gallery-statistics,
      .rating-wrapper {
        margin-left: -15px;
        margin-right: -15px;
        margin-bottom: -1px;
        width: calc(100% + 30px);

        .scrollbar {
          opacity: 0;
        }
      }

      .stars-rating-wrapper {
        width: 100%;
        margin-right: 0 !important;
      }

      .question-statistics__question-average-rating + .table,
      .question-statistics__question-dish-ratings-statistics-table {
        margin-left: -15px;
        margin-right: -15px;
        width: calc(100% + 30px);
      }

      .question-statistics__variant-statistics-legend-table,
      .question-statistics__gallery-statistics .table {
        margin-bottom: 0;
      }

      .points-block {
        margin-top: 12px !important;
      }
    });

    .dense-form-group .select2-container--form-control .select2-selection--multiple .select2-search {
      margin-left: 0 !important;
    }
    .form-control.is-valid:not(.is-invalid), .is-valid:not(.is-invalid) .form-control {
      box-shadow: none !important;
      outline: none !important;
      border: none !important;
    }
  }

  .question-statistics__question-all-profiles-button {
    display: flex;
  }

  .question-statistics__question-all-profiles-button:before {
    display: inline-block;
    margin-right: 10px;
  }

  .question-statistics__question-all-addresses-button:before {
    display: inline-block;
    margin-right: 10px;
  }
}

.points-stats-modal-page {
  .foquz-modal-page__header {
    padding-right: 30px !important;
  }
  .modal.foquz-modal-page {
    .foquz-modal-page__mask {
      background: rgba(0, 0, 0, 0.65);
    }
    .foquz-modal-page__close {
      color: white;
      border: 2px solid #fff;
    }
    .table thead th {
      border-top: none;
    }
    .switch-form-group {
      .switch.form-control {
        width: 50px;
      }
    }
  }
}

.os-host-scrollbar-vertical-hidden .filials-bar-chart-legend-table thead, .native-scrollbar--y-start .filials-bar-chart-legend-table thead {
  box-shadow: none;
}
.os-host-scrollbar-vertical-hidden .filials-bar-chart-legend-table thead, .native-scrollbar--y-start .variants-statistics-legend-table thead {
  box-shadow: none;
}

.filials-bar-chart-legend .os-scrollbar.os-scrollbar-vertical {
  opacity: 1 !important;
}


.priority-chart-wrapper {
  .priority-chart-wrapper-column-chart {
    overflow-x: auto !important;
    &::-webkit-scrollbar {
      width: 4px;
      height: 4px;
    }
    
    &::-webkit-scrollbar-track {
      background: #E8EDEE;
      width: 8px;      /* цвет зоны отслеживания */
    }
    
    &::-webkit-scrollbar-thumb {
      background-color: #8E99A3;    /* цвет бегунка */
      border-radius: 20px;       /* округлось бегунка */
    }
  }
  }

  .priority-statistic-legend {
    overflow-x: auto !important;
    &::-webkit-scrollbar {
      width: 4px;
      height: 4px;
    }
    
    &::-webkit-scrollbar-track {
      background: #E8EDEE;
      width: 8px;      /* цвет зоны отслеживания */
    }
    
    &::-webkit-scrollbar-thumb {
      background-color: #8E99A3;    /* цвет бегунка */
      border-radius: 20px;       /* округлось бегунка */
    }
  }

.priority-statistic-legend.question-statistics__variant-statistics-legend {
  margin-left: 0 !important;
  max-height: 100% !important;
  max-width: 100%;
  overflow-x: hidden;
  .question-statistics__variant-statistics-legend-table {
    margin-bottom: 0;
  }
  .os-scrollbar-vertical {
    display: none;
  }
  .priority-statistic-legend-head-cell {
    position: sticky;
    left: 0;
    background-color: #fff;
    &::after {
      content: '';
      position: absolute;
      z-index: 2;
      opacity: 1;
      pointer-events: none;
      top: 0;
      bottom: 0;
      right: -15px;
      width: 15px;
      background: linear-gradient(to right, white, transparent);
    }
  }
  .question-statistics__priority-statistics-legend-table-text-cell {
    position: sticky;
    left: 0;
    background-color: #fff;
    &::after {
      content: '';
      position: absolute;
      z-index: 2;
      opacity: 1;
      pointer-events: none;
      top: 0;
      bottom: 0;
      right: -15px;
      width: 15px;
      background: linear-gradient(to right, white, transparent);
    }
    span {
      min-width: 335px;
      .only-mobile({
        min-width: 110px
      });
    }
  }
}

.priority-chart-toggle {
  margin-left: -6px;
}