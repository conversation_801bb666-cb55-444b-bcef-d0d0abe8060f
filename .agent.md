# .agent.md - Foquz-Core Development Guide for Rovo Dev

## Project Overview
Foquz-core is a comprehensive, enterprise-grade survey and polling platform built with a dual-layer architecture combining PHP backend views with Knockout.js frontend view models. The system supports complex survey logic, real-time analytics, and automated marketing workflows across multiple communication channels.

## Technology Stack

### Backend (PHP/Yii2)
- **Framework**: Yii 2 (PHP 8.2+)
- **Database**: MySQL with Redis caching
- **Queue System**: RabbitMQ for background processing
- **Architecture**: MVC pattern with console commands and modules
- **Key Features**: OAuth2 server, SAML authentication, Sentry error tracking

### Frontend (JavaScript/TypeScript)
- **Framework**: KnockoutJS with TypeScript
- **Build Tool**: Webpack 5 with Babel transpilation
- **CSS**: LESS preprocessing
- **Architecture**: MVVM pattern with component-based structure
- **UI**: Responsive design with mobile support

## Architecture Overview

### Dual-Layer Architecture
The system uses a **dual-layer architecture** that combines:
- **PHP Views**: Server-side rendering and data preparation
- **Knockout.js ViewModels**: Client-side logic, interactions, and dynamic UI

### Data Flow Pattern
1. **PHP Controller** prepares data and renders view
2. **PHP View** outputs JavaScript globals:
   ```php
   $this->registerJs("
       var QUESTIONS = " . Json::encode($questions) . ";
       var POLL = " . Json::encode($poll) . ";
   ");
   ```
3. **Knockout ViewModel** consumes globals
4. **Components** handle user interactions and state updates

## Directory Structure

### Standard Module Pattern
```
modules/foquz/
├── controllers/           # PHP controllers
├── models/               # ActiveRecord models
├── views/                # PHP view templates
├── public/assets/        # Static assets
└── widgets/              # Reusable components
```

### Frontend Structure
```
ko/                       # KnockoutJS components
├── components/           # Reusable UI components
├── pages/               # Page-specific ViewModels
├── widgets/             # Widget implementations
├── entities/            # Data models
└── bindings/            # Custom Knockout bindings
```

## Development Guidelines

### PHP Development Rules
1. **Follow Yii2 conventions** for controllers, models, and views
2. **Use ActiveRecord** for database operations
3. **Implement proper validation** in model classes
4. **Use dependency injection** where appropriate
5. **Follow PSR-4 autoloading** standards

### Frontend Development Rules
1. **Use TypeScript** for new components when possible
2. **Follow MVVM pattern** with Knockout.js
3. **Implement proper data binding** with observables
4. **Use component-based architecture**
5. **Maintain responsive design** principles

### Code Quality Standards
1. **Write comprehensive tests** for new features
2. **Use proper error handling** and logging
3. **Follow established naming conventions**
4. **Document complex business logic**
5. **Maintain backward compatibility**

## Key Architectural Patterns

### Answer Management System
The answer management system is one of the most complex parts of the codebase:
- Uses sophisticated state management
- Handles real-time data synchronization
- Implements complex validation logic
- Requires careful testing when modified

### Sidesheet Pattern
Sidesheets are used for secondary interfaces:
- Implement consistent UI patterns
- Handle data loading and state management
- Follow established component lifecycle

### Widget System
Widgets are reusable components that:
- Can be embedded in various contexts
- Have their own build configurations
- Follow modular architecture principles

## Database Conventions
1. **Use migrations** for schema changes
2. **Follow naming conventions** for tables and columns
3. **Implement proper indexing** for performance
4. **Use foreign key constraints** where appropriate
5. **Document complex queries** and stored procedures

## Testing Guidelines
1. **Write unit tests** for models and services
2. **Implement integration tests** for complex workflows
3. **Use functional tests** for user-facing features
4. **Maintain test coverage** above 80%
5. **Test edge cases** and error conditions

## Performance Considerations
1. **Use caching** strategically (Redis)
2. **Optimize database queries** and avoid N+1 problems
3. **Implement lazy loading** for large datasets
4. **Use CDN** for static assets
5. **Monitor performance** with profiling tools

## Security Best Practices
1. **Validate all user input** on both client and server
2. **Use parameterized queries** to prevent SQL injection
3. **Implement proper authentication** and authorization
4. **Sanitize output** to prevent XSS attacks
5. **Use HTTPS** for all communications

## Deployment and DevOps
1. **Use Docker** for containerization
2. **Implement CI/CD** pipelines
3. **Use environment-specific** configurations
4. **Monitor application** health and performance
5. **Implement proper logging** and error tracking

## Important Notes for Rovo Dev
- This is a **mature codebase** with established patterns
- The **answer management system** and **sidesheets** are the most complex parts
- **Approach modifications carefully** with thorough planning and testing
- **Follow existing conventions** rather than introducing new patterns
- **Be especially careful** when modifying answer-related modules due to intricate state management
- **Test thoroughly** before deploying changes to production

## Common Pitfalls to Avoid
1. **Don't break existing data flow patterns**
2. **Don't modify core answer management without understanding dependencies**
3. **Don't introduce new frontend frameworks** without team consensus
4. **Don't skip database migrations** for schema changes
5. **Don't ignore existing validation logic**

## Getting Help
- Review existing code patterns before implementing new features
- Consult team members for complex architectural decisions
- Use established debugging tools and techniques
- Follow the existing error handling patterns