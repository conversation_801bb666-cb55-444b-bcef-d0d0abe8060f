<?php

namespace app\modules\foquz\services;

use app\models\User;
use app\modules\foquz\models\FilialPollKey;
use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\FoquzPollShortLink;
use Yii;
use yii\db\Exception;

class ShortLinkService
{
    /**
     * @var FoquzPoll
     */
    protected $poll;

    public function __construct($poll)
    {
        $this->poll = $poll;
    }

    /**
     * @throws Exception
     */
    public function saveAll(): void
    {
        $this->removeAll();

        Yii::$app->db->createCommand()->batchInsert(
            'foquz_poll_short_links',
            ['link', 'code'],
            $this->collectInserts()
        )->execute();

        $this->poll->is_short_link = true;
        $this->poll->save();
    }

    /**
     * @throws Exception
     */
    public function saveOne($link): ?string
    {
        $l = new FoquzPollShortLink();
        $l->link = $link;
        $l->code = FoquzPollShortLink::unique_code();
        $l->save();
        return $l->code;
    }

    public function removeAll() : void
    {
        FoquzPollShortLink::deleteAll(['in', 'link', $this->collectLinks()]);
        $this->poll->is_short_link = true;
        $this->poll->save();
    }

    public function collectLinks()
    {
        return array_map(function ($item) {
            return $this->getLink($item->key);
        }, $this->getPollKeys());
    }

    public function getShortLink($code)
    {
        /** @var User $identity */
        $identity = Yii::$app->user->identity;
        return $identity->company->shortLink.$code;
    }

    public function findShortLinkByKey(string $code): string|null
    {
        return FoquzPollShortLink::find()
            ->select(['code'])
            ->where(['link' => $this->getLink($code)])
            ->scalar();
    }

    public function getLink($key): string
    {
        /** @var User $identity */
        $identity = Yii::$app->user->identity;
        return Yii::$app->params['protocol'].'://'.$identity->company->alias.'/p/'.$key;
    }

    public function updateShortLink($link): void
    {
        $shortLink = FoquzPollShortLink::findOne(['link' => $link]);

        if ($shortLink !== null) {
            $shortLink->link = $this->getLink($this->poll->key);
            $shortLink->save();
        }
    }

    private function collectInserts()
    {
        $result = [
            [
                $this->getLink($this->poll->key),
                FoquzPollShortLink::unique_code()
            ]
        ];

        foreach ($this->getPollKeys() as $key) {
            $result[] = [
                $this->getLink($key->key),
                FoquzPollShortLink::unique_code()
            ];
        }

        return $result;
    }

    private function getPollKeys()
    {
        return FilialPollKey::find()
            ->where(['foquz_poll_id' => $this->poll->id])
            ->all();
    }
}