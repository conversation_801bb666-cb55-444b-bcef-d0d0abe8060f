<?php

namespace app\modules\foquz\services;

use app\models\FilialCategory;
use app\models\User;
use app\modules\foquz\models\FoquzComplaintFile;
use app\modules\foquz\models\FoquzPollAnswer;
use app\modules\foquz\models\processing\FoquzPollAnswerProcessing;
use app\modules\foquz\services\dto\AnswerTagDto;
use yii\helpers\ArrayHelper;

/**
 * Сервис конструктор сбора ответов.
 *
 * @property array $answersResultItems
 * @property array $processingStats
 * @property array $answerData
 * @property array $answerIds
 */

class AnswerResponseConstructService
{
    private array $answersResultItems;
    private array $processingStats;
    private array $answerData;

    public function __construct(array $answersResult)
    {
        $this->answersResultItems = $answersResult;

        $this->processingStats = $this->processingStatsCreate();
        $this->answerData = $this->collectAnswerData();
    }

    /**
     * @return array
     */
    public function collectAnswerData(){
        $answersArr = $this->getAnswerItemsArray();

        $answersArray = [];
        foreach($this->answersResultItems as $answerArrayItem)
        {
            $this->answerIds[] = $answerArrayItem['id'];

            /** @var FoquzPollAnswer $answer */
            $answer = $answersArr[$answerArrayItem['id']] ?? null;
            if (!$answer) {
                continue;
            }

            $answersArray[] = $this->responseItem($answer);
        }

        return $answersArray;
    }

    /**
     * @return array
     */
    public function getAnswerData(){
        return $this->answerData;
    }

    /**
     * @return array
     */
    public function getAnswerIds(){
        return $this->answerIds ?? [];
    }

    private function getAnswerItemsArray()
    {
        $answersIDS = ArrayHelper::getColumn($this->answersResultItems, "id");
        $answersArr = ArrayHelper::map(
            FoquzPollAnswer::find()
                ->with([
                    "foquzAnswer.foquzQuestion",
                    "foquzPollDishes",
                    "pollLang",
                    "answerFilial",
                    "contact",
                    "contact.additionalFieldValues",
                    "contact.computedFields",
                    "contact.contactTags.tag",
                    "contact.contactTags.tag.conditions",
                    "foquzPoll",
                    "foquzPoll.foquzQuestions",
                    "foquzPoll.foquzQuestions.starRatingOptions",
                    "foquzPoll.foquzQuestions.scaleRatingSetting",
                    "foquzPoll.foquzQuestions.questionDetails",
                    "processing",
                    "processing.executor",
                    "processing.moderator",
                    "order",
                    "complaint",
                    "answerChannel",
                    "sends",
                    "mailingListSend.mailingListContact.mailingList",
                    "tags",
                ])
                ->where(['IN', 'id', $answersIDS])->all(),
            "id", function($m) {return $m;});

        return $answersArr;
    }

    /**
     * @param User $user
     * @return array
     */
    private function getUserInfo(User $user)
    {
        return [
            'name' => $user->correctName,
            'avatar' => $user->getThumbUploadUrl('avatar', 'preview')
        ];
    }

    /** Время прохождения анкеты
     *
     * @param FoquzPollAnswer $answer
     * @return array
     */
    private function clientAdditionalFields(FoquzPollAnswer $answer)
    {
        $clientAdditionalFields = [];
        if($answer->contact && count($answer->contact->additionalFieldValues) > 0) {
            foreach($answer->contact->additionalFieldValues as $afv) {
                $clientAdditionalFields[$afv->additional_field_id] = $afv->value;
            }
        }

        return $clientAdditionalFields;
    }

    /**
     * @param FoquzPollAnswer $answer
     * @return string
     */
    private function filialName(FoquzPollAnswer $answer)
    {
        $filialName = '';
        if ($answer->foquzPoll->is_auto) {
            if ($answer->order && $answer->order->filial) {
                if ($categoryId = $answer->order->filial->category_id) {
                    $filialName = FilialCategory::findOne($categoryId)->name . '/' . $answer->order->filial->name;
                } else {
                    $filialName = $answer->order->filial->name;
                }
            }
        } else {
            if ($answer->answerFilial) {
                if ($categoryId = $answer->answerFilial->category_id) {
                    $filialName = FilialCategory::findOne($categoryId)->name . '/' . $answer->answerFilial->name;
                } else {
                    $filialName = $answer->answerFilial->name;
                }
            }
        }

        return $filialName;
    }


    private function processingStatsCreate()
    {
        return FoquzPollAnswerProcessing::processingStatsCreate();
    }

    private function processingStatsSet($status)
    {
        if (!is_null($status)) {
            $this->processingStats[$status]['count']++;
        }
    }

    public function processingStatsGet()
    {
        return $this->processingStats;
    }

    /**
     * @param FoquzPollAnswer $answer
     * @return array
     */
    private function responseItem(FoquzPollAnswer $answer)
    {
        $this->processingStatsSet($answer->processing ? $answer->processing->status : null);

        $pointPercent = $answer->max_points > 0 ? round(($answer->points / $answer->max_points) * 100) : 0;
        if ($pointPercent < 0) {
            $pointPercent = 0;
        }

        $ret = [
            'id' => $answer->id,
            'processingTimeAt' => isset($answer->processing->process_up) ? date('d.m.Y', strtotime($answer->processing->process_up)) : null, //date('d.m.Y', strtotime($answer->processing->delayed_up)),
            'isAuto' => $answer->foquzPoll->is_auto,
            'pollName' => $answer->foquzPoll->name,
            'pollId' => $answer->foquzPoll->id,
            'passedAt' => in_array($answer->status, [FoquzPollAnswer::STATUS_IN_PROGRESS, FoquzPollAnswer::STATUS_DONE, FoquzPollAnswer::STATUS_QUOTE_FULL, FoquzPollAnswer::STATUS_SCREEN_OUT]) ? date('d.m.Y H:i', strtotime($answer->updated_at)) : null,
            'processingId' => $answer->processing ? $answer->processing->id : null,
            'executor' => $answer->processing && $answer->processing->executor ? $this->getUserInfo($answer->processing->executor) : null,
            'moderator' => $answer->processing && $answer->processing->moderator ? $this->getUserInfo($answer->processing->moderator) : null,
            'processingStatus' => $answer->processing ? $answer->processing->status : null,
            'processingEnabled' => (bool) $answer->processing,
            'clientName' => $answer->contact ? implode(' ', [$answer->contact->last_name, $answer->contact->first_name, $answer->contact->patronymic]) : null,
            'clientPhone' => $answer->contact->formattedPhone ?? null,
            'clientEmail' => $answer->contact->email ?? null,
            'clientFilials' => array_map(function($item){ return $item->filial->toArray();}, $answer->contact->contactFilials ?? []),
            'clientBirthday' => $answer->contact->birthday ?? null,
            'clientGender' => $answer->contact->gender ?? null,
            'clientTags' => array_map(function($item){ return $item->tag->toArray(); }, $answer->contact->contactTags ?? []),
            'clientAdded' => $answer->contact ? date("Y-m-d", $answer->contact->created_at) : null,
            'clientUpdated' => $answer->contact ? date("Y-m-d", $answer->contact->updated_at) : null,
            'clientComputedFields' => ($answer->contact && $answer->contact->computedFields) ? $answer->contact->computedFields->toObject() : null,
            'clientCustomerId' => $answer->contact ? $answer->contact->company_client_id : null,
            'clientAdditionalFields' => $this->clientAdditionalFields($answer),
            'clientPersonalData' => $answer->user_agreement,
            'answerTags' => array_map(
                fn ($tag) => AnswerTagDto::getResponseItem($tag),
                $answer->tags
            ),

            /** answers */
            'answers' => $answer->getAnswerItems(),
            'answerChannel' => null,
            'sends' => $answer->sends,
            'filial' => $this->filialName($answer),
            'complaint' => $answer->complaint ? [
                'text' => $answer->complaint->text,
                'photoUrls' => FoquzComplaintFile::find()->select(['file_path'])->where(['foquz_poll_answer_id' => $answer->id])->column()
            ] : null,
            'order' => $answer->order ? [
                'number' => $answer->order->number ?? $answer->order->id,
                'sum' => $answer->order->sum,
                'createdTime' => date('d.m.Y H:i', strtotime($answer->order->created_time)),
                'deliveryType' => $answer->order->delivery_type,
                'sourceType' => $answer->order->source_type,
                'deliveryAddress' => $answer->order->address,
            ] : null,
            'points' => $answer->foquzPoll->point_system ? [
                'answer_points' => $answer->points ?? 0,
                'points_max' => $answer->max_points,
                'percent' => $pointPercent,
                'without_points' => $answer->withoutPoints,
            ] : [],
            'device' => $answer->device,
            'mailing' => $answer->mailingListSend[0]->mailingListContact->mailingList->name ?? '',
            'elapsedTime' => $answer->elapsedTime,
            'timeToPass' => $answer->foquzPoll->time_to_pass,
            'hash' => $answer->hash_id ?? null,
            'language' => $answer->pollLang,
            'status' => $answer->status ?? null,
            'anketaStatus' => $answer->displayStatus,
            'anketaLink'   => $answer->linkQuote?->name,
            'anketaLinkStatus'   => $answer->linkQuote?->active,
            'anketaQuote'   => $answer->answerItemQuote?->quote?->name,
            'answerTime' => $answer->answerTime(),
        ];
        if ($answer->answerChannel) {
            $ret['answerChannel'] = [
                'id' => $answer->answerChannel->id,
                'contact_id' => $answer->answerChannel->contact_id,
                'answer_id' => $answer->answerChannel->answer_id,
                'channel_id' => $answer->answerChannel->channel_id,
                'repeat_id' => $answer->answerChannel->repeat_id,
                'widget_id' => $answer->answerChannel->widget_id,
                'status' => $answer->answerChannel->status,
                'key' => $answer->answerChannel->key,
                'sended' => $answer->answerChannel->sended,
                'channel_name' => $answer->answerChannel->channel_name,
                'answer_service' => $answer->answerChannel->answer_service,
                'source' => $answer->answerChannel->source,
                'widget' => [
                    'id' => $answer->answerChannel->widget?->id,
                    'name' => $answer->answerChannel->widget?->name,
                    'site' => $answer->answerChannel->widgetSite?->site,
                    'source' => $answer->answerChannel->parseSource()['source'],
                    'scheme' => $answer->answerChannel->parseSource()['scheme']
                ]
            ];
        }
        return $ret;
    }
}