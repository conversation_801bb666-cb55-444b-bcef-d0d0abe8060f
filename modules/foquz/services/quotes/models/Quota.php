<?php

declare(strict_types=1);

namespace app\modules\foquz\services\quotes\models;

/**
 * Модель основной квоты опроса по ссылке
 *
 * Содержит:
 * - Основные параметры квоты (лимит, дата окончания)
 * - Связанные квоты по ответам
 * - Методы для проверки соответствия ответов критериям
 */
class Quota
{
    /** @var int ID */
    public int $id;

    /** @var ?int Лимит ответов */
    public ?int $limit;

    /** @var string|null Дата окончания квоты */
    public ?string $datetimeEnd;

    /** @var bool Активна ли квота */
    public bool $active;

    /** @var int|null ID экрана Screenout */
    public ?int $endScreenout;

    /** @var int|null ID экрана при QuotaFull */
    public ?int $endScreenQuotefull;

    /** @var int Текущее количество ответов */
    public int $answersCount;

    /** @var bool Превышено ли количество ответов */
    public bool $isAnswerLimited;

    /** @var QuotaAnswer[] Квоты по ответам */
    public array $answerQuotes = [];

    /**
     * Инициализирует объект квоты данными из БД
     *
     * @param array $data Массив данных квоты:
     * - id: ID квоты
     * - limit: Лимит ответов
     * - datetime_end: Дата окончания
     * - active: Активность
     * - end_screenout: ID экрана screenout
     * - end_screen_quotefull: ID экрана quotafull
     * - answers_count: Текущее количество ответов
     * - is_answer_limited: Ограничение ответов
     * - answerQuotes: Массив квот по ответам
     */
    public function init(array $data): void
    {
        $this->id = $data['id'];
        $this->limit = $data['limit'];
        $this->datetimeEnd = $data['datetime_end'];
        $this->active = (bool)$data['active'];
        $this->endScreenout = $data['end_screenout'];
        $this->endScreenQuotefull = $data['end_screen_quotefull'] ? $data['end_screen_quotefull'] : null;
        $this->answersCount = $data['answers_count'];
        $this->isAnswerLimited = (bool)$data['is_answer_limited'];

        if (!empty($data['answerQuotes'])) {
            foreach ($data['answerQuotes'] as $answerQuoteData) {
                $this->answerQuotes[$answerQuoteData['id']] = new QuotaAnswer([
                    'id'              => $answerQuoteData['id'],
                    'answers_limit'   => $answerQuoteData['answers_limit'],
                    'answers_count'   => $answerQuoteData['answers_count'],
                    'logic_operation' => $answerQuoteData['logic_operation'],
                    'end_screen'      => $answerQuoteData['end_screen'],
                    'end_screen_done' => $answerQuoteData['end_screen_done'],
                    'criteria'        => array_map(
                        fn($c) => new QuotaAnswerCriterion($c),
                        $answerQuoteData['foquzPollAnswerQuotesDetails'] ?? []
                    ),
                    'groups'          => array_map(
                        function ($g) {
                            $group = new QuotaAnswerGroupCriterion([
                                'logic_operation' => $g['logic_operation']
                            ]);
                            $group->criteria = array_map(
                                fn($c) => new QuotaAnswerCriterion($c),
                                $g['foquzPollAnswerQuotesDetails'] ?? []
                            );
                            return $group;
                        },
                        $answerQuoteData['foquzPollAnswerQuotesGroups'] ?? []
                    )
                ]);
            }
        }
    }

    /**
     * Находит подходящую квоту по ответам
     *
     * @param int $answerId ID ответа
     * @param array $answers Массив ответов пользователя
     * @return int|null ID найденной квоты или null если не определена
     */
    public function findAnswerQuota(int $answerId, array $answers): ?int
    {
        $result = 0;
        foreach ($this->answerQuotes as $answerQuote) {
            $quotaCheck = $answerQuote->check($answerId, $answers);
            if (is_null($quotaCheck)) {
                $result = null;
            } elseif ($quotaCheck) {
                return $answerQuote->id;
            }
        }

        return $result;
    }


}
