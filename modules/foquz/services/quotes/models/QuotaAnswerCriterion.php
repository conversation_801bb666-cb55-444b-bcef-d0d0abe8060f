<?php

declare(strict_types=1);

namespace app\modules\foquz\services\quotes\models;

use app\modules\foquz\models\FoquzPollAnswerItem;
use app\modules\foquz\models\quotes\FoquzPollAnswerQuotesDetail;

/**
 * Модель критерия отбора для квоты по ответам
 *
 * Определяет правила проверки соответствия ответа критериям:
 * - Выбор/не выбор определенных вариантов
 * - Пропуск вопроса
 * - Затруднился ответить на вопрос
 */
class QuotaAnswerCriterion
{
    /**
     * @var int ID вопроса
     */
    public int $questionId;

    /**
     * @var int Тип поведения (использует константы из FoquzPollAnswerQuotesDetail)
     * @see FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE
     * @see FoquzPollAnswerQuotesDetail::BEHAVIOR_DONT_CHOOSE
     * @see FoquzPollAnswerQuotesDetail::BEHAVIOR_SKIP
     * @see FoquzPollAnswerQuotesDetail::BEHAVIOR_DONT_ASK
     */
    public int $behavior;

    /**
     * @var array|null  варианты ответов (может быть null)
     */
    public ?array $variants;

    /**
     * Конструктор критерия отбора
     *
     * @param array $data Данные критерия:
     * - question_id: ID вопроса
     * - behavior: Тип поведения (см. константы BEHAVIOR_*)
     * - variants: JSON-сериализованные варианты ответов (опционально)
     */
    public function __construct(array $data)
    {
        $this->questionId = $data['question_id'];
        $this->behavior = $data['behavior'];
        $this->variants = isset($data['variants']) ? json_decode($data['variants'], true) : null;
    }

    /**
     * Проверяет соответствие ответа критерию
     *
     * @param int $answerId ID ответа
     * @param FoquzPollAnswerItem[] $answers Массив ответов пользователя
     * @return bool|null Результат проверки:
     * - true: соответствует критерию
     * - false: не соответствует
     * - null: невозможно определить (ответа на вопрос еще нет)
     */
    public function check(int $answerId, array &$answers): ?bool
    {
        if (!isset($answers[$this->questionId])) {
            $answers[$this->questionId] = FoquzPollAnswerItem::find()
                ->where(['foquz_poll_answer_id' => $answerId, 'foquz_question_id' => $this->questionId])
                ->one();
        }

        //результат не определен, ответа на нужный вопрос еще не было
        if (empty($answers[$this->questionId])) {
            return null;
        }

        /** @var string|array|null $detailItem */
        $detailItem = $answers[$this->questionId]->detail_item;
        if (!empty($detailItem) && !is_array($detailItem)) {
            $detailItem = json_decode($detailItem, true);
            if ($answers[$this->questionId]->self_variant) {
                $detailItem[]  = -1;
            }
            $answers[$this->questionId]->detail_item = $detailItem;
        }


        return match ($this->behavior) {
            FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE => !empty($answers[$this->questionId]->detail_item)
                && !empty(array_intersect($this->variants, $answers[$this->questionId]->detail_item)),

            FoquzPollAnswerQuotesDetail::BEHAVIOR_DONT_CHOOSE => !empty($answers[$this->questionId]->detail_item)
                && empty(array_intersect($this->variants, $answers[$this->questionId]->detail_item)),

            FoquzPollAnswerQuotesDetail::BEHAVIOR_SKIP => empty($answers[$this->questionId]->detail_item)
                && $answers[$this->questionId]->skipped,

            FoquzPollAnswerQuotesDetail::BEHAVIOR_DONT_ASK => empty($answers[$this->questionId]->detail_item)
                && !$answers[$this->questionId]->skipped,

            default => false,
        };

    }
}
