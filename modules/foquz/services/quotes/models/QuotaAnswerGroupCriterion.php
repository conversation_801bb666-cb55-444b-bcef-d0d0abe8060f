<?php

declare(strict_types=1);

namespace app\modules\foquz\services\quotes\models;

use app\modules\foquz\models\FoquzPollAnswerItem;
use app\modules\foquz\models\quotes\FoquzPollAnswerQuotes;

/**
 * Модель группы критериев для квоты по ответам
 *
 * Группирует критерии проверки с определенной логикой (AND/OR)
 * и позволяет проверять ответы по всем критериям группы
 */
class QuotaAnswerGroupCriterion
{

    /**
     * @var int Логическая операция (AND/OR)
     * @see FoquzPollAnswerQuotes::LOGIC_OPERATION_AND
     * @see FoquzPollAnswerQuotes::LOGIC_OPERATION_OR
     */
    public int $logicOperation;

    /**
     * @var QuotaAnswerCriterion[] Критерии в группе
     */
    public array $criteria = [];

    /**
     * Конструктор группы критериев
     *
     * @param array $data Данные группы:
     * - logic_operation: Логическая операция (AND/OR)
     * - criteria: Массив критериев QuotaAnswerCriterion
     */
    public function __construct(array $data)
    {
        $this->logicOperation = $data['logic_operation'];
        $this->criteria = $data['criteria'] ?? [];
    }


    /**
     * Проверяет ответ по всем критериям группы
     *
     * @param int $answerId ID ответа
     * @param FoquzPollAnswerItem[] $answers Массив ответов пользователя
     * @return bool|null Результат проверки:
     * - true: соответствует критериям группы
     * - false: не соответствует
     * - null: невозможно определить (нет ответов на некоторые вопросы)
     */
    public function check(int $answerId, array &$answers): ?bool
    {
        $result = false;

        foreach ($this->criteria as $criteria) {
            $criteriaCheck = $criteria->check($answerId, $answers);
            if (is_null($criteriaCheck)) {
                //нет ответа, результат не определен, если нет других решающих
                $result = null;
            } elseif ($criteriaCheck) {
                if ($this->logicOperation == FoquzPollAnswerQuotes::LOGIC_OPERATION_OR) {
                    return true;
                }
            } else {
                if ($this->logicOperation == FoquzPollAnswerQuotes::LOGIC_OPERATION_AND) {
                    return false;
                }
            }
        }

        if (is_null($result)) {
            return null;
        }

        return $this->logicOperation == FoquzPollAnswerQuotes::LOGIC_OPERATION_AND;
    }
}
