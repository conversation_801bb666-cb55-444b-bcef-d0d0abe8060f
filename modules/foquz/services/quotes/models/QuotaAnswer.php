<?php

declare(strict_types=1);

namespace app\modules\foquz\services\quotes\models;

use app\modules\foquz\models\FoquzPollAnswerItem;
use app\modules\foquz\models\quotes\FoquzPollAnswerQuotes;

/**
 * Модель квоты по ответам
 * 
 * Содержит:
 * - Параметры квоты по ответам (лимит, счетчик)
 * - Логику проверки соответствия критериям
 * - Связанные критерии и группы критериев
 */
class QuotaAnswer
{
    /** @var int ID квоты по ответам */
    public int $id;

    /** @var ?int Лимит ответов */
    public ?int $answersLimit;

    /** @var int Текущее количество ответов */
    public int $answersCount;

    /**
     * @var int Логическая операция (AND/OR)
     * @see FoquzPollAnswerQuotes::LOGIC_OPERATION_AND
     * @see FoquzPollAnswerQuotes::LOGIC_OPERATION_OR
     */
    public int $logicOperation;

    /** @var int|null ID экрана при quotafull */
    public ?int $endScreen;

    /** @var int|null ID экрана при успешном прохождении */
    public ?int $endScreenDone;

    /** @var QuotaAnswerCriterion[] Критерии отбора для квоты */
    public array $criteria = [];

    /** @var QuotaAnswerGroupCriterion[] Группы критериев */
    public array $groups = [];

    /**
     * Конструктор модели квоты ответа
     * 
     * @param array $data Данные для инициализации:
     * - id: ID квоты
     * - answers_limit: Лимит ответов
     * - answers_count: Текущее количество ответов
     * - logic_operation: Логическая операция (AND/OR)
     * - end_screen: ID экрана quotafull
     * - end_screen_done: ID экрана при успешном прохождении
     * - criteria: Массив критериев
     * - groups: Массив групп критериев
     */
    public function __construct(array $data)
    {
        $this->id = $data['id'];
        $this->answersLimit = $data['answers_limit'];
        $this->answersCount = $data['answers_count'];
        $this->logicOperation = $data['logic_operation'];
        $this->endScreen = $data['end_screen'];
        $this->endScreenDone = $data['end_screen_done'];
        $this->criteria = $data['criteria'] ?? [];
        $this->groups = $data['groups'] ?? [];
    }

    /**
     * Проверяет соответствие ответа критериям квоты
     * 
     * @param int $answerId ID проверяемого ответа
     * @param FoquzPollAnswerItem[] $answers Массив ответов пользователя
     * @return bool|null Результат проверки:
     * - true: соответствует критериям
     * - false: не соответствует
     * - null: невозможно определить (недостаточно ответов)
     */
    public function check(int $answerId, array &$answers): ?bool
    {
        $result = false;

        if (empty($this->groups) && empty($this->criteria)) {
            return  null;
        }

        foreach ($this->groups as $group) {
            $groupCheck = $group->check($answerId, $answers);
            if (is_null($groupCheck)) {
                $result = null;
            } elseif ($groupCheck) {    
                if ($this->logicOperation == FoquzPollAnswerQuotes::LOGIC_OPERATION_OR) {
                    return true;
                }
            } else {
                if ($this->logicOperation == FoquzPollAnswerQuotes::LOGIC_OPERATION_AND) {
                    return false;
                }
            }
        }

        foreach ($this->criteria as $criteria) {
            $criteriaCheck = $criteria->check($answerId, $answers);
            if (is_null($criteriaCheck)) {
                $result = null;
            } elseif ($criteriaCheck) {
                if ($this->logicOperation == FoquzPollAnswerQuotes::LOGIC_OPERATION_OR) {
                    return true;
                }
            } else {
                if ($this->logicOperation == FoquzPollAnswerQuotes::LOGIC_OPERATION_AND) {
                    return false;
                }
            }
        }

        if (is_null($result)) {
            return  null;
        }

         return $this->logicOperation == FoquzPollAnswerQuotes::LOGIC_OPERATION_AND;
    }
}
