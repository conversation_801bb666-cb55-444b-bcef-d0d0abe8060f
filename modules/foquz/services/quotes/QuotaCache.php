<?php

declare(strict_types=1);

namespace app\modules\foquz\services\quotes;

use app\modules\foquz\models\quotes\FoquzPollLinkQuotes;
use app\modules\foquz\services\quotes\models\Quota;
use Yii;
use yii\base\InvalidConfigException;
use yii\redis\Connection;

/**
 * Сервис для кэширования данных о квотах опросов
 */
class QuotaCache
{
    /**
     * Префикс ключа для хранения данных квоты в кэше
     * @var string
     */
    const CACHE_PREFIX = 'quota_data_';
    
    /**
     * Префикс ключа для хранения счетчиков ответов в Redis
     * @var string
     */
    const COUNTER_PREFIX = 'quota_counter_';

    /**
     * Время жизни данных в кэше (в секундах)
     * @var int
     * @default 86400 (24 часа)
     */
    const CACHE_DURATION = 86400;

    /**
     * Получает данные квоты из кэша или загружает из БД при отсутствии в кэше
     * Устанавливает счетчики ответов значениями из БД
     *
     * @param int $quoteId ID квоты
     * @return Quota Объект квоты с данными
     * @throws \yii\base\InvalidConfigException Если компонент кэша не настроен
     */
    public static function getOrCreate(int $quoteId): Quota
    {
        $cacheKey = self::CACHE_PREFIX . $quoteId;
        $data = Yii::$app->cache->get($cacheKey);
     //   $data = false;
        if ($data === false) {
            $data = self::loadFromDatabase($quoteId);
            Yii::$app->cache->set($cacheKey, $data, self::CACHE_DURATION);

            if (!empty($data)) {
                self::setCounter($quoteId, null, $data['answers_count']);
                foreach ($data['answerQuotes'] as $quota) {
                    self::setCounter($quoteId, $quota['id'], $quota['answers_count']);
                }
            }
        }

        return self::initQuota($data);
    }

    /**
     * Получает текущее значение счетчика ответов для указанной квоты
     *
     * @param int $quoteId ID основной квоты
     * @param int|null $answerQuoteId ID квоты по ответам (null для основной квоты)
     * @return int Текущее количество ответов
     * @throws \yii\base\InvalidConfigException Если компонент Redis не настроен
     */
    public static function getCounter(int $quoteId, ?int $answerQuoteId = null): int
    {
        $key = self::getKeyCounter($quoteId, $answerQuoteId);
        /** @var Connection $redis */
        $redis = Yii::$app->redis;
        return (int)$redis->get($key);
    }

    /**
     * Устанавливает начальное значение счетчика ответов
     *
     * @param int $quoteId ID основной квоты
     * @param int|null $answerQuoteId ID квоты по ответам (null для основной квоты)
     * @param int $count Начальное значение счетчика
     * @throws \yii\base\InvalidConfigException Если компонент Redis не настроен
     */
    private static function setCounter(int $quoteId, ?int $answerQuoteId, int $count): void
    {
        $key = self::getKeyCounter($quoteId, $answerQuoteId);
        /** @var Connection $redis */
        $redis = Yii::$app->redis;
        $redis->set($key, $count);
        $redis->expire($key, self::CACHE_DURATION * 2);
    }

    /**
     * Увеличивает значение счетчика ответов на 1
     *
     * @param int $quoteId ID основной квоты
     * @param int|null $answerQuoteId ID квоты по ответам (null для основной квоты)
     * @throws \yii\base\InvalidConfigException Если компонент Redis не настроен
     */
    public static function incrementCounter(int $quoteId, ?int $answerQuoteId = null): void
    {
        $key = self::getKeyCounter($quoteId, $answerQuoteId);
        /** @var Connection $redis */
        $redis = Yii::$app->redis;
        $redis->incr($key);
    }

    /**
     * Формирует ключ для хранения счетчика в Redis
     *
     * @param int $quoteId ID основной квоты
     * @param int|null $answerQuoteId ID квоты по ответам (null для основной квоты)
     * @return string Сформированный ключ
     */
    private static function getKeyCounter(int $quoteId, ?int $answerQuoteId): string
    {
        $answerQuoteId = $answerQuoteId ?? 'base';
        return self::COUNTER_PREFIX . $quoteId . '_' . $answerQuoteId;
    }

    /**
     * Загружает данные квоты из базы данных
     *
     * @param int $quoteId ID квоты
     * @return array Данные квоты
     */
    private static function loadFromDatabase(int $quoteId): array
    {
        $data = FoquzPollLinkQuotes::find()
            ->with([
                // квоты на ответы
                'answerQuotes' => function ($query) {
                    $query->select([
                        'id',
                        'link_quote_id',
                        'answers_limit',
                        'answers_count',
                        'logic_operation',
                        'end_screen',
                        'end_screen_done'
                    ])
                        ->where(['deleted_at' => null])
                        ->with([
                            // Критерии не в группах
                            'foquzPollAnswerQuotesDetails' => function ($query) {

                                $query->select([
                                    'quote_id',
                                    'question_id',
                                    'behavior',
                                    'variants'
                                ])
                                    ->andWhere(['deleted_at' => null, 'group_id' => null])
                                    ->orderBy(['position' => SORT_ASC]);
                            },
                            // Группы критериев
                            'foquzPollAnswerQuotesGroups'  => function ($query) {
                                $query->select([
                                    'id',
                                    'quote_id',
                                    'logic_operation'
                                ])
                                    ->andWhere(['deleted_at' => null])
                                    ->orderBy(['position' => SORT_ASC])
                                    ->with([
                                        // Критерии внутри групп
                                        'foquzPollAnswerQuotesDetails' => function ($query) {
                                            $query->select([
                                                'group_id',
                                                'question_id',
                                                'behavior',
                                                'variants'
                                            ])
                                                ->andWhere(['deleted_at' => null])
                                                ->orderBy(['position' => SORT_ASC]);
                                        },
                                    ]);
                            },
                        ]);
                }
            ])
            ->select([
                'id',
                'limit',
                'datetime_end',
                'active',
                'end_screenout',
                'end_screen_quotefull',
                'answers_count',
                'is_answer_limited',
                'poll_id'
            ])
            ->where(['id' => $quoteId])
            ->asArray()
            ->one();

        return $data;
    }

    /**
     * Инициализирует объект Quota данными
     *
     * @param array $data Данные квоты
     * @return Quota Инициализированный объект квоты
     */
    private static function initQuota(array $data): Quota
    {
        $quota = new Quota();
        $quota->init($data);

        return $quota;
    }

    /**
     * Сбрасывает кэш для указанной квоты
     *
     * @param int $quoteId ID квоты
     * @throws InvalidConfigException
     */
    public static function resetCache(int $quoteId): void
    {
        $cacheKey = self::CACHE_PREFIX . $quoteId;
        Yii::$app->cache->delete($cacheKey);

        $quota = QuoteService::getById($quoteId);
        $quota->updateWidgets();
    }
}
