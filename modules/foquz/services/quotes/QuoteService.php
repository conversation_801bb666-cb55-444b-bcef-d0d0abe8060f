<?php

declare(strict_types=1);

namespace app\modules\foquz\services\quotes;

use app\components\RabbitMQComponent;
use app\modules\foquz\models\FoquzPollAnswer;
use app\modules\foquz\models\FoquzPollWidget;
use app\modules\foquz\models\quotes\FoquzPollAnswerQuote;
use app\modules\foquz\models\quotes\FoquzPollAnswerQuotes;
use app\modules\foquz\models\quotes\FoquzPollLinkQuotes;
use app\modules\foquz\services\quotes\models\Quota;
use app\modules\foquz\services\quotes\models\QuotaAnswer;
use yii\base\InvalidConfigException;
use yii\db\Exception;

/**
 * Сервис для работы с квотами опросов
 *
 * Отвечает за:
 * - Управление счетчиками ответов
 * - Проверку заполненности квот
 * - Определение конечных экранов
 */
class QuoteService
{
    /** @var Quota Текущая квота */
    private Quota $quota;

    /**
     * Конструктор сервиса
     *
     * @param FoquzPollLinkQuotes|null $linkQuote Объект квоты
     * @throws InvalidConfigException
     */
    public function __construct(FoquzPollLinkQuotes $linkQuote = null)
    {
        if (!empty($linkQuote)) {
            $this->quota = QuotaCache::getOrCreate($linkQuote->id);
        }
    }

    /**
     * Создает экземпляр сервиса по ID квоты
     *
     * @param int $quoteId ID квоты
     * @return self Экземпляр сервиса
     * @throws InvalidConfigException
     */
    public static function getById(int $quoteId): self
    {
        $service = new self;
        $service->quota = QuotaCache::getOrCreate($quoteId);
        return $service;
    }

    /**
     * Обновляет квоты после удаления ответов
     *
     * Метод выполняет:
     * 1. Поиск всех указанных ответов и связанных с ними квот
     * 2. Уменьшение счетчиков ответов для соответствующих квот
     * 3. Сброс кеша для обновленных квот
     *
     * @param array $answerIds Массив ID ответов для удаления
     * @throws \yii\db\Exception При ошибках работы с базой данных
     * @throws InvalidConfigException
     */
    public static function deleteAnswers(array $answerIds): void
    {
        if (empty($answerIds)) {
            return;
        }

        $data = FoquzPollAnswer::find()->where(['a.id' => $answerIds, 'status' => FoquzPollAnswer::STATUS_DONE])
            ->alias('a')
            ->leftJoin(['fqal' => FoquzPollAnswerQuote::tableName()], 'fqal.answer_id = a.id')
            ->select([
                'quote_id'        => 'a.quote_id',
                'quote_answer_id' => 'fqal.quote_id',
                'c'               => 'count(DISTINCT a.id)',
            ])
            ->groupBy(['a.quote_id', 'fqal.quote_id'])
            ->orderBy(['a.quote_id' => SORT_ASC])
            ->asArray()->all();

        $quoteId = null;
        $sum = 0;
        foreach ($data as $item) {
            $c = $item['c'];
            $sum += $c;
            if (!empty($item['quote_answer_id'])) {
                FoquzPollAnswerQuotes::updateAllCounters(['answers_count' => -$c], ['id' => $item['quote_answer_id']]);
            }
            if (!empty($quoteId) && $item['quote_id'] !== $quoteId) {
                FoquzPollLinkQuotes::updateAllCounters(['answers_count' => -$sum], ['id' => $quoteId]);
                $s = 0;
                QuotaCache::resetCache($quoteId);
            }
            $quoteId = $item['quote_id'];
        }

        if (!empty($quoteId)) {
            FoquzPollLinkQuotes::updateAllCounters(['answers_count' => -$sum], ['id' => $quoteId]);
            QuotaCache::resetCache($quoteId);
        }
    }

    /**
     * Получает ID квоты по ответам
     *
     * @param int $answerId ID ответа
     * @return int|null ID квоты или null если не найдена
     */
    private function getAnswerQuota(int $answerId): ?int
    {
        $result = FoquzPollAnswerQuote::find()
            ->where(['answer_id' => $answerId])
            ->select('quote_id')
            ->scalar();
        return $result ?: null;
    }

    /**
     * Увеличивает счетчики ответов
     *
     * @param FoquzPollAnswer $answer Объект ответа
     * @throws InvalidConfigException
     */
    public function incrementCounters(FoquzPollAnswer $answer): void
    {
        FoquzPollLinkQuotes::updateAllCounters(['answers_count' => 1], ['id' => $answer->quote_id]);
        QuotaCache::incrementCounter($answer->quote_id);

        $quoteId = $this->getAnswerQuota($answer->id);
        if (!empty($quoteId)) {
            FoquzPollAnswerQuotes::updateAllCounters(['answers_count' => 1], ['id' => $quoteId]);
            QuotaCache::incrementCounter($answer->quote_id, $quoteId);
        }

        $this->updateWidgets(true);
    }

    /**
     * Обновляет статусы квот для связанных виджетов через RabbitMQ
     *
     * @param bool $onlyIfFull Обновлять только если квота заполнена
     * @return void
     * @throws InvalidConfigException Если компонент RabbitMQ не настроен
     */
    public function updateWidgets(bool $onlyIfFull = false): void
    {
        if (!isset(\Yii::$app->rabbit)) {
            return;
        }
        /** @var  $rabbit RabbitMQComponent */
        $rabbit = \Yii::$app->rabbit;

        $isQuotaFull = $this->isQuotaLinkFull();
        if (!$isQuotaFull && $onlyIfFull) {
            return;
        }

        $widgets = FoquzPollWidget::find()->where(['poll_link_id' => $this->quota->id])
            ->select(['id'])
            ->column();
        if (empty($widgets)) {
            return;
        }

        $data = [
            'is_quota_full' => $isQuotaFull,
            'quota_end'     => $this->getQuotaEnd(),
            'widgets_ids'   => $widgets
        ];
        $rabbit->queue('widget.poll_settings')
            ->type('upsert_quota_status')
            ->push($data);
        $data['message'] = 'Обновление статусов квот для виджетов';
        $data['counter'] = QuotaCache::getCounter($this->quota->id);
        $data['limit'] = $this->quota->limit;
        \Yii::info($data, 'app\widgets');
    }

    /**
     * Получает дату окончания действия квоты
     *
     * @return string|null Дата окончания в формате Y-m-d H:i:s или null
     */
    public function getQuotaEnd(): ?string
    {
        return $this->quota->datetimeEnd;
    }

    /**
     * Проверяет заполнена ли основная квота или все квоты по ответам
     *
     * @return bool true если квота(ы) заполнена(ы)
     */
    public function isQuotaLinkFull(): bool
    {
        return !$this->checkQuota() || !$this->checkSumQuotaAnswer();
    }

    /**
     * Находит квоту для ответа
     *
     * @param int $answerId ID ответа
     * @param array $answers Массив ответов
     * @return array [
     *     ID квоты по ответам или null,
     *     ID экрана завершения или null,
     *     Статус ответа или null
     * ]
     * @throws Exception
     */
    public function findAnswerQuota(int $answerId, array $answers): array
    {
        if (empty($this->quota->answerQuotes)) {
            return [null, null, null];
        }

        $quotaId = $this->quota->findAnswerQuota($answerId, $answers);

        //квота еще не определена
        if (is_null($quotaId)) {
            return [null, null, null];
        }

        //нашлась квота
        if ($quotaId) {
            $link = new FoquzPollAnswerQuote();
            $link->answer_id = $answerId;
            $link->quote_id = $quotaId;
            $link->save(false);
            return [$quotaId, $this->quota->answerQuotes[$quotaId]->endScreenDone, null];
        }

        //screen out
        return [null, $this->quota->endScreenout ?: 0, FoquzPollAnswer::STATUS_SCREEN_OUT];
    }

    /**
     * Проверяет заполненность квоты
     *
     * @param FoquzPollAnswer $answer Объект ответа
     * @param int|null $quotaId ID квоты (если известен)
     * @return array [
     *     ID квоты или null,
     *     ID экрана завершения или null,
     *     Статус ответа или null
     * ]
     */
    public function checkQuotaFull(
        FoquzPollAnswer $answer,
        ?int $quotaId = null,
        $openAnswer = false,
        $isNewAnswer = false
    ): array {
        //если новая анкета, то запрос не нужен
        if (!$isNewAnswer && empty($quotaId)) {
            $quotaId = $this->getAnswerQuota($answer->id);
        }
        if (!empty($quotaId)) {
            $quotaAnswer = $this->quota->answerQuotes[$quotaId] ?? null;
        }

        if (!$this->checkQuota()) {
            return [$this->quota->endScreenQuotefull, FoquzPollAnswer::STATUS_QUOTE_FULL, $quotaAnswer->id ?? null];
        }

        if (!$this->checkQuotaDate()) {
            return [
                $openAnswer && empty($this->quota->endScreenQuotefull) ? self::END_SCREEN_DATE : $this->quota->endScreenQuotefull,
                FoquzPollAnswer::STATUS_QUOTE_FULL,
                $quotaAnswer->id ?? null
            ];
        }

        if (empty($quotaAnswer) && !$this->checkSumQuotaAnswer()) {
            return [$this->quota->endScreenQuotefull, FoquzPollAnswer::STATUS_QUOTE_FULL, null];
        }

        if (!empty($quotaAnswer) && !$this->checkQuotaAnswer($quotaAnswer)) {
            return [$quotaAnswer->endScreen ?: null, FoquzPollAnswer::STATUS_QUOTE_FULL, $quotaAnswer->id];
        }

        //квоты остались
        if (!empty($quotaAnswer)) {
            return [$quotaAnswer->endScreenDone, null, $quotaAnswer->id];
        }

        return [null, null, null];
    }

    /**
     * Получает ID конечного экрана для ответа
     *
     * @param FoquzPollAnswer $answer Объект ответа
     * @return int|null ID экрана или null
     */
    public function getEndScreen(FoquzPollAnswer $answer): ?int
    {
        if ($answer->status == FoquzPollAnswer::STATUS_QUOTE_FULL) {
            $quotaId = $this->getAnswerQuota($answer->id);
            if (!empty($quotaId)) {
                return $this->quota->answerQuotes[$quotaId]->endScreen ?: 0;
            }
            return $this->checkQuotaDate() ? self::END_SCREEN_DATE : $this->quota->endScreenQuotefull;
        } else {
            if ($answer->status == FoquzPollAnswer::STATUS_SCREEN_OUT) {
                return $this->quota->endScreenout ?: 0;
            } else {
                if ($answer->status == FoquzPollAnswer::STATUS_DONE) {
                    $quotaId = $this->getAnswerQuota($answer->id);
                    return empty($quotaId) ? null : $this->quota->answerQuotes[$quotaId]?->endScreenDone;
                }
            }
        }
        return null;
    }

    /**
     * Специальное значение для обозначения экрана "Квота закрыта по дате"
     * @const int
     */
    const END_SCREEN_DATE = -1;

    private function checkQuotaDate(): bool
    {
        if ($this->quota->datetimeEnd && $this->quota->datetimeEnd <= date("Y-m-d H:i:s")) {
            return false;
        }
        return true;
    }


    /**
     * Проверяет доступность основной квоты
     *
     * @return bool true если квота активна и не превышен лимит ответов
     */
    private function checkQuota(): bool
    {
        if (!$this->quota->active) {
            return false;
        }

        if (!is_null($this->quota->limit) && QuotaCache::getCounter($this->quota->id) >= $this->quota->limit) {
            return false;
        }
        return true;
    }

    /**
     * Проверяет наличие хотя бы одной доступной квоты по ответам
     *
     * @return bool true если есть хотя бы одна квота с доступными ответами
     */
    private function checkSumQuotaAnswer(): bool
    {
        if (empty($this->quota->answerQuotes)) {
            return true;
        }

        foreach ($this->quota->answerQuotes as $answerQuota) {
            if (is_null($answerQuota->answersLimit)) {
                //вероятно надо поправить после правки на фронте / сейчас 0==не ограничено
                return true;
            }
            if (QuotaCache::getCounter($this->quota->id, $answerQuota->id) < $answerQuota->answersLimit) {
                return true;
            }
        }

        return false;
    }

    /**
     * Проверяет заполненность конкретной квоты по ответам
     *
     * @param QuotaAnswer $quotaAnswer Объект квоты по ответам
     * @return bool true если квота доступна
     */
    private function checkQuotaAnswer(QuotaAnswer $quotaAnswer): bool
    {
        if ($quotaAnswer->answersLimit === 0) {
            return false;
        }
        return empty($quotaAnswer->answersLimit) || QuotaCache::getCounter($this->quota->id,
                $quotaAnswer->id) < $quotaAnswer->answersLimit;
    }

    /**
     * Проверяет активна ли квота
     *
     * @return bool true если активна
     */
    public function isActive(): bool
    {
        return $this->quota->active;
    }

    public function getRestrictedQuestionIds(): array
    {
        $result = [];
        foreach ($this->quota->answerQuotes as $answerQuota) {
            foreach ($answerQuota->criteria as $criterion) {
                $result[] = $criterion->questionId;
            }
            foreach ($answerQuota->groups as $group) {
                foreach ($group->criteria as $criterion) {
                    $result[] = $criterion->questionId;
                }
            }
        }
        return array_values(array_unique($result));
    }

}
