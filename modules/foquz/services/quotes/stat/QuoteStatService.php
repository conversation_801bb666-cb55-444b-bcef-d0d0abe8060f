<?php

namespace app\modules\foquz\services\quotes\stat;

use app\helpers\DateTimeHelper;
use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\quotes\FoquzPollLinkQuotes;
use yii\db\ActiveQuery;
use yii\web\NotFoundHttpException;

class QuoteStatService
{
    private FoquzPoll $poll;
    private array $summary = [
        'total' => 0,
        'in_process' => 0,
        'done' => 0,
    ];

    public const IN_PROCESS = 'В процессе';
    public const DONE = 'Выполнена';

    public static function getInstance(int $pollId): QuoteStatService
    {
        $poll = FoquzPoll::findOne($pollId);
        if (is_null($poll) || $poll->deleted) {
            throw new NotFoundHttpException("Опрос не существует");
        }

        $service = new self();
        $service->poll = $poll;

        return $service;
    }


    public function stat(): array
    {
        $ret = [];
        /** @var array $quote */
        foreach ($this->getQuery()->asArray()->all() as $quote) {
            $items = [];
            // для ссылки квоты не настроены
            if (is_null($quote['limit']) && is_null($quote['datetime_end']) && !count($quote['answerQuotes'])) {
                continue;
            }

            $statusQuote = self::IN_PROCESS;
            if ($this->isQuoteDone($quote)) {
                $statusQuote = self::DONE;
            }

            if (!is_null($quote['limit']) || !is_null($quote['datetime_end'])) {
                $this->updateTotal($statusQuote);
            }

            $allAnswerQuotesIsDone = count($quote['answerQuotes']) > 0;
            foreach ($quote['answerQuotes'] as $answerQuote) {
                $groupsCount = $answerQuote['foquzPollAnswerQuotesGroups'][0]['count'] ?? 0;
                $detailCount = $answerQuote['foquzPollAnswerQuotesDetails'][0] ['count'] ?? 0;
                $statusAnswerQuote = self::IN_PROCESS;

                if ($this->isAnswerQuoteDone($answerQuote)) {
                    $statusAnswerQuote = self::DONE;
                } else {
                    $allAnswerQuotesIsDone = false;
                }

                $tooltipAnswerQuote = '';
                if ($statusQuote === self::DONE) {
                    $statusAnswerQuote = self::DONE;
                    $tooltipAnswerQuote = 'Квота ' . $answerQuote['name'] . ' по ответам выполнена, так как выполнена квота по ссылкам';
                }

                $this->updateTotal($statusAnswerQuote);

                $items[] = [
                    'id' => $answerQuote['id'],
                    'name' => $answerQuote['name'],
                    'conditions_count' => $groupsCount + $detailCount,
                    'status' => $statusAnswerQuote,
                    'status_tooltip' => $tooltipAnswerQuote,
                    'limit' => $answerQuote['answers_limit'],
                    'collect' => $answerQuote['answers_count'],
                    'last_answer_date' => $answerQuote['lastAnswer']['answer']['created_at'] ?? null,
                ];
            }

            $tooltipQuote = '';
            if ($allAnswerQuotesIsDone) {
                $statusQuote = self::DONE;
                $tooltipQuote = 'Квота по ссылкам выполнена, так как выполнены все квоты по ответам';
            }

            $ret[] = [
                'id' => $quote['id'],
                'name' => $quote['name'],
                'status' => $statusQuote,
                'status_tooltip' => $tooltipQuote,
                'datetime_end' => DateTimeHelper::formatISO8601($quote['datetime_end']),
                'active' => $quote['active'],
                'collect' => $quote['answers_count'],
                'limit' => $quote['limit'],
                'last_answer_date' => $quote['lastAnswer']['created_at'] ?? null,
                'items' => $items,
            ];
        }

        return $ret;
    }

    public function summary(): array
    {
        return $this->summary;
    }


    private function getQuery(): ActiveQuery
    {
        return FoquzPollLinkQuotes::find()
            ->with([
                'answerQuotes' => function ($query) {
                    $query->select([
                        'id',
                        'name',
                        'link_quote_id',
                        'answers_limit',
                        'answers_count',
                    ])->where(['deleted_at' => null])
                        ->with([
                            // Отдельные условия
                            'foquzPollAnswerQuotesDetails' => function ($query) {

                                $query->select([
                                    'quote_id',
                                    'COUNT(*) as count'
                                ])
                                    ->andWhere(['deleted_at' => null, 'group_id' => null])
                                    ->groupBy(['quote_id'])
                                ;
                            },
                            // Группы условий
                            'foquzPollAnswerQuotesGroups' => function ($query) {
                                $query->select([
                                    'quote_id',
                                    'COUNT(*) as count'
                                ])
                                    ->andWhere(['deleted_at' => null])
                                    ->groupBy(['quote_id']);
                            },
                            'lastAnswer' => function ($query) {
                                $query->with([
                                    'answer' => function ($query) {
                                        $query->select(['id', 'created_at']);
                                    }
                                ]);
                            },
                        ]);
                },
                'lastAnswer',
            ])->select([
                'id',
                'name',
                'limit',
                'datetime_end',
                'active',
                'answers_count',
                'is_answer_limited',
            ])
        ->where((['poll_id' => $this->poll->id]));
    }

    private function isQuoteDone(array $quote): bool
    {
        $timeOut = false;
        if (!is_null($quote['datetime_end'])) {
            $timeOut = time() > strtotime($quote['datetime_end']);
        }
        if ($quote['limit'] === 0) {
            return true;
        }
        if (($quote['limit'] && ($quote['answers_count'] >= $quote['limit'])) || $timeOut) {
            return true;
        }
        return false;
    }

    private function isAnswerQuoteDone(array $answerQuote): bool
    {
        if (!is_null($answerQuote['answers_limit']) && ($answerQuote['answers_count'] >= $answerQuote['answers_limit'])) {
            return true;
        }
        return false;
    }

    private function updateTotal(string $status): void
    {
        $this->summary['total']++;
        if ($status === self::IN_PROCESS) {
            $this->summary['in_process']++;
        } else {
            $this->summary['done']++;
        }
    }
}