<?php

namespace app\modules\foquz\services;

use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\FoquzPollStatsLink;
use app\modules\foquz\services\answers\search\query\AnswerSearchQueryBuilder;
use app\modules\foquz\services\answers\stat\AnswerStatService;
use yii\web\BadRequestHttpException;
use yii\web\ForbiddenHttpException;
use yii\web\NotFoundHttpException;

class ExternalLinkService
{
    public FoquzPoll $poll;

    /**
     * Получить экземпляр по внешней ссылки.
     * Можно передать id опроса и id компании для дополнительной проверки доступа.
     *
     * @param string $linkKey
     * @param int|null $pollId
     * @param int|null $companyId
     * @return ExternalLinkService
     * @throws BadRequestHttpException
     * @throws ForbiddenHttpException
     * @throws NotFoundHttpException
     */
    public static function checkLink(
        string $linkKey,
        ?int $pollId = null,
        ?int $companyId = null
    ): self {
        if (strlen($linkKey) !== 32) {
            throw new BadRequestHttpException('Некорректная ссылка');
        }

        $linkPoll = FoquzPollStatsLink::find()
            ->select('foquz_poll_id, link')
            ->where(['right_level' => FoquzPollStatsLink::RIGHT_READ])
            ->andWhere(['like', 'link', '/stats/' . $linkKey])
            ->asArray()->one();

        if (!$linkPoll) {
            throw new NotFoundHttpException('Опрос не найден');
        }
        /** @var FoquzPoll|null $poll */
        $poll = FoquzPoll::find()->with(['statsLink' => function ($query) use ($linkPoll) {
            $query->where(['link' => $linkPoll['link']]);
        }])->where(['id' => $linkPoll['foquz_poll_id']])->one();

        if (empty($poll) || $poll->deleted) {
            throw new NotFoundHttpException('Опрос не найден');
        }

        if (!is_null($pollId) && $poll->id != $pollId) {
            throw new ForbiddenHttpException('Доступ запрещен');
        }
        if (!is_null($companyId) && $poll->company_id != $companyId) {
            throw new ForbiddenHttpException('Доступ запрещен');
        }

        $service = new self();
        $service->poll = $poll;

        return $service;
    }

}