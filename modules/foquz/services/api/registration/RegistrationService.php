<?php

declare(strict_types=1);


namespace app\modules\foquz\services\api\registration;

use app\components\AmoCrmComponent;
use app\components\UtmService;
use app\models\company\Company;
use app\models\company\CompanyStaff;
use app\models\User;
use app\modules\foquz\helpers\MailHelper;
use app\modules\foquz\models\FoquzContact;
use app\modules\foquz\models\OpenRegistration;
use app\modules\foquz\queue\company\CopyTemplatesJob;
use app\modules\foquz\services\notifications\NotificationsSystemService;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Yii;
use yii\db\Exception;

class RegistrationService
{

    /**
     * @throws RegistrationException
     */
    public function validateUser(array $post): void
    {
        $email = $post['email'];

        if (!$email) {
            throw RegistrationException::make(['email' => Yii::t('main', 'Неправильно передан параметр email')]);
        }
        if (User::find()->where(['email' => $email])->exists()) {
            throw RegistrationException::make([
                'email' => Yii::t('main', 'Пользователь с таким email уже добавлен в систему')
            ]);
        }
        if (User::find()->where(['username' => $email])->exists()) {
            throw RegistrationException::make([
                'username' => Yii::t('main', 'Пользователь с таким логином уже добавлен в систему')
            ]);
        }

        $userValidateModel = new User();
        $userValidateModel->scenario = 'newUser';
        $userValidateModel->email = $email;
        $userValidateModel->username = htmlspecialchars($email);
        $userValidateModel->password = htmlspecialchars($post['password']);
        $userValidateModel->repeat_password = htmlspecialchars($post['repeatPassword']);
        if (!$userValidateModel->validate()) {
            throw RegistrationException::make($userValidateModel->getFirstErrors());
        }
        if ($post['password'] !== $post['repeatPassword']) {
            throw RegistrationException::make(['repeatPassword' => Yii::t('main', 'пароли не совпадают')]);
        }
    }

    /**
     * Создать пользователя
     * @param array $post
     * @return User
     * @throws Exception
     * @throws RegistrationException
     * @throws \Throwable
     */
    private function createUser(array $post): User
    {
        $email = $post['email'];
        $this->validateUser($post);

        $user = new User();
        $user->username = htmlspecialchars($email);
        $user->password = htmlspecialchars($post['password']);
        $user->status = 0;
        $user->email_confirmed = 0;
        $user->email = $email;
        $this->setUserData($user, $post);
        $user->status = 1;

        if (!$user->save()) {
            throw RegistrationException::make($user->errors);
        }

        return $user;
    }

    /**
     * Создать компанию
     * Компания получает имя Новая компания и сгенерированный домен
     * @param array $post
     * @return array
     * @throws Exception
     * @throws RegistrationException
     * @throws \Throwable
     */
    public function createCompany(array $post): array
    {
        $transaction = Yii::$app
            ->getDb()
            ->beginTransaction();

        try {
            $user = $this->createUser($post);
            $company = new Company();
            $company->name = 'Новая компания';
            $company->alias = $this->generateDomain();
            if (!$company->save()) {
                throw new \Exception("Ошибка создания компании");
            }
            $cstaff = new CompanyStaff([
                'user_id'    => $user->id,
                'company_id' => $company->id,
            ]);
            if (!$cstaff->save()) {
                throw new \Exception("Ошибка создания пользователя компании");
            }
            User::assignRole($user->id, User::DEFAULT_ROLE);

            $transaction->commit();

            if (!empty(\Yii::$app->params['amo_client_id'])) {
                $amo = new AmoCrmComponent();
                $companyId = $amo->createCompany($company->name,
                    \Yii::$app->params['protocol'] . '://' . $company->alias);
                if ($companyId) {
                    $contactId = $amo->createContact($user->name, $user->email, $user->phone ? $user->phone : '',
                        $companyId);
                    $amo->createLeadNew($company->name . ' ' . date("d.m.Y H:i"), 0, $companyId, $contactId,
                        UtmService::getUtmTags(), 'Новая регистрация');
                }
            }


            // копирование шаблонов для новой компании
            // @phpstan-ignore-next-line

            /*Yii::$app->rabbit_queue->push(new CopyTemplatesJob([
                'company_id' => $company->id,
                'company_user_id' => $user->id,
            ]));*/

            Yii::$app->consoleRunner->run(
                "company/copy-templates "
                . $company->id
                . " "
                . $user->id
            );

            return [
                'user'          => $user,
                'company_alias' => $company->alias,
                'company_name'  => $company->name,
            ];

        } catch (RegistrationException $e) {
            $transaction->rollBack();
            throw $e;
        } catch (\Throwable $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    /**
     * @param User $user
     * @param array $post
     * @return void
     * @throws RegistrationException
     */
    private function setUserData(User $user, array $post): void
    {
        if (empty($post['phone'])) {
            throw RegistrationException::make(['phone' => Yii::t('main', 'Не указан номер телефона')]);
        }
        $phone = FoquzContact::preformatPhone($post['phone']);
        if (strlen($phone) < 11) {
            throw RegistrationException::make([
                'phone' => Yii::t('main', 'Неправильный номер телефона, минимум 11 цифр')
            ]);
        }
        $user->phone = FoquzContact::preformatPhone($phone);
        if (!empty($post['name'])) {
            $user->name = htmlspecialchars($post['name']);
        }
    }

    /**
     * Делаем токен для подтверждения email
     * @param User $user
     * @return OpenRegistration
     * @throws Exception
     */
    public function openRegistration(User $user): OpenRegistration
    {
        $model = new OpenRegistration();
        $model->email = htmlspecialchars($user->email);
        if (!$model->save()) {
            throw new \Exception(Yii::t('main', 'Ошибка сохрания данных'));
        }
        return $model;
    }

    /**
     * @param User $user
     * @param OpenRegistration $openRegistration
     * @return void
     * @throws Exception
     */
    public function sendNewUserEmail(User $user, OpenRegistration $openRegistration, ?FoquzContact $foquzContact): void
    {
        if (!empty(\Yii::$app->queue_mailings_out)) {
            $service = new NotificationsSystemService(\Yii::$app->queue_mailings_out);
            $service->sendToSalesAfterRegistration($user);
            $service->sendToUserAfterRegistration($user, $openRegistration->code, $foquzContact);
        }


    }


    /**
     * @param OpenRegistration $openRegistration
     * @return User|null
     * @throws Exception
     */
    public function confirmEmail(OpenRegistration $openRegistration): ?User
    {
        $openRegistration->is_used = true;
        $openRegistration->save();

        $user = User::findOne(['email' => $openRegistration->email]);
        if ($user) {
            $user->email_confirmed = 1;
            if ($user->save()) {
                return $user;
            }
        }
        return null;
    }

    public function confirmEmailByCode(string $email, string $code): int
    {
        /** @var OpenRegistration|null $openRegistration */
        $openRegistration = OpenRegistration::find()
            ->where(['email' => $email, 'short_code' => $code])
            ->orderBy(['id' => SORT_DESC])
            ->one();

        if (!$openRegistration) {
            throw new \Exception(Yii::t('main', 'Код не найден или не действителен'));
        }

        if (strtotime($openRegistration->short_code_available_to) < time()) {
            throw new \Exception(Yii::t('main', 'Код не найден или не действителен'));
        }

        $openRegistration->is_used = true;
        $openRegistration->save();

        $user = User::findOne(['email' => $email]);
        if ($user) {
            $user->email_confirmed = 1;
            $user->save();
            return $user->email_confirmed;
        }

        return 0;
    }

    /**
     * @throws RegistrationException
     * @throws GuzzleException
     */
    public function validateCaptcha($token): void
    {
        if (Yii::$app->request->hostName === 'localhost') {
            return;
        }
        $client = new Client();
        $response = $client->request(
            'GET',
            'https://smartcaptcha.yandexcloud.net/validate',
            [
                'content-type' => 'application/json',
                'query'        => [
                    'secret' => Yii::$app->params['smart_captcha_server_key'],
                    'token'  => $token,
                    'ip'     => RegistrationHelper::getIp(),
                ]
            ]
        );

        if ($response->getStatusCode() !== 200) {
            Yii::warning(
                sprintf("Yandex smartcaptcha error: %d, %s", $response->getStatusCode(),
                    $response->getBody()->getContents())
            );
            // Из документации по smartcaptcha: Чтобы при обработке запроса от пользователя не было задержки, ошибки HTTP-протокола (код ответа не 200) рекомендуется обрабатывать как ответ сервиса "status": "ok"
            return;
        }

        $contents = $response->getBody()->getContents();
        $resp = json_decode($contents);

        if ($resp->status !== 'ok') {
            $message = (!empty($resp->message) ? $resp->message : 'robot');
            throw RegistrationException::make(['captcha' => $message]);
        }
    }

    /**
     * Генерация уникального доменного имени для компании
     * @return string
     */
    private function generateDomain(): string
    {
        $aliasExists = true;
        $alias = '';
        preg_match('/\.[a-zA-Z0-9-]+\.[a-zA-Z0-9-]+$/', Yii::$app->params['company_domen'], $matches);
        $secondLevelDomain = $matches[0];
        while ($aliasExists) {
            $alias = $this->generateRandomString() . $secondLevelDomain;
            $aliasExists = Company::find()->where(['alias' => $alias])->exists();
        }

        return $alias;
    }

    private function generateRandomString(int $len = 4): string
    {
        $permitted_chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
        $random_string = '';
        for ($i = 0; $i < $len; $i++) {
            $random_character = $permitted_chars[mt_rand(0, strlen($permitted_chars) - 1)];
            $random_string .= $random_character;
        }

        return $random_string;
    }
}