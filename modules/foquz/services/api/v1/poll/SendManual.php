<?php

namespace app\modules\foquz\services\api\v1\poll;

use app\models\Filial;
use app\modules\foquz\models\FoquzPoll;
use Yii;
use yii\web\BadRequestHttpException;
use yii\web\NotFoundHttpException;
use yii\web\Request;

class SendManual
{
    private array $contacts;
    private bool $onlyLink;
    private ?int $filialId;
    private int $companyId;
    private FoquzPoll $poll;


    /**
     * SendManual constructor.
     * @param Request $request
     * @throws BadRequestHttpException
     */
    public function __construct(Request $request, int $pollId)
    {
        $this->companyId = Yii::$app->user->identity->company->id;

        $poll = FoquzPoll::findOne(['id' => $pollId, 'company_id' => $this->companyId, 'deleted' => 0, 'is_auto' => 0]);
        if (!$poll) {
            throw new NotFoundHttpException('Опрос не найден');
        }
        $this->poll = $poll;

        $this->contacts = $request->post('contacts');
        $this->onlyLink = $request->get('onlyLink') ?? false;
        if (!$this->onlyLink) {
            $this->onlyLink = $request->post('onlyLink') ?? false;
        }

        $filialId = $request->post('filial_id') ?? null;
        $filialName = $request->post('filial') ?? null;

        if (!empty($filialId)) {
            $filial = Filial::findOne(['id' => $filialId, 'company_id' => $this->companyId]);
            if (!$filial) {
                throw new BadRequestHttpException('Филиал не найден');
            }
            $this->filialId = $filial->id;
        }
        if (empty($this->filialId) && !empty($filialName)) {
            $filial = Filial::findOne(['name' => $filialName, 'company_id' => $this->companyId]);
            if (!$filial) {
                throw new BadRequestHttpException('Филиал не найден');
            }
            $this->filialId = $filial->id;
        }


    }
}