<?php

namespace app\modules\foquz\services\export\usecase;

use app\modules\foquz\models\FoquzPollAnswerItem;
use app\modules\foquz\models\FoquzPollAnswerItemPoints;
use app\modules\foquz\models\FoquzQuestion;

class FirstClick
{
    /**
     * Ответы для первой выгрузки
     * @param array|null $detailItem
     * @param FoquzQuestion $question
     * @param array $itemPoints
     * @return string
     */
    public function getAnswerColumnExport1(array|null $detailItem, FoquzQuestion $question, array $itemPoints): string
    {
        $ret = [];
        $points = $question->firstClick->parseAnswer($itemPoints);
        foreach ($points as $point) {
            $pointArr = explode(',', $point);
            $ret[] = $pointArr[4] . ' / ' . $pointArr[2] . ' с';
        }
        if (isset($detailItem['time_expired'])) {
            $ret[] = 'Время истекло';
        }
        return implode("\n", $ret);
    }

    /**
     * Шапка таблицы для второй выгрузки
     * @param FoquzQuestion $question
     * @param array $labels
     * @param array $details
     * @return array
     */
    public function getAnswerSheetHeaderExport2(FoquzQuestion $question, array $labels, array $details): array
    {
        $areas = $question->firstClickArea;
        foreach ($areas as $area) {
            $details[] = $area->name;
        }
        $details[] = 'Пользовательские точки';
        $details[] = 'Затрудняюсь ответить';

        return ['labels' => $labels, 'details' => $details];
    }

    /**
     * Ответы для второй выгрузки
     * @param FoquzQuestion $question
     * @param FoquzPollAnswerItem|null $answerItem
     * @param array $row
     * @return array
     */
    public function getAnswerSheetRowsExport2(FoquzQuestion $question, FoquzPollAnswerItem|null $answerItem, array $row): array
    {
        $userPoints = [];
        $areas = $question->firstClickArea;
        if ($answerItem && isset($answerItem->detail_item['time_expired'])) {
            $userPoints[] = 'Время истекло';
        }

        $answers = $question->firstClick->parseAnswer($answerItem->answerItemPoints ?? []);
        $answersPart = [];
        foreach ($answers as $answer) {
            $answersPart[] = explode(',', $answer);
        }

        foreach ($areas as $area) {
            $areaFound = [];
            foreach ($answersPart as $idx => $answerPart) {
                if ((int)$answerPart[3] === 0) {
                    $userPoints[$idx] = $answerPart[4] . ' / ' . $answerPart[2] . ' с';
                    continue;
                }
                if ((int)$answerPart[3] === $area->id) {
                    $areaFound[] = $answerPart[4] . ' / ' . $answerPart[2] . ' с';
                }
            }
            $row[] = implode("\n", $areaFound);
        }
        $row[] = implode("\n", $userPoints);
        $skipText = $question->skip_text ?: 'Затрудняюсь ответить';
        $row[] = ($answerItem?->skipped) ? $skipText : '';
        return $row;
    }

    /**
     * Ответы для третьей выгрузки
     * @param FoquzQuestion $question
     * @param FoquzPollAnswerItem|null $answerItem
     * @return array
     */
    public function getAnswerSheetRowsExport3(FoquzQuestion $question, FoquzPollAnswerItem|null $answerItem): array
    {
        $result = [];
        if ($answerItem && $answerItem->skipped) {
            return [[$question->description, $question->skip_text ?: 'Затрудняюсь ответить']];
        }

        $answers = $question->firstClick->parseAnswer($answerItem->answerItemPoints ?? []);
        $answersPart = [];
        foreach ($answers as $answer) {
            $a = explode(',', $answer);
            $answersPart[] = $a[4] . ' / ' . $a[2] . ' с';
        }

        // название вопроса
        $result[0] = [$question->description];
        // ответ на вопрос
        if ($answerItem && isset($answerItem->detail_item['time_expired'])) {
            $result[0][1] = 'Время истекло';
        } else {
            $result[0][1] = implode("\n", $answersPart);
        }
        // комментарий
        if ($answerItem && $answerItem->answer) {
            $result[0][2] = $answerItem->answer;
        }
        return $result;
    }
}