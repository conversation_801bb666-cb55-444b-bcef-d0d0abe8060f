<?php

declare(strict_types=1);


namespace app\modules\foquz\services\export;

use app\models\Export;
use app\models\User;
use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\FoquzPollStatsLink;
use yii\web\BadRequestHttpException;
use yii\web\NotFoundHttpException;

class ExportHelper
{
    public static function convertEncoding(?string $value): string
    {
        return mb_convert_encoding($value, 'Windows-1251', 'UTF-8');
    }

    /**
     * Получить ID Компании пользователя
     * @throws NotFoundHttpException
     * @throws BadRequestHttpException
     */
    public static function getUserCompanyId(Export $export): int
    {
        if ($export->user_id) {
            $userCompanyID = User::findOne($export->user_id)->company->id ?? null;
        } elseif (FoquzPollStatsLink::find()
                ->where(['foquz_poll_id' => $export->params['id'], 'show_answers' => true, 'right_level' => FoquzPollStatsLink::RIGHT_READ])
                ->andWhere(['like', 'link', '/stats/' . $export->key])
                ->exists() && $poll = FoquzPoll::findOne($export->params['id'])) {
                $userCompanyID = $poll->company_id;
        } else {
            throw new BadRequestHttpException('Неверный ключ ссылки');
        }
        if (!$userCompanyID) {
            throw new NotFoundHttpException('Компания пользователя не найдена');
        }
        return $userCompanyID;
    }

    public static function updateProcess(Export $export, int $processed): void
    {
        $export->scenario = Export::SCENARIO_UPDATE_PROGRESS;
        $export->processed = min($processed, 100);
        $export->save();
        sleep(1);
    }

    /**
     * Подсчет ширин столбцов по контенту таблицы
     * @param array $data
     * @return array
     */
    public static function calculateColumnsWidth(array $data): array
    {

        $colWidths = []; $minWidthLimit = 14; $maxWidthLimit = 50;
        foreach ($data as $row) {
            foreach ($row as $colIdx => $colValue) {
                $len = (is_null($colValue)) ? 0 : mb_strlen((string)$colValue);
                if ($len >= $maxWidthLimit) {
                    $len = $maxWidthLimit;
                }
                if ($len < $minWidthLimit) {
                    $len = $minWidthLimit;
                }
                if (!isset($colWidths[$colIdx])) {
                    $colWidths[$colIdx] = $len;
                } else {
                    if (($len > $colWidths[$colIdx]) && ($len < $maxWidthLimit)) {
                        $colWidths[$colIdx] = $len;
                    }
                }
            }
        }
        return $colWidths;
    }
}