<?php

namespace app\modules\foquz\services\export\csv;

use app\models\DictionaryElement;
use app\models\Export;
use app\models\Filial;
use app\models\User;
use app\modules\foquz\models\ContactAdditionalField;
use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\FoquzPollAnswer;
use app\modules\foquz\models\FoquzPollAnswerItem;
use app\modules\foquz\models\FoquzPollStatsLink;
use app\modules\foquz\models\FoquzQuestion;
use app\modules\foquz\models\FoquzQuestionMatrixElement;
use app\modules\foquz\models\FoquzQuestionMatrixElementVariant;
use app\modules\foquz\models\SettingTables;
use app\modules\foquz\services\answers\search\AnswerSearchService;
use Yii;
use yii\helpers\ArrayHelper;
use yii\web\BadRequestHttpException;
use yii\web\NotFoundHttpException;

class AnswersAdvantageSuppliersCSVService extends BaseCSVService
{
    public const POINTS = [
        '1' => '-100',
        '2' => '-50',
        '3' => '0',
        '4' => '50',
        '5' => '100',
    ];

    /** @var string $model */
    public $model = FoquzPollAnswer::class;

    /** @var FoquzPoll $poll */
    public $poll;

    /** @var Export $export */
    public $export;

    /** @var array $columns */
    public $columns;

    /** @var array $additionFields */
    public $additionalFields = [];

    /** @var FoquzQuestion[] $questions */
    public $questions;

    /** @var FoquzQuestion[] $formQuestions */
    public $formQuestions;

    /** @var Filial[] $companyFilials */
    public $companyFilials;

    /** @var array $questionDictionary  */
    public $questionDictionary = [];

    /** @var FoquzQuestionMatrixElementVariant[] $matrixVariants */
    public $matrixVariants;

    /** @var FoquzQuestion $contactDataQuestion */
    public $contactDataQuestion;

    /** @var FoquzQuestion $stmQuestion */
    public $stmQuestion;

    /** @var FoquzQuestion $regionalityQuestion */
    public $regionalityQuestion;

    /** @var FoquzQuestion $respondentTypeQuestion */
    public $respondentTypeQuestion;

    /** @var FoquzQuestion $subcategoriesQuestion */
    public $subcategoriesQuestion;

    /** @var FoquzQuestion $assessedCompanyQuestion */
    public $assessedCompanyQuestion;

    /** @var FoquzQuestion[] $matrix3DQuestions */
    public $matrix3DQuestions = [];

    public function __construct(Export $export)
    {
        $this->export = $export;
    }

    /**
     * Формирует XLSX файл экспорта
     * @return void
     * @throws NotFoundHttpException
     * @throws BadRequestHttpException
     */
    public function process(): void
    {
        ini_set('memory_limit', '2048M');
        $export = $this->export;
        print('Processing...' . PHP_EOL);
        if (empty($export->params['id'])) {
            throw new BadRequestHttpException('Не указан ID опроса');
        }
        if ($export->user_id) {
            $userCompanyID = User::findOne($export->user_id)->company->id ?? null;
        } elseif (FoquzPollStatsLink::find()
                ->where(['foquz_poll_id' => $export->params['id'], 'show_answers' => true, 'right_level' => FoquzPollStatsLink::RIGHT_READ])
                ->andWhere(['like', 'link', '/stats/' . $export->key])
                ->exists() && $poll = FoquzPoll::findOne($export->params['id'])) {
            $userCompanyID = $poll->company_id;
        } else {
            throw new BadRequestHttpException('Неверный ключ ссылки');
        }
        if (!$userCompanyID) {
            throw new NotFoundHttpException('Компания пользователя не найдена');
        }
        $this->poll = FoquzPoll::find()->where(['id' => $export->params['id'], 'company_id' => $userCompanyID])->one();
        if (!$this->poll) {
            throw new NotFoundHttpException('Опрос не найден');
        }

        $this->entityType = Export::ENTITES[$export->entity_type];
        $this->fileType = Export::FILETYPES[$export->file_type];
        $path = Yii::getAlias('@app') . '/upload/export/' . $this->fileType . '/' . $this->entityType;
        if (!file_exists($path) && !mkdir($path, 0777, true) && !is_dir($path)) {
            throw new \RuntimeException(sprintf('Directory "%s" was not created', $path));
        }

        $export->status = Export::STATUS_PROCESSING;
        $export->save();

        foreach (ContactAdditionalField::arrayFields($this->poll->company_id)['additional'] as $field) {
            $this->additionalFields['client' . $field['id']] = $field['text'];
        }
        $allColumns = ArrayHelper::merge(SettingTables::COLUMNS, $this->additionalFields);
        $this->columns = array_filter(explode(',', $export->params['columns']), static function ($value) use ($allColumns) {
            return !in_array($value, ['points', 'comments', 'pointsCollect']) && array_key_exists($value, $allColumns);
        });

        $this->setQuestions();

        $file = $path . '/' . $export->id . '.csv';
        $array = ArrayHelper::merge([$this->getHeader()], $this->getRows());

        $export->total = count($array) - 1;
        $batchItems = array_chunk($array, 1000);
        $export->status = Export::STATUS_PROCESSING;
        $export->save();
        $count = 0;
        foreach ($batchItems as $batchItem) {
            $fp = fopen($file, 'ab');
            foreach ($batchItem as $item) {
                $item = array_map([$this, 'convertEncoding'], $item);
                fputcsv($fp, $item, ";");
            }
            fclose($fp);
            $count += count($batchItem);
            $export->processed = $count;
            $export->save();
        }
        $export->processed = $export->total;
        $export->status = Export::STATUS_DONE;
        $export->save();
    }

    /**
     * Устанавливает вопросы для экспорта
     * @return void
     */
    public function setQuestions(): void
    {
        /** @var FoquzQuestion[] $questions */
        $this->questions = $this->poll->getFoquzQuestions()
            ->with(['questionDetails', 'recipientsQuestionDetails', 'questionFiles', 'differentialRows', 'matrixElements', 'matrixElements'])
            ->andWhere(['NOT IN', 'main_question_type', [FoquzQuestion::TYPE_FORM, FoquzQuestion::TYPE_INTERMEDIATE_BLOCK]])
            ->all();

        /** @var FoquzQuestion[] $formQuestions */
        $this->formQuestions = $this->poll->getFoquzQuestions()
            ->with('formFields')
            ->andWhere(['main_question_type' => FoquzQuestion::TYPE_FORM])
            ->all();

        $this->companyFilials = ArrayHelper::index(Filial::find()->where(['company_id' => $this->poll->company_id])->all(), 'id');

        foreach ($this->questions as $key => $question) {
            if (
                !$this->stmQuestion &&
                $question->main_question_type === FoquzQuestion::TYPE_VARIANTS &&
                $question->service_name === 'Бренды/СТМ'
            ) {
                $this->stmQuestion = $question;
                unset($this->questions[$key]);
            }
            if (
                !$this->regionalityQuestion &&
                $question->main_question_type === FoquzQuestion::TYPE_VARIANTS &&
                $question->service_name === 'Региональность'
            ) {
                $this->regionalityQuestion = $question;
                unset($this->questions[$key]);
            }
            if (
                !$this->respondentTypeQuestion &&
                $question->main_question_type === FoquzQuestion::TYPE_VARIANTS &&
                $question->service_name === 'Тип респондента'
            ) {
                $this->respondentTypeQuestion = $question;
                unset($this->questions[$key]);
            }
            if (
                !$this->subcategoriesQuestion &&
                $question->main_question_type === FoquzQuestion::TYPE_DICTIONARY &&
                $question->service_name === 'Категории'
            ) {
                $this->subcategoriesQuestion = $question;
                unset($this->questions[$key]);
            }
            if (
                !$this->assessedCompanyQuestion &&
                $question->service_name === 'Выбор поставщиков' &&
                in_array($question->main_question_type, [FoquzQuestion::TYPE_DICTIONARY, FoquzQuestion::TYPE_VARIANTS], true)
            ) {
                $this->assessedCompanyQuestion = $question;
                unset($this->questions[$key]);
            }
            if ($question->main_question_type === FoquzQuestion::TYPE_3D_MATRIX) {
                $this->matrix3DQuestions[] = $question;
                unset($this->questions[$key]);
            }
        }

        foreach ($this->formQuestions as $key => $question) {
            if ($question->service_name === 'Контактные данные') {
                $this->contactDataQuestion = $question;
                unset($this->formQuestions[$key]);
                break;
            }
        }
    }

    /**
     * Возвращает заголовок выгрузки
     * @return array
     */
    public function getHeader(): array
    {
        return [
            'Статус',
            'Дата',
            'Код анкеты',
            'Код респондента',
            'Имя',
            'Фамилия',
            'EMAIL',
            'Телефон',
            'Должность',
            'Бренды/СТМ',
            'Региональность',
            'Тип респондента',
            'Код оценивающей компании',
            'Оценивающая компания',
            'Категория, которую выбрал ритейлер',
            'Оцениваемая компания',
            'Функциональные композиты',
            'Критерий',
            'Оценка',
            'Баллы',
        ];
    }


    /**
     * Возвращает строки выгрузки
     * @return array
     */
    public function getRows(): array
    {
        $export = $this->export;

        $searchService = AnswerSearchService::getInstanceByCompany($this->poll->company_id);
        $searchService->applyParams($export->params);
        $data = ArrayHelper::getColumn($searchService->all(), "id");

        /** @var FoquzPollAnswer[] $answers */
        $answers = FoquzPollAnswer::find()
            ->where(['id' => $data])
            ->with([
                'foquzPoll', 'answerChannel', 'pollLang', 'foquzAnswer',
                'answerFilial', 'order', 'contact', 'processing',
            ])
            ->all();

        $answers = ArrayHelper::index($answers, 'id');
        $sortedAnswers = [];
        foreach ($data as $answer) {
            if (!isset($answers[$answer])) {
                continue;
            }
            $sortedAnswers[] = $answers[$answer];
        }

        $rows = [];
        foreach ($sortedAnswers as $key => $answer) {
            $answerItems = ArrayHelper::index($answer->foquzAnswer, 'foquz_question_id');

            if (!$this->isHave3DAnswers($answerItems)) {
                continue;
            }

            $infoColumns =  $this->getInfoColumns($answer, $answerItems);
            $questionColumns = $this->getQuestionsAnswers($answerItems);
            if (empty($questionColumns)) {
                return [$infoColumns];
            }
            foreach ($questionColumns as $questionColumn) {
                $rows[] = ArrayHelper::merge($infoColumns, $questionColumn);
            }
        }
        return $rows;
    }

    /**
     * Возвращает столбцы с информацией о респонденте
     * @param FoquzPollAnswer $answer
     * @param array $answerItems
     * @return array
     */
    public function getInfoColumns(FoquzPollAnswer $answer, array $answerItems): array
    {
        $kHash = $answer->contact_id ? $answer->contact_id % 100 : 101;
        if ($kHash == 0) $kHash = 101;

        $columns = [
            $answer->getAnswerValue('status'),
            $answer->getAnswerValue('passedAt'),
            $answer->getAnswerValue('id'),
            $answer->contact_id ? ($answer->contact->created_at - 1557158285) * $kHash : '',
        ];

        $columns = ArrayHelper::merge($columns, $this->getContactData($answerItems));

        if ($this->stmQuestion && !empty($answerItems[$this->stmQuestion->id])) {
            $columns[] = $this->getVariantAnswer($this->stmQuestion, $answerItems[$this->stmQuestion->id], [])[0][1] ?? '';
        } else {
            $columns[] = '';
        }

        if ($this->regionalityQuestion && !empty($answerItems[$this->regionalityQuestion->id])) {
            $columns[] = $this->getVariantAnswer($this->regionalityQuestion, $answerItems[$this->regionalityQuestion->id], [])[0][1] ?? '';
        } else {
            $columns[] = '';
        }

        if ($this->respondentTypeQuestion && !empty($answerItems[$this->respondentTypeQuestion->id])) {
            $columns[] = $this->getVariantAnswer($this->respondentTypeQuestion, $answerItems[$this->respondentTypeQuestion->id], [])[0][1] ?? '';
        } else {
            $columns[] = '';
        }

        if ($answer->answer_filial_id) {
            $columns[] = $this->companyFilials[$answer->answer_filial_id]->param1 ?? '';
            $columns[] = $this->companyFilials[$answer->answer_filial_id]->name ?? '';
        } else {
            $columns[] = '';
            $columns[] = '';
        }

        if ($this->subcategoriesQuestion && !empty($answerItems[$this->subcategoriesQuestion->id])) {
            $columns[] = $this->getDictionaryAnswer($this->subcategoriesQuestion, $answerItems[$this->subcategoriesQuestion->id], ', ')[0][1] ?? '';
        } else {
            $columns[] = '';
        }

        return $columns;
    }

    /**
     * Возвращает ответ на вопрос типов Варианты ответа и приоритет
     * @param FoquzQuestion $question
     * @param FoquzPollAnswerItem $answerItem
     * @param array $answerItems
     * @return array[]
     */
    public function getVariantAnswer(FoquzQuestion $question, FoquzPollAnswerItem $answerItem, array $answerItems): array
    {
        if ($answerItem->skipped) {
            return [[$question->description, $question->skip_text ?: 'Затрудняюсь ответить']];
        }
        if (is_array($answerItem->detail_item)) {
            $answerArray = $answerItem->detail_item;
        } else {
            $answerArray = json_decode($answerItem->detail_item, true) ?? [];
        }
        if (!$question->donor) {
            $variants = ArrayHelper::map($question->questionDetails, 'id', 'question');
        } elseif ($question->getMainDonor()->main_question_type === FoquzQuestion::TYPE_DICTIONARY) {
            $variants = ArrayHelper::map(DictionaryElement::findAll(array_values($answerArray)), 'id', 'fullPath');
        } else {
            $variants = ArrayHelper::map($question->recipientsQuestionDetails, 'id', 'question');
        }
        $selectedVariants = [];
        foreach ($answerArray as $item) {
            $variantName = $variants[$item] ?? '';
            if ($item === '-1') {
                /** @var FoquzPollAnswerItem $donorAnswerItem */
                $donorAnswerItem = $answerItems[$question->donor] ?? null;
                $variantName = $donorAnswerItem->self_variant ?? '';
            }
            $selectedVariants[] = $variantName;
        }
        if (!empty($answerItem->self_variant)) {
            $selectedVariants[] = $answerItem->self_variant;
        }
        return [[$question->description, implode(';', $selectedVariants)]];
    }

    /**
     * Возвращает ответ на вопрос типа Классификатор
     * @param FoquzQuestion $question
     * @param FoquzPollAnswerItem $answerItem
     * @param string $separator
     * @return array[]
     */
    public function getDictionaryAnswer(FoquzQuestion $question, FoquzPollAnswerItem $answerItem, string $separator = ';'): array
    {
        if ($answerItem->skipped) {
            return [[$question->description, $question->skip_text ?: 'Не готов(а) оценить']];
        }
        $selectedIDs = $answerItem->detail_item ?? [];
        if (isset($this->questionDictionary[$question->dictionary_id])) {
            $dictionary = $this->questionDictionary[$question->dictionary_id];
        } else {
            /** @var DictionaryElement[] $dictionary */
            $dictionary = ArrayHelper::index(DictionaryElement::find()->where(['dictionary_id' => $question->dictionary_id])->all(), 'id');;
            $this->questionDictionary[$question->dictionary_id] = $dictionary;
        }
        $result = [];
        foreach ($selectedIDs as $selectedID) {
            $result[] = $dictionary[$selectedID]->title ?? '';
        }
        return [[$question->description, implode($separator, $result)]];
    }

    /**
     * Возвращает столбцы с контактными данными респондента
     * @param array $answerItems
     * @return array
     */
    public function getContactData(array $answerItems): array
    {
        if ($this->contactDataQuestion) {
            $answerItem = $answerItems[$this->contactDataQuestion->id] ?? null;
            if ($answerItem) {
                $answer = json_decode($answerItem->answer, true);
                foreach ($this->contactDataQuestion->formFields as $field) {
                    if (empty($surname) && empty($name) && $field->mask_type === FoquzQuestion::MASK_NAME) {
                        $surname = $answer[$field->id]['surname'] ?? '';
                        $name = $answer[$field->id]['name'] ?? '';
                    }
                    if (empty($email) && $field->mask_type === FoquzQuestion::MASK_EMAIL) {
                        $email = $answer[$field->id] ?? '';
                    }
                    if (empty($phone) && $field->mask_type === FoquzQuestion::MASK_PHONE) {
                        $phone = $answer[$field->id] ?? '';
                    }
                    if (empty($position) && $field->name === 'Должность') {
                        $position = $answer[$field->id] ?? '';
                    }
                }
            }
        }
        return [
            $name ?? '',
            $surname ?? '',
            $email ?? '',
            $phone ?? '',
            $position ?? '',
        ];
    }

    /**
     * Возвращает столбцы с ответами на вопросы
     * @param array $answerItems
     * @return array
     */
    public function getQuestionsAnswers(array $answerItems): array
    {
        $rows = [];
        if ((!$this->assessedCompanyQuestion || empty($answerItems[$this->assessedCompanyQuestion->id])) && empty($this->matrix3DQuestions)) {
            return [['','','','','']];
        }
        if ($this->assessedCompanyQuestion && !empty($answerItems[$this->assessedCompanyQuestion->id])) {
            /** @var FoquzPollAnswerItem $answerItem */
            $answerItem = $answerItems[$this->assessedCompanyQuestion->id];
            $selectedCompanies = $answerItem->detail_item;
            if (is_string($selectedCompanies)) {
                $selectedCompanies = json_decode($selectedCompanies, true) ?? [];
            }
        }
        if (empty($this->matrix3DQuestions)) {
            if ($this->assessedCompanyQuestion->main_question_type === FoquzQuestion::TYPE_DICTIONARY) {
                $companies = $this->getDictionaryAnswer($this->assessedCompanyQuestion, $answerItem)[0][1] ?? '';
            } else {
                $companies = $this->getVariantAnswer($this->assessedCompanyQuestion, $answerItem, [])[0][1] ?? '';
            }
            $companies = explode(';', $companies);
            if (empty($companies)) {
                return [['','','','','']];
            }
            foreach ($companies as $company) {
                $rows[] = [
                    $company,
                    '',
                    '',
                    '',
                    '',
                ];
            }
            return $rows;
        }
        foreach ($this->matrix3DQuestions as $question) {
            $answer3D =  json_decode($answerItems[$question->id]->answer ?? '', true) ?? [];
            if (empty($answer3D)) {
                continue;
            }
            $skipped = $answerItems[$question->id]->skipped ?? 0;
            $matrixElements = ArrayHelper::index($question->matrixElements, null, 'type_id');
            /** @var FoquzQuestionMatrixElement[] $columns */
            $columns = !empty($matrixElements[FoquzQuestionMatrixElement::TYPE_COLUMN]) ?
                ArrayHelper::index($matrixElements[FoquzQuestionMatrixElement::TYPE_COLUMN], 'id') : [];
            /** @var FoquzQuestionMatrixElement[] $mRows */
            $mRows = !empty($matrixElements[FoquzQuestionMatrixElement::TYPE_ROW]) ?
                ArrayHelper::index($matrixElements[FoquzQuestionMatrixElement::TYPE_ROW], 'id') : [];
            if (!empty($this->matrixVariants[$question->id])) {
                $matrixVariants = $this->matrixVariants[$question->id];
            } else {
                $matrixVariants = FoquzQuestionMatrixElementVariant::find()
                    ->where(['matrix_element_id' => ArrayHelper::getColumn($columns, 'id')])
                    ->all();
                $this->matrixVariants[$question->id] = ArrayHelper::index($matrixVariants, 'id');
            }
            /** @var FoquzQuestionMatrixElementVariant[] $matrixVariants */
            $matrixVariants = ArrayHelper::index($matrixVariants, 'id');
            foreach ($mRows as $mRow) {
                if (
                    (
                        $this->assessedCompanyQuestion &&
                        $this->assessedCompanyQuestion->main_question_type === FoquzQuestion::TYPE_DICTIONARY &&
                        $question->donor_rows &&
                        !in_array($mRow->donor_dictionary_element_id, $selectedCompanies)
                    ) || (
                        $this->assessedCompanyQuestion &&
                        $this->assessedCompanyQuestion->main_question_type === FoquzQuestion::TYPE_VARIANTS &&
                        $question->donor_rows &&
                        !in_array($mRow->donor_variant_id, $selectedCompanies)
                    )
                ) {
                    continue;
                }
                $mRow->name = strpos($mRow->name, ' / ') === false ? $mRow->name : mb_substr(mb_strrchr($mRow->name, ' / '), 2);
                foreach ($columns as $column) {
                    $column->name = strpos($column->name, ' / ') === false ? $column->name : mb_substr(mb_strrchr($column->name, ' / '), 2);
                    $answerValue = $answer3D[$mRow->id][$column->id] ?? '';
                    if (empty($answerValue)) {
                        continue;
                    }
                    if (is_array($answerValue)) {
                        $answerValue = array_map(static function ($value) use ($matrixVariants, $question) {
                            if ($value === '-1') {
                                return $question->skip_text ?: 'Затрудняюсь ответить';
                            }
                            return $matrixVariants[$value]->name ?? '';
                        }, $answerValue);
                        $answerValue = implode(';', $answerValue);
                    } elseif ($skipped) {
                        $answerValue = $question->skip_text ?: 'Затрудняюсь ответить';
                    }
                    $points = self::POINTS[$answerValue] ?? '';

                    $rows[$mRow->name][] = [
                        $mRow->name,
                        $question->service_name,
                        $column->name,
                        $answerValue,
                        $points,
                    ];
                }
            }
        }
        uksort($rows, static function($a, $b)
        {
            $a = mb_strtolower($a, 'utf-8');
            $b = mb_strtolower($b, 'utf-8');

            if ($a === $b)
            {
                return 0;
            }

            return ($a < $b) ? -1 : 1;
        });
        $result = [];
        foreach ($rows as $row) {
            foreach ($row as $item) {
                $result[] = $item;
            }
        }
        return $result;
    }

    /**
     * @param FoquzPollAnswerItem[] $answerItems
     * @return bool
     */
    public function isHave3DAnswers(array $answerItems): bool
    {
        $answerItems = array_filter($answerItems, static function ($item) {
            return $item->skipped != 1 && $item->answer !== null && $item->answer !== '';
        });
        $IDs = ArrayHelper::getColumn($answerItems, 'foquz_question_id');
        return (bool)array_intersect($IDs, ArrayHelper::getColumn($this->matrix3DQuestions, 'id'));
    }

    /**
     * Конвертирует кодировку из UTF-8 в Windows-1251
     * @param string|null $value
     * @return string
     */
    public function convertEncoding(?string $value): string
    {
        return mb_convert_encoding($value, 'Windows-1251', 'UTF-8');
    }
}
