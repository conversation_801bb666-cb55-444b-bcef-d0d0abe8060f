<?php

declare(strict_types=1);


namespace app\modules\foquz\services\export\handlers;

use app\models\Export;
use app\modules\foquz\models\ContactAdditionalField;
use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\FoquzPollAnswer;
use app\modules\foquz\models\FoquzPollAnswerItem;
use app\modules\foquz\models\FoquzPollDisplaySetting;
use app\modules\foquz\models\FoquzQuestion;
use app\modules\foquz\models\FoquzQuestionDetail;
use app\modules\foquz\models\SettingTables;
use app\modules\foquz\services\answers\search\AnswerSearchService;
use app\modules\foquz\services\export\ExportHelper;
use avadim\FastExcelWriter\Excel;
use avadim\FastExcelWriter\Style;
use RuntimeException;
use yii\helpers\ArrayHelper;
use yii\web\NotFoundHttpException;

class ExportXLSXAnswers1Handler
{

    private ?FoquzPoll $poll = null;

    private const borderCellStyleLeftBorder = [
        Style::BORDER => [
            Style::BORDER_LEFT => [
                Style::BORDER_STYLE => Style::BORDER_THIN,
            ]
        ]
    ];
    private const borderCellStyleRightBorder = [
        Style::BORDER => [
            Style::BORDER_RIGHT => [
                Style::BORDER_STYLE => Style::BORDER_THIN,
            ]
        ]
    ];
    private const commonRowStyle = [
        Style::TEXT_ALIGN => Style::TEXT_ALIGN_CENTER,
        Style::VERTICAL_ALIGN => 'top',
        Style::TEXT_WRAP => true,
    ];
    private const boldTextStyle = [
        Style::FONT_STYLE_BOLD => true,
        Style::WIDTH => 'auto'
    ];

    /**
     * @throws NotFoundHttpException
     */
    public function handle(
        string $path,
        array $filters,
        int $userCompanyId,
        ?int $userId,
        ?string $key,
        bool $isConsole = false,
        Export|null $export = null,
    ): void
    {
        ini_set('memory_limit', '1000M');

        $id = $filters['id'] ?? null;
        if ($isConsole && $export instanceof Export) {
            $id = $export->params['id'] ?? null;
        }

        if ($id) {
            $this->poll = FoquzPoll::find()->where(['id' => $id, 'company_id' => $userCompanyId])->one();
            if (!$this->poll) {
                throw new NotFoundHttpException('Опрос не найден');
            }
        }
        //Excel::setTempDir("/var/www/opros/runtime/xlsx");
        $excel = Excel::create(['Worksheet']);
        $sheet = $excel->sheet();
        if (!$sheet) {
            throw new RuntimeException('Не удалось создать лист');
        }

        $sheet->mergeCells('B1:D1');
        if ($this->poll) {
            if ($this->poll->is_auto) {
                $sheet->mergeCells('H2:K2');
            } else {
                $sheet->mergeCells('F2:I2');
            }
        }

        if ($isConsole && $export instanceof Export) {
            $export->status = Export::STATUS_PROCESSING;
            $export->total = 100;
            $export->save();
        }

        if ($isConsole && $export instanceof Export) {
            ExportHelper::updateProcess($export, 5);
        }

        $data = $this->getData($filters);

        if ($isConsole && $export instanceof Export) {
            ExportHelper::updateProcess($export, 10);
        }


        // Заголовки таблицы
        $columns = explode(',', $filters['columns']);
        if (($index = array_search('points', $columns)) !== false) {
            unset($columns[$index]);
        }
        if (($index = array_search('name', $columns)) !== false) {
            $columns[$index] = "pollName";
        }
        $columns = ArrayHelper::merge($columns, ['ip', 'os', 'browser']);
        $labels = $this->getHeader($columns, $userCompanyId);

        $data[] = array_values($labels);
        $dataIndex = $this->poll ? 3 : 1;

        $leftBorder = Excel::colLetter(count($data[$dataIndex])+1);
        $rightBorders = [];


        if ($key) {
            $pollID = 0;
            $searchService = AnswerSearchService::getInstanceByLink($key, $pollID);
            $searchService->setPollID($pollID);
        } else {
            $searchService = AnswerSearchService::getInstanceByUser($userId);
        }

        $searchService->applyParams($filters);

        $answersResult=['items' => $searchService->all()];

        $maxAnswers = 1;
        $questions = [];
        if ($this->poll) {
            $questionsQuery = $this
                ->poll
                ->getFoquzQuestions()
                ->andWhere(['!=', 'main_question_type', FoquzQuestion::TYPE_INTERMEDIATE_BLOCK]);

            if ($this->poll->displaySetting?->type === FoquzPollDisplaySetting::MANUAL_SPLIT) {
                $questionsQuery->joinWith(['pollDisplayPageQuestion.displayPage'], false)
                    ->orderBy(['foquz_poll_display_pages.order' => SORT_ASC, 'foquz_question.position' => SORT_ASC]);
            }

            /** @var FoquzQuestion[] $questions */
            $questions = $questionsQuery->all();
            $maxAnswers = count($questions);
        }

        $answersData = $this->getAnswersResult(
            $answersResult,
            $columns,
            $userCompanyId,
            $questions,
            $rightBorders,
            $export
        );



        $data = ArrayHelper::merge($data, $answersData);

        if ($isConsole && $export instanceof Export) {
            ExportHelper::updateProcess($export, 90);
        }

        for ($i = 0; $i < $maxAnswers; $i++) {
            $data[$dataIndex][] = 'Тип вопроса';
            $data[$dataIndex][] = 'Название вопроса';
            $data[$dataIndex][] = 'Ответ';
            $data[$dataIndex][] = 'Комментарий';
        }

        foreach ($data as $rowIndex => $row) {
            foreach ($row as $columnIndex => $column) {
                if (is_string($column) && preg_match('/(^=+)/', $column)) {
                    $data[$rowIndex][$columnIndex] = preg_replace('/(^=+)/', '', $column);
                }
            }
        }


        $maxColCount = isset($data[4]) ? count($data[4]) : count($data[1]);
        $maxRowCount = count($data);
        $maxColLetter = Excel::colLetter($maxColCount);

        $sheet->writeArrayTo('A1', $data);

        // set common style
        $sheet->setStyle('A1:' . $maxColLetter . $maxRowCount, self::commonRowStyle);
        // set borders
        $sheet->setStyle($leftBorder.'4:'.$leftBorder.$maxRowCount,
            array_merge(self::commonRowStyle, self::borderCellStyleLeftBorder));
        foreach ($rightBorders as $rb) {
            $sheet->setStyle($rb.'4:'.$rb.$maxRowCount,
                array_merge(self::commonRowStyle, self::borderCellStyleRightBorder));
        }

        print("9\n");
        // set header styles
        if ($this->poll) {
            $sheet->setStyle('A4:' . $maxColLetter . '4',
                array_merge(self::commonRowStyle, self::boldTextStyle));
        } else {
            $sheet->setStyle('A1:' . $maxColLetter . '1',
                array_merge(self::commonRowStyle, self::boldTextStyle));
        }
        // set preheader styles
        $sheet->setStyle('A1', self::boldTextStyle);
        $sheet->setStyle('A2', self::boldTextStyle);
        $sheet->setStyle('B2', ['font-size' => 12]);
        $sheet->setStyle('C2', self::boldTextStyle);
        $sheet->setStyle('D2', ['font-size' => 12]);
        $sheet->setStyle('E2', self::boldTextStyle);
        $sheet->setStyle('F2', ['font-size' => 12]);
        $sheet->setStyle('H2', ['font-size' => 12]);

        // установка ширин столбцов
        $colWidths = ExportHelper::calculateColumnsWidth($answersData);
        foreach ($colWidths as $colId => $colWidth) {
            $sheet->setColWidth($colId + 1, $colWidth);
        }

        $excel->save($path, false);
        chmod($path,  0777);

        if ($isConsole && $export instanceof Export) {
            $export->processed = $export->total;
        }

    }

    private function getHeader(array $columns, int $userCompanyId): array
    {
        $labels = [];
        $additionalFields = [];
        foreach (ContactAdditionalField::arrayFields($userCompanyId)['additional'] as $field) {
            $additionalFields['client' . $field['id']] = $field['text'];
        }
        foreach ($columns as $value) {
            if (isset(SettingTables::COLUMNS[$value])) {
                $labels[] = SettingTables::COLUMNS[$value];
            }
            if (array_key_exists($value, $additionalFields)) {
                $labels[] = $additionalFields[$value];
            }
        }
        if ($userCompanyId == 633) {
            $labels[] = "Менеджер";
        }
        if ($userCompanyId == 1462) {
            $labels[] = "OTRS";
        }
        if ($userCompanyId == 1625) {
            $labels[] = "Ticket ID";
        }
        //print($companyId); exit;
        if ($userCompanyId == 1055 || $userCompanyId==1990 || $userCompanyId==3026) {
            $labels[] = "Ключ анкеты";
        }
        if ($userCompanyId == 2760) {
            $labels[] = "Ключ задачи";
            $labels[] = "Тип работ";
            $labels[] = "Исполнитель";
            $labels[] = "Имя клиента";
            $labels[] = "Источник";
        }

        if ($this->poll && $this->poll->id == 262574) { //особый опрос касперского
            $labels[] = "task";
            $labels[] = "reporter";
            $labels[] = "assignee";
        }
        if ($this->poll && $this->poll->id == 253110) { //особый опрос касперского
            $labels[] = "JIRA";
            $labels[] = "engineer";
        }

        if ($this->poll && $this->poll->company_id == 3751) {
            $labels[] = "Номер брони";
            $labels[] = "Дата заезда";
            $labels[] = "Дата выезда";
            $labels[] = "ФИО";
        }
        if ($this->poll && $this->poll->id === 267388) { //особый опрос касперского
            $labels[] = 'ID компании';
        }

        return $labels;
    }
    private function getData($filters): array
    {
        $data = [];
        if ($this->poll) {
            if ($this->poll->is_auto) {
                $a2 = 'ID';
                $b2 = (string)$this->poll->id;
                $c2 = 'Тип';
                $d2 = 'Автоматический';
                $e2 = 'Триггер';
                $f2 = $this->poll->triggerString;
                $g2 = 'Название';
                $h2 = $this->poll->name;
                $a1 = 'Данные за период';

                if (isset($post["filters"]["from"])) {
                    $value = date('d.m.Y', strtotime($post['filters']['from'])).' - '.date('d.m.Y', strtotime($post['filters']['to']));
                } else {
                    $value = "-";
                }
                if (isset($post["filters"]["period"]["from"])) {
                    $value = date('d.m.Y', strtotime($post['filters']["period"]['from'])).' - '.date('d.m.Y', strtotime($post['filters']["period"]['to']));

                }
                $b1 = $value;

                $data[] = [$a1, $b1];
                $data[] = [$a2, $b2, $c2, $d2, $e2, $f2, $g2, $h2];
            } else {
                $a2 = 'ID';
                $b2 = (string)$this->poll->id;
                $c2 = 'Тип';
                $d2 = 'Ручной';
                $e2 = 'Название';
                $f2 = $this->poll->name;
                $a1 = 'Данные за период';

                if (!empty($filters["filters"]["from"]) && !empty($filters["filters"]["to"])) {
                    $value = date('d.m.Y', strtotime($filters["filters"]["from"])).' - '.date('d.m.Y', strtotime($filters["filters"]["to"]));
                } else {
                    $value = "-";
                }
                $b1 = $value;

                $data[] = [$a1, $b1];
                $data[] = [$a2, $b2, $c2, $d2, $e2, $f2];
            }
            $data[] = ['', '', ''];
        } else {

            $a1 = 'Данные за период';

            if (!empty($filters["filters"]["from"]) && !empty($filters["filters"]["to"])) {
                $value = date('d.m.Y', strtotime($filters["filters"]["from"])).' - '.date('d.m.Y', strtotime($filters["filters"]["to"]));
            } else {
                $value = "-";
            }
            $b1 = $value;
            $data[] = [$a1, $b1];
        }

        return $data;
    }


    private function getAnswersResult(
        array $answersResult,
        array $columns,
        int $userCompanyId,
        array $questions,
        array &$rightBorders,
        Export|null $export = null
    ): array
    {
        $maxAnswers = 0;
        $data = [];
        $i = 0;
        $step = 1;
        $rowsInStep = count($answersResult['items'])/20;

        $answerModels = FoquzPollAnswer::find()
            ->where(['id' => ArrayHelper::getColumn($answersResult['items'], 'id')])
            ->with([
                'foquzPoll', 'answerChannel', 'pollLang', 'foquzAnswer.foquzQuestion', 'answerFilial', 'order',
                'contact', 'processing', 'mailingListSend.mailingListContact.mailingList', 'tags',
            ])
            ->indexBy('id')
            ->all();

        foreach ($answersResult['items'] as $nRow => $answerModel) {
            if ($nRow>=($rowsInStep*$step)) {
                ExportHelper::updateProcess($export, $step*5+10);
                $step ++;
            }
            $answerModel = $answerModels[$answerModel['id']] ?? null;
            if (!$answerModel) {
                throw new NotFoundHttpException();
            }
            $item = [];

            foreach ($columns as $value) {
                if ($this->poll && $value=="mode") continue;
                if (!$this->poll && $value=="mode") continue;
                if (!$this->poll && $value=="name") continue;
                // общие столбцы
                $item[] = $answerModel->getAnswerValue($value);
            }
            if ($userCompanyId == 633) {
                $item[] = @($answerModel->contact->additionalFieldValues[0]->value);
            }
            if ($userCompanyId == 1462) {
                $otrs = '';
                $cf = $answerModel->custom_fields;
                if ($cf) {
                    $cf = @json_decode($cf, true);
                    if (is_array($cf) && isset($cf["OTRS"])) {
                        $otrs = $cf['OTRS'];
                    }
                }
                $item[] = $otrs;
            }
            if ($userCompanyId == 1625) {
                $otrs = '';
                $cf = $answerModel->custom_fields;
                if ($cf) {
                    $cf = @json_decode($cf, true);
                    if (is_array($cf) && isset($cf["ticket_id"])) {
                        $otrs = $cf['ticket_id'];
                    }
                }
                $item[] = $otrs;
            }
            if ($userCompanyId == 2760) {
                $track_id = '';
                $work_type = '';
                $worker = '';
                $user_name = '';
                $source = '';
                $cf = $answerModel->custom_fields;
                if ($cf) {
                    $cf = @json_decode($cf, true);
                    if (is_array($cf) && isset($cf["track_id"])) {
                        $track_id = $cf['track_id'];
                    }
                    if (is_array($cf) && isset($cf["work_type"])) {
                        $work_type = $cf['work_type'];
                    }
                    if (is_array($cf) && isset($cf["executor"])) {
                        $worker = $cf['executor'];
                    }
                    if (is_array($cf) && isset($cf["user_name"])) {
                        $user_name = $cf['user_name'];
                    }
                    if (is_array($cf) && isset($cf["source"])) {
                        $source = $cf['source'];
                    }
                }
                $item[] = $track_id;
                $item[] = $work_type;
                $item[] = $worker;
                $item[] = $user_name;
                $item[] = $source;
            }
            if ($userCompanyId == 1055 || $userCompanyId==1990 || $userCompanyId==3026) {
                $item[] = $answerModel->getAnswerKey() ? $answerModel->getAnswerKey() : '';
            }
            if ($this->poll && $this->poll->id == 262574) { //особый опрос касперского
                $cf = $answerModel->custom_fields;
                $task = '';
                $reporter = '';
                $assignee = '';
                if ($cf) {
                    $cf = @json_decode($cf, true);
                    if (is_array($cf)) {
                        if (isset($cf['task'])) $task = $cf["task"];
                        if (isset($cf['reporter'])) $reporter = $cf["reporter"];
                        if (isset($cf['assignee'])) $assignee = $cf["assignee"];
                    }
                }
                $item[] = $task;
                $item[] = $reporter;
                $item[] = $assignee;
            }
            if ($this->poll && $this->poll->id == 253110) { //особый опрос касперского
                $cf = $answerModel->custom_fields;
                $jira = '';
                $engineer = "";
                if ($cf) {
                    $cf = @json_decode($cf, true);
                    if (is_array($cf)) {
                        if (isset($cf['JIRA'])) $jira = $cf["JIRA"];
                        if (isset($cf['engineer'])) $engineer = $cf["engineer"];
                    }
                }
                $item[] = $jira;
                $item[] = $engineer;
            }
            if ($this->poll && $this->poll->company_id == 3751) {
                $cf = $answerModel->custom_fields;
                $number = '';
                $arrival = '';
                $depature = '';
                $nameClient = '';
                if ($cf) {
                    $cf = @json_decode($cf, true);
                    if (is_array($cf)) {
                        if (isset($cf['number'])) $number = $cf["number"];
                        if (isset($cf['arrival'])) $arrival = $cf["arrival"];
                        if (isset($cf['depature'])) $depature = $cf["depature"];
                        if (isset($cf['name'])) $nameClient = $cf["name"];
                    }
                }
                $item[] = $number;
                $item[] = $arrival;
                $item[] = $depature;
                $item[] = $nameClient;
            }

            if ($this->poll && $this->poll->id === 267388) { //особый опрос касперского
                $cf = $answerModel->custom_fields;
                $company_id = '';
                if ($cf) {
                    $cf = @json_decode($cf, true);
                    if (is_array($cf) && isset($cf['company_id'])) {
                        $company_id = $cf['company_id'];
                    }
                }
                $item[] = $company_id;
            }

            $answersNpsOrRating = $answerModel->foquzAnswer;
            if (count($answersNpsOrRating)) {

                if ($this->poll) {
                    $questionsWithAnswer = ArrayHelper::getColumn($answersNpsOrRating, 'foquz_question_id');
                    foreach ($questions as $question) {
                        /** @var FoquzPollAnswerItem $answerItem */
                        if (in_array($question->id, $questionsWithAnswer)) {
                            $index = array_search($question->id, $questionsWithAnswer);
                            $answerItem = $answersNpsOrRating[$index];
                        } else {
                            $answerItem = null;
                        }

                        // Тип вопроса
                        $item[] = $question->mainTypeString;

                        // Название вопроса
                        $item[] = ($question->name == '' ? $question->description : $question->name);

                        // Ответ
                        if($answerItem) {
                            $answer = json_decode($answerItem->answer ?? '', true) ?? [];
                            if (isset($answer['-1'][0])) {
                                if ($question->main_question_type === FoquzQuestion::TYPE_SIMPLE_MATRIX ||
                                    $question->main_question_type === FoquzQuestion::TYPE_SCALE
                                ) {
                                    $parts = [];
                                    foreach ($answer as $questionId => $questionAnswer) {
                                        if (is_array($questionAnswer)) {
                                            $answerText = $questionAnswer[0] ?? '';
                                        } else {
                                            $answerText = $questionAnswer;
                                        }
                                        if ($questionId === -1) {
                                            if ($question->donor) {
                                                $donorAnswer = FoquzPollAnswerItem::findOne([
                                                    'foquz_poll_answer_id' => $answerItem->foquz_poll_answer_id,
                                                    'foquz_question_id' => $question->getMainDonor()->id,
                                                ]);
                                                if ($donorAnswer) {
                                                    $parts[] = $donorAnswer->self_variant . ': ' . $answerText;
                                                }
                                            }
                                        } elseif (is_numeric($questionId)) {
                                            $detailQuestion = FoquzQuestionDetail::findOne($questionId);
                                            if ($detailQuestion) {
                                                $parts[] = $detailQuestion->question . ': ' . $answerText;
                                            }
                                        }
                                    }
                                    $item[] = implode("\n", $parts);
                                } else {
                                    $item[] = $answer['-1'][0];
                                }
                            } else {
                                $answerColumn = $answerItem->getAnswerColumn(true);
                                $item[] = (!empty($answerColumn) || $answerColumn === '0') ? $answerColumn : '';
                            }
                        } else {
                            $item[] = '';
                        }

                        // Комментарий
                        $comment = '';
                        if (isset($answerItem->commentColumn)) {
                            $comment = is_null($answerItem->commentColumn) ? '' : $answerItem->commentColumn;
                        }
                        $item[] = $answerItem ? trim($comment, " \n") : '';

                        $letter = Excel::colLetter(count($item));
                        if (!in_array($letter, $rightBorders))
                            $rightBorders[] = $letter;
                    }
                } else {
                    $countAnswers = 0;
                    foreach ($answersNpsOrRating as $answerItem) {
                        if ($answerItem->foquzQuestion->main_question_type == FoquzQuestion::TYPE_INTERMEDIATE_BLOCK) {
                            continue;
                        }
                        $countAnswers++;

                        // $item = [];
                        $item[] = $answerItem->foquzQuestion->mainTypeString;
                        $item[] = ($answerItem->foquzQuestion->name == '' ? strip_tags($answerItem->foquzQuestion->description) : $answerItem->foquzQuestion->name);

                        // Получение ответа
                        $item[] = $answerItem->getAnswerColumn(true);

                        $item[] = $answerItem->commentColumn;
                        $letter = Excel::colLetter(count($item));
                        if (!in_array($letter, $rightBorders))
                            $rightBorders[] = $letter;
                        //$data[] = ArrayHelper::merge($item, $additionalColumns);
                        //if ($i++ >= 3000) {
                        //  break 2;
                        //}
                    }
                    if ($maxAnswers<$countAnswers) $maxAnswers = $countAnswers;
                }
            }
            //if ($poll) {
            $data[] = $item;
            if ($i++ >= 16000) {
                break;
            }
            //}
        }
        return $data;
    }
}