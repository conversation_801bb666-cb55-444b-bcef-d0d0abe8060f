<?php

namespace app\modules\foquz\services;

use app\modules\foquz\models\FilialPollKey;
use app\modules\foquz\models\FoquzPollShortLink;
use app\modules\foquz\models\quotes\FoquzPollLinkQuotes;
use Yii;
use yii\base\InvalidConfigException;
use yii\db\Exception;
use yii\httpclient\Client;
use yii\web\NotFoundHttpException;

class ShortLinkServiceUshortener extends ShortLinkService
{
    protected array $quoteIds;

    public function __construct($poll)
    {
        parent::__construct($poll);

        $this->quoteIds = array_map(static function ($quote) {
            return $quote->id;
        }, $this->poll->quotes);
    }

    /**
     * Получить массив коротких ссылок опроса
     *
     * @param int|null $quote_id
     * @return array
     * @throws Exception
     * @throws \Throwable
     */
    public function getPollShortLinks(int|null $quote_id = null): array
    {
        $ret = [];
        $pollLinkQuotes = $this->poll->getQuotes();
        if ($quote_id) {
            $pollLinkQuotes->andWhere(['id' => $quote_id]);
        }

        foreach ($pollLinkQuotes->all() as $quote) {
            $pollLink = $this->getLink($quote->key);
            $ret[$pollLink]['short'] = $this->updateAndGetReferenceShortLink($quote, $pollLink);
            $ret[$pollLink]['name'] = 'Без филиала';
            $ret[$pollLink]['active'] = $quote->active;
        }
        return $ret;
    }


    /**
     * Получить массив коротких ссылок филиалов
     * @param null $filials
     * @param null $quoteId
     * @return array
     * @throws \Throwable

     */
    public function getPollFilialsShortLinks($filials = null, $quoteId = null): array
    {
        $ret = [];
        $filialPollKeyQuery = FilialPollKey::find()
            ->joinWith('filial')
            ->andWhere(['in', 'quote_id', $this->quoteIds])
            ->orderBy('filials.name');

        if (!$quoteId) {
            $filialPollKeyQuery->andWhere(['in', 'quote_id', $this->quoteIds]);
        } else {
            $filialPollKeyQuery->andWhere(['quote_id' => $quoteId]);
        }

        if ($filials) {
            $filialPollKeyQuery->andWhere(['in', 'filial_id', $filials]);
        }
        $filialPollKeys = $filialPollKeyQuery->all();

        /** @var FilialPollKey $filialPollKey */
        foreach ($filialPollKeys as $filialPollKey) {
            $pollLink = $this->getLink($filialPollKey->key);
            $ret[$pollLink]['name'] = $filialPollKey->filial->name;
            $ret[$pollLink]['active'] = $filialPollKey->filial->is_active;
            $ret[$pollLink]['short'] = $this->updateAndGetReferenceShortLink($filialPollKey, $pollLink);
        }
        return $ret;
    }

    /**
     * Получить короткую ссылку из справочного поля
     * В случае старого опроса - получить из foquz_poll_short_links
     * Если нигде нет - сгенерировать и записать в справочное поле
     * @param FoquzPollLinkQuotes|FilialPollKey $model
     * @param string $pollLink
     * @return string
     * @throws Exception
     * @throws \Throwable
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\httpclient\Exception
     */
    private function updateAndGetReferenceShortLink(FoquzPollLinkQuotes|FilialPollKey $model, string $pollLink): string
    {
        $shortLink = $model->short_link;
        if (empty($shortLink)) {
            $shortLink = FoquzPollShortLink::find()
                ->select('code')
                ->where(['link' => $pollLink])
                ->scalar();
            try {
                if (!$shortLink) {
                    $shortLink = $this->getShortLinkFromUShortenerByCode($model->key);
                } else {
                    // переносим ссылку-короткую ссылку в базу сокращателя
                    $this->addLinkToUrlShortener($pollLink, $shortLink);
                }
            } catch (Exception $e) {
                Yii::warning($e->getMessage());
            }
            $model->short_link = $shortLink;
            $model->save();
        }
        return $shortLink;
    }

    /**
     * получить короткую ссылку по коду
     * @param string $code
     * @return string|null
     * @throws Exception
     * @throws \Throwable
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\httpclient\Exception
     */
    public function findShortLinkByKey(string $code): string|null
    {
        $link = $this->getLink($code);
        $links = $this->getPollShortLinks();
        if (isset($links[$link])) {
            return $links[$link]['short'];
        }
        $links = $this->getPollFilialsShortLinks();
        if (isset($links[$link])) {
            return $links[$link]['short'];
        }

        return $this->getShortLinkFromUShortenerByCode($code);
    }

    /**
     * Получить ссылку по короткой ссылке
     * @param string $code
     * @return string
     * @throws NotFoundHttpException
     */
    public static function findLinkByShortLink(string $code): string
    {
        try {
            return self::resolveShortLink($code);
        } catch (\Throwable) {
            // для старых опросов короткие ссылки резолвим по старому
            $shortLink = FoquzPollShortLink::find()->where(['code' => $code])->one();
            if (!$shortLink) {
                throw new NotFoundHttpException();
            }
            /** @var FoquzPollShortLink $shortLink */
            return $shortLink->link;
        }
    }


    // ничего больше в foquz_poll_short_links не пишем
    public function saveAll(): void {}
    public function saveOne($link): ?string{return null;}
    public function updateShortLink($link): void {}

    /**
     * Получить из внешнего сервиса ссылку по короткой ссылке
     * @param string $code
     * @return mixed
     * @throws Exception
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\httpclient\Exception
     */
    public static function resolveShortLink(string $code)
    {
        $client = new Client();
        $response = $client->createRequest()
            ->setMethod('GET')
            ->setUrl(Yii::$app->params['urlShortenerHostPort'] . '/api/v1/info/' . $code . '?access_token=' . urlencode(Yii::$app->params['urlShortenerAuthKey']))
            ->send();

        if (!$response->isOk) {
            throw new Exception('Service returns nonOK answer');
        }
        $result = json_decode($response->content, true);

        return $result['url'];
    }

    public function addLinkToUrlShortener(string $url, string $shortUrl): void
    {
        $client = new Client();
        $response = $client->createRequest()
            ->setMethod('POST')
            ->setUrl(Yii::$app->params['urlShortenerHostPort'] . '/api/v1/add?access_token=' . urlencode(Yii::$app->params['urlShortenerAuthKey']))
            ->setFormat(Client::FORMAT_JSON)
            ->setData([
                'url' => $url,
                'short' => $shortUrl
            ])
            ->send();

        if (!$response->isOk) {
            throw new Exception('Service urlshortener returns nonOK answer: ' . $response->getContent());
        }
    }

    public static function redirectTo($code)
    {
        $client = new Client();
        $response = $client->createRequest()
            ->setMethod('GET')
            ->setUrl(Yii::$app->params['urlShortenerHostPort'] . '/q' . $code )
            ->setOptions([
                'followLocation' => true,
            ])
            ->send();

        return $response;
    }

    /**
     * Получить из внешнего сервиса код короткой ссылки для ключа
     * @param string $key
     * @return string
     * @throws Exception
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\httpclient\Exception|\Throwable
     */
    public function getShortLinkFromUShortenerByCode(string $key): string
    {
        $link = $this->getLink($key);
        return self::getShortLinkFromUShortener($link);
    }

    /**
     * Получить из внешнего сервиса код короткой ссылки для ссылки не опрос
     * @param string $link
     * @return string
     * @throws Exception
     * @throws \Throwable
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\httpclient\Exception
     */
    public static function getShortLinkFromUShortener(string $link): string
    {

       // return mt_rand(0, 9999999999999);
        try {
            $client = new Client();
            $response = $client->createRequest()
                ->setMethod('POST')
                ->setUrl(Yii::$app->params['urlShortenerHostPort'] . '/api/v1/short-url?access_token=' . urlencode(Yii::$app->params['urlShortenerAuthKey']))
                ->setFormat(Client::FORMAT_JSON)
                ->setHeaders(['Content-Type' => 'application/json'])
                ->setData(['url' => $link])
                ->send();

            if (!$response->isOk) {
                throw new \RuntimeException('Service returns nonOK answer');
            }

            $result = json_decode($response->content, true);

            return $result['code'];

        } catch (\Throwable $e) {
            Yii::error($e->getMessage());
            throw $e;
        }
    }
}