<?php

namespace app\modules\foquz\services;

use app\helpers\DateTimeHelper;
use app\models\User;
use app\modules\foquz\models\FilialPollKey;
use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\quotes\FoquzPollAnswerQuotes;
use app\modules\foquz\models\quotes\FoquzPollLinkQuotes;
use Yii;
use yii\helpers\ArrayHelper;
use yii\helpers\Url;

class LinkService
{
    public function __construct(private FoquzPoll $poll)
    {}

    public function setEndScreenout(int $linkQuoteId, int|null $endScreenout): void
    {
        $linkQuote = FoquzPollLinkQuotes::findOne($linkQuoteId);
        if ($linkQuote) {
            $linkQuote->end_screenout = $endScreenout;
            if (!$linkQuote->validate()) {
                throw ValidateException::make('end_screenout', $linkQuote->getErrors('end_screenout')[0]);
            }
            $linkQuote->save();
            $this->poll->refresh();
        }
    }

    public function grabLinkItems(int $linkQuoteId = 0): array
    {
        $items = [];
        foreach ($this->poll->quotes as $quote) {
            if ($linkQuoteId > 0 && $quote->id != $linkQuoteId) {
                continue;
            }

            $items[] = [
                'quote_id' => $quote->id,
                'key' => $quote->key,
                'limit' => $quote->limit,
                'link_name' => $quote->name,
                'end_screenout' => $quote->end_screenout,
                'datetime_end' => DateTimeHelper::formatISO8601($quote->datetime_end),
                'active' => $quote->active,
                'answer_quotes' => count($quote->answerQuotes) ? $quote->answerQuotes : null,
            ];
        }
        return $items;
    }


    public function getLinksForExport(int|null $quoteId): array
    {
        $shortLinkService = new ShortLinkServiceUshortener($this->poll);
        $shortLinks =  $shortLinkService->getPollFilialsShortLinks(quoteId: $quoteId);
        return ArrayHelper::merge($shortLinkService->getPollShortLinks($quoteId), $shortLinks);
    }

    /**
     * @param int|null $quoteId
     * @param int|null $filialId
     * @param bool $withQuotes
     * @return array
     * @throws \Throwable
     */
    public function grabLinkItemsWithFilials(
        int $quoteId = null,
        int|null $filialId = null,
        bool $withQuotes = false
    ): array
    {
        /** @var User $user */
        $user = Yii::$app->user->identity;

        $shortLinkService = new ShortLinkServiceUshortener($this->poll);

        $items = [];
        $pollQuotesQuery = $this->poll->getQuotes()->andFilterWhere(['id' => $quoteId]);
        $pollQuotes = $pollQuotesQuery->all();

        $quoteIds = array_map(static function ($quote) {
            /** @var FoquzPollLinkQuotes $quote */
            return $quote->id;
        }, $pollQuotes);

        $shortLinks = [];
        $filial = null;
        if ($filialId) {
            $filial = FilialPollKey::find()
                ->with('filial')
                ->where(['filial_poll_key.filial_id' => $filialId])
                ->andWhere(['in', 'quote_id', $quoteIds])
                ->one();
            $shortLinks =  $shortLinkService->getPollFilialsShortLinks($filialId);
        }
        $answerQuoteCounters = FoquzPollAnswerQuotes::find()
            ->select('id')
            ->where(['in','link_quote_id', $quoteIds])
            ->andWhere(['is', 'deleted_at', null])
            ->indexBy('link_quote_id')
            ->column();

        $shortLinks = ArrayHelper::merge($shortLinks, $shortLinkService->getPollShortLinks());

        /** @var FoquzPollLinkQuotes $quote */
        foreach ($pollQuotes as $quote) {
            $filialLinks = [];
            /** @var FilialPollKey $filial */
            if ($filial) {
                $link = $shortLinkService->getLink($filial->key);
                $filialLinks[] = [
                    'filial_id' => $filial->filial->id,
                    'filial_name' => $filial->filial->name,
                    'filial_category_id' => $filial->filial->category_id,
                    'key' => $filial->key,
                    'link' => Url::to(['/foquz/default/anonymous', 'id' => $filial->key], 'https'),
                    'qr' => Url::to([
                        '/foquz/foquz-poll/qr',
                        'v' => Url::to(['/foquz/default/anonymous',
                        'id' => $filial->key], true),
                        'label' => $user->company->name
                    ], 'https'),
                    'link_name' => $quote->name,
                    'short_link' => $shortLinkService->getShortLink($shortLinks[$link]['short'])
                ];
            }

            $item = [
                'quote_id' => $quote->id,
                'filial_id' => null,
                'filial_name' => 'Без филиала',
                'filial_category_id' => null,
                'key' => $quote->key,
                'link' => Url::to(['/foquz/default/anonymous', 'id' => $quote->key], 'https'),
                'qr' => Url::to([
                    '/foquz/foquz-poll/qr',
                    'v' => Url::to(['/foquz/default/anonymous', 'id' => $quote->key], true),
                    'label' => $user->company->name
                ], 'https'),
                'limit' => $quote->limit,
                'link_name' => $quote->name,
                'datetime_end' => DateTimeHelper::formatISO8601($quote->datetime_end),
                'active' => $quote->active,
                'has_answer_quotes' => isset($answerQuoteCounters[$quote->id]) ? 1 : 0,
                'end_screen_quotefull' => $quote->end_screen_quotefull,
            ];
            $link = $shortLinkService->getLink($item['key']);
            $item['short_link'] = $shortLinkService->getShortLink($shortLinks[$link]['short']);
            $item['filial_links'] = $filialLinks;

            if ($withQuotes) {
                $quotes = $quote->getAnswerQuotes()
                    ->select(['id', 'name', 'link_quote_id as quote_id'])
                    ->orderBy(['id' => SORT_ASC])
                    ->asArray()
                    ->all();
                array_walk($quotes, static function (&$quote, $idx) {
                    $quote['name'] = (empty($quote['name']) ? 'Квота ' . ($idx + 1) : $quote['name']);
                });
                $item['items'] = $quotes;
            }
            $items[] = $item;
        }
        return $items;
    }


    /**
     * Упрощенный ответ для фильтра в статистике по внешней ссылке
     * @return array
     */
    public function grabLinkWithQuotes(): array
    {
        $items = [];
        $pollQuotesQuery = $this->poll->getQuotes();
        $pollQuotes = $pollQuotesQuery->all();

        $quoteIds = array_map(static function ($quote) {
            /** @var FoquzPollLinkQuotes $quote */
            return $quote->id;
        }, $pollQuotes);

        $answerQuoteCounters = FoquzPollAnswerQuotes::find()
            ->select('id')
            ->where(['in','link_quote_id', $quoteIds])
            ->andWhere(['is', 'deleted_at', null])
            ->indexBy('link_quote_id')
            ->column();

        /** @var FoquzPollLinkQuotes $quote */
        foreach ($pollQuotes as $quote) {
            $item = [
                'quote_id' => $quote->id,
                'filial_id' => null,
                'filial_name' => 'Без филиала',
                'filial_category_id' => null,
                'key' => $quote->key,
                'link' => Url::to(['/foquz/default/anonymous', 'id' => $quote->key], 'https'),
                'limit' => $quote->limit,
                'link_name' => $quote->name,
                'datetime_end' => DateTimeHelper::formatISO8601($quote->datetime_end),
                'active' => $quote->active,
                'has_answer_quotes' => isset($answerQuoteCounters[$quote->id]) ? 1 : 0,
                'end_screen_quotefull' => $quote->end_screen_quotefull,
            ];
            $quotes = $quote->getAnswerQuotes()
                ->select(['id', 'name', 'link_quote_id as quote_id'])
                ->orderBy(['id' => SORT_ASC])
                ->asArray()
                ->all();
            array_walk($quotes, static function (&$quote, $idx) {
                $quote['name'] = (empty($quote['name']) ? 'Квота ' . ($idx + 1) : $quote['name']);
            });
            $item['items'] = $quotes;
            $items[] = $item;
        }
        return $items;
    }


    /**
     * Получить ссылки для опроса
     * @return array
     */
    public function grabLinkSimple(): array
    {
        $items = [];
        $pollQuotesQuery = $this->poll->getQuotes();
        $pollQuotes = $pollQuotesQuery->all();

        /** @var FoquzPollLinkQuotes $quote */
        foreach ($pollQuotes as $quote) {
            $items[] = [
                'quote_id' => $quote->id,
                'link_name' => $quote->name,
                'active' => $quote->active,
            ];
        }
        return $items;
    }
}