<?php
declare(strict_types=1);

namespace app\modules\foquz\services\search;

use app\modules\foquz\services\answers\search\query\AnswerSearchQueryBuilder;
use app\modules\foquz\services\search\filters\CompareFilter;
use app\modules\foquz\services\search\filters\ExpressionFilter;
use app\modules\foquz\services\search\filters\Filter;
use app\modules\foquz\services\search\filters\LikeDatetimeFilter;
use app\modules\foquz\services\search\filters\LikeFilter;
use app\modules\foquz\services\search\filters\NotNullFilter;
use yii\db\Expression;
use yii\db\Query;

/**
 * Базовый абстрактный класс для списков с фильтрами и сортировками.
 */
abstract class SearchQueryBuilder
{
    /**
     * Настройки фильтров, сортировок, связок.
     */
    protected array $configFilters;
    protected array $configLinks;
    protected array $configSorting;
    protected array $configCalcFields;

    /**
     * @var string[] Таблицы, которые нужно подключить в запросе.
     */
    private array $joinedTables = [];
    /**
     * @var array Именованные SQL-выражения, которые нужно добавить в запрос для сортировок.
     */
    private array $calcFields = [];

    /**
     * @var string Основная таблица - alias
     */
    protected string $baseTable;
    /**
     * @var Filter[] Фильтры, которые нужно применить
     */
    protected array $filters = [];
    /**
     * @var array Сортировки, которые нужно применить
     */
    private array $sorting = [];
    /**
     * @var bool Нужна ли группировка по ID для финального запроса.
     */
    protected bool $groupBy = false;


    protected Query $query;

    /* Пока не нужен, но может пригодится в будущем.
    private function baseQuery(ActiveQuery $query): void
    {
        $this->query = $query->alias($this->baseTable);
    }
    */

    /**
     * Начальное создание запроса.
     * @return void
     */
    abstract protected function baseQuery(): void;

    /**
     * Регистрация нового фильтра или значения для ранее зарегистрированного фильтра.
     * @param string $key
     * @param mixed $value
     * @return void
     */
    public function addFilter(string $key, mixed $value): void
    {
        if (!isset($this->filters[$key])) {
            $filter = $this->createFilter($key);
            if (!empty($filter)) {
                $this->filters[$key] = $filter;
            }
        }

        if (isset($this->filters[$key]) && !$this->filters[$key]->isSingleValue()) {
            $this->filters[$key]->addValue($value);
        }
    }

    /**
     * Создание фильтра одного из типов.
     * @param string $filterName
     * @return Filter|null
     */
    protected function createFilter(string $filterName): ?Filter
    {
        if (!isset($this->configFilters[$filterName])) {
            return null;
        }

        $filterConfig = $this->configFilters[$filterName];

        /** @var string $filterType */
        $filterType = $filterConfig[0];
        $filter = match ($filterType) {
            Filter::FILTER_TYPE_IN, Filter::FILTER_TYPE_EQUAL, Filter::FILTER_TYPE_MORE, Filter::FILTER_TYPE_LESS_OR_EQUAL, Filter::FILTER_TYPE_LESS, Filter::FILTER_TYPE_MORE_OR_EQUAL => new CompareFilter($filterConfig[0]),
            Filter::FILTER_TYPE_LIKE => new LikeFilter(),
            Filter::FILTER_TYPE_EXPRESSION => new ExpressionFilter(),
            Filter::FILTER_LIKE_DATETIME => new LikeDatetimeFilter(),
            Filter::FILTER_TYPE_NOT_NULL => new NotNullFilter(),
            default => $this->createCustomFilter($filterType),
        };

        if (empty($filter)) {
            return null;
        }

        $filter->setTable($filterConfig[1] ?? $this->baseTable);
        $filter->setField($filterConfig[2]);
        if (!empty($filterConfig[3])) {
            $filter->setExpression($this->configCalcFields[$filterConfig[3]]);
        }

        return $filter;
    }

    /**
     * Создание особого фильтра конкретным строителем для конкретного списка.
     * @param string $filterType
     * @return Filter|null
     */
    abstract protected function createCustomFilter(string $filterType): ?Filter;

    /**
     * Добавление фильтров в запрос.
     * @return void
     */
    protected function applyFilters(): void
    {
        foreach ($this->filters as $filter) {
            if (!$filter->isSingleValue() && !$filter->hasValues()) {
                Filter::applyNotFound($this->query);
            }
            foreach ($filter->links as $link) {
                $this->joinTable($link);
            }
            if ($filter->tableName) {
                $filter->setAlias($this->joinTable($filter->tableName));
            }
            $filter->apply($this->query);
        }
    }

    /**
     * Регистрация стандартной сортировки по полю или готовому выражению.
     * @param string $name Название сортировки, должно быть указано в конфиге строителя.
     * @param int $type Тип сортировки. SORT_DESC или SORT_ASC
     * @param array|null $params Доп. параметры
     * @return void
     */
    public function addSorting(string $name, int $type, ?array $params = null): void
    {
        $this->sorting[] = ['name' => $name, 'type' => $type, 'custom' => false, 'params' => $params];
    }

    /**
     * Регистрация специфичной для конкретного списка сортировки, которая не может быть настроена через конфиг.
     * @param string $name
     * @param int $type Тип сортировки. SORT_DESC или SORT_ASC
     * @param array|null $params
     * @return void
     */
    public function addSortingCustom(string $name, int $type, ?array $params): void
    {
        $this->sorting[] = ['name' => $name, 'type' => $type, 'custom' => true, 'params' => $params];
    }

    /**
     * Добавление в запрос специфичной сортировки строителем.
     * @param string|null $tableName
     * @param string $name
     * @param int $number
     * @param array|null $params
     * @return string|null
     */
    abstract protected function applyCustomSorting(
        ?string $tableName,
        string $name,
        int $number,
        ?array $params
    ): ?string;

    /**
     * Добавление сортировок в запрос.
     * @return void
     */
    private function applySorting(): void
    {
        $applySorting = [];
        foreach ($this->sorting as $i => $sorting) {
            if (!isset($this->configSorting[$sorting["name"]])) {
                continue;
            }

            $configSorting = $this->configSorting[$sorting["name"]];
            if (!empty($configSorting[AnswerSearchQueryBuilder::SORT_CUSTOM_INDEX])) {
                $sorter = new $configSorting[AnswerSearchQueryBuilder::SORT_CUSTOM_INDEX]($sorting);
                $sorter->apply($this->query);
                continue;
            }
            $tableName = $configSorting[AnswerSearchQueryBuilder::SORT_TABLE_INDEX] ?? null;
            if (!empty($tableName)) {
                $tableName = $this->joinTable($tableName);
            }
            $tableName = $tableName ?: $this->baseTable;

            $fieldName = $configSorting[AnswerSearchQueryBuilder::SORT_CALC_FIELD_INDEX] ?? null;
            if (!empty($fieldName)) {
                $this->addCalcField($fieldName, $sorting["type"]);
            }

            if (!empty($sorting['custom'])) {
                $fieldName = $this->applyCustomSorting($tableName, $sorting['name'], $i, $sorting['params'] ?? null);
            } elseif (empty($fieldName) && !empty($configSorting[AnswerSearchQueryBuilder::SORT_FIELD_INDEX])) {
                $fieldName = $tableName . '.' . $configSorting[AnswerSearchQueryBuilder::SORT_FIELD_INDEX];
            }

            if (empty($fieldName)) {
                continue;
            }

            $applySorting[$fieldName] = $sorting["type"];
            if (!empty($configSorting[AnswerSearchQueryBuilder::SORT_SPECIAL_INDEX])) {
                $this->groupBy = true;
            }
        }

        if (count($applySorting) > 0) {
            $this->query->addOrderBy($applySorting);
        }
    }

    /**
     * Добавление таблицы к запросу, если еще не добавлена.
     * @param string $linkName Идентификатор связи, должен быть настроен в конфиге.
     * @return string
     */
    protected function joinTable(string $linkName): string
    {
        if ($linkName == $this->baseTable || in_array($linkName, $this->joinedTables)) {
            return $linkName;
        }

        $this->joinedTables[] = $linkName;

        $tableJoin = $this->baseTable;
        $alias = $linkName;
        if (preg_match_all("@^(.+)\.([^.]+)$@", $linkName, $arr)) {
            $tableJoin = $this->joinTable($arr[1][0]);
            $alias = $arr[2][0];
        }

        $link = $this->configLinks[$linkName];
        /**
         * @var string $table
         */
        $table = $link[1]::tableName();
        $reverse = isset($link[2]) && $link[2];
        if ($reverse) {
            $field = $tableJoin . ".id";
            $fieldJoin = $alias . "." . $link[0];
        } else {
            $field = $tableJoin . "." . $link[0];
            $fieldJoin = $alias . ".id";
        }

        $addJoinCondition = '';
        if (!empty($link[3])) {
            $addJoinCondition = $link[3];
        }
        $this->query->leftJoin([$alias => $table], "$field=$fieldJoin $addJoinCondition");
        $this->groupBy = true;
        return $alias;
    }

    /**
     * Добавление именованного-SQL выражения в запрос, для сортировок по ним.
     * @param string $fieldName Название выражения, должно быть в конфиге строителя.
     * @param int $sortingType
     * @return void
     */
    private function addCalcField(string $fieldName, int $sortingType = SORT_ASC): void
    {
        if (in_array($fieldName, $this->calcFields)) {
            return;
        }
        $this->calcFields[] = $fieldName;

        $calcField = $this->configCalcFields[$fieldName];
        if (isset($calcField["links"])) {
            foreach ($calcField["links"] as $link) {
                $this->joinTable($link);
            }
        }

        $expression = str_replace('{type_sort}', ($sortingType == SORT_DESC ? "DESC" : "ASC"),
            $calcField["expression"]);
        $this->query->addSelect([$fieldName => new Expression($expression)]);
    }

    /**
     * Построение запроса с фильтрами, но без сортировок.
     * @return void
     */
    protected function build(): void
    {
        $this->baseQuery();

        $this->applyFilters();
    }

    /**
     * Запрос для количества элементов.
     * @return Query
     */
    public function buildCount(): Query
    {
        $this->build();
        if ($this->groupBy) {
            return $this->query->select(["count(DISTINCT answer.id)"]);
        }
        return $this->query->select(["count(*)"]);
    }

    /**
     * Запрос на получение списка.
     * @param null $onlySelect если нужны только определенные поля
     * @return Query
     */
    public function buildList($onlySelect = null): Query
    {
        $this->build();
        if ($onlySelect) {
            $this->query->select($onlySelect);
        }
        $this->applySorting();
        if ($this->groupBy) {
            $this->query->groupBy("answer.id");
        }
        //print($this->query->createCommand()->getRawSql());
        return $this->query;
    }

}