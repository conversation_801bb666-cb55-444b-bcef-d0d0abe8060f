<?php
/** @noinspection PhpDeprecationInspection */
declare(strict_types=1);

namespace app\modules\foquz\services\answers\stat;

use app\helpers\DevHelper;
use app\models\Filial;
use app\models\User;
use app\modules\foquz\models\ContactAdditionalField;
use app\modules\foquz\models\FoquzFile;
use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\FoquzPollAnswer;
use app\modules\foquz\models\FoquzPollDisplaySetting;
use app\modules\foquz\models\FoquzPollQuestionViewLogic;
use app\modules\foquz\models\FoquzPollStatFilterSettings;
use app\modules\foquz\models\FoquzPollStatsLink;
use app\modules\foquz\models\FoquzQuestion;
use app\modules\foquz\services\answers\search\query\AnswerSearchQueryBuilder;
use app\modules\foquz\services\answers\stat\questions\AssessmentQuestionStat;
use app\modules\foquz\services\answers\stat\questions\CardSortingClosedStat;
use app\modules\foquz\services\answers\stat\questions\ChooseMediaQuestionStat;
use app\modules\foquz\services\answers\stat\questions\DateQuestionStat;
use app\modules\foquz\services\answers\stat\questions\DictionaryQuestionStat;
use app\modules\foquz\services\answers\stat\questions\DistibutionScaleQuestionStat;
use app\modules\foquz\services\answers\stat\questions\FileUploadQuestionStat;
use app\modules\foquz\services\answers\stat\questions\FilialQuestionStat;
use app\modules\foquz\services\answers\stat\questions\FirstClickQuestionStat;
use app\modules\foquz\services\answers\stat\questions\FormQuestionStat;
use app\modules\foquz\services\answers\stat\questions\GalleryRatingQuestionStat;
use app\modules\foquz\services\answers\stat\questions\Matrix3DQuestionStat;
use app\modules\foquz\services\answers\stat\questions\MatrixQuestionStat;
use app\modules\foquz\services\answers\stat\questions\NpsSimpleQuestionStat;
use app\modules\foquz\services\answers\stat\questions\PriorityQuestionStat;
use app\modules\foquz\services\answers\stat\questions\QuestionStat;
use app\modules\foquz\services\answers\stat\questions\RatingQuestionStat;
use app\modules\foquz\services\answers\stat\questions\ScaleQuestionStat;
use app\modules\foquz\services\answers\stat\questions\SemDifferentialQuestionStat;
use app\modules\foquz\services\answers\stat\questions\SmilesQuestionStat;
use app\modules\foquz\services\answers\stat\questions\TextQuestionStat;
use app\modules\foquz\services\answers\stat\questions\VariantsNPSQuestionStat;
use app\modules\foquz\services\answers\stat\questions\VariantsQuestionStat;
use app\modules\foquz\services\answers\stat\questions\VariantsStarsQuestionStat;
use Yii;
use yii\db\ActiveQuery;
use yii\helpers\ArrayHelper;
use yii\web\BadRequestHttpException;
use yii\web\ForbiddenHttpException;
use yii\web\NotFoundHttpException;

/**
 * Сервис для раздела Статистика опроса.
 * Создание запроса, расчет и подготовка данных.
 */
class AnswerStatService
{
    private FoquzPoll $poll;
    private const TIME_LIMIT_LOGGING = 300;
    private bool $logging = true;

    /** @var bool $hasFilters Используются ли фильтры. */
    private bool $hasFilters = false;
    private AnswerSearchQueryBuilder $query;

    /** @var FoquzQuestion[] $questions Вопросы, по которым нужна статистика. */
    private array $questions;
    private const ITEM_FIELDS = [
        QuestionStat::FIELD_DETAIL_ITEM     => 'answerItem.detail_item',
        QuestionStat::FIELD_QUESTION_ID     => 'answerItem.foquz_question_id',
        QuestionStat::FIELD_RATING          => 'answerItem.rating',
        QuestionStat::FIELD_SKIPPED         => 'answerItem.skipped',
        QuestionStat::FIELD_SELF_VARIANT    => 'answerItem.self_variant',
        QuestionStat::FIELD_ITEM_ID         => 'answerItem.id',
        QuestionStat::FIELD_IS_SELF_VARIANT => 'answerItem.is_self_variant',
        QuestionStat::FIELD_ANSWER          => 'answerItem.answer',
        QuestionStat::FIELD_FILES           => "count(files.id)",
        QuestionStat::FIELD_FILIAL_ID       => 'answer.answer_filial_id',

        QuestionStat::FIELD_FIRST_CLICK_POINTS_X => 'answerItemPoints.x',
        QuestionStat::FIELD_FIRST_CLICK_POINTS_Y => 'answerItemPoints.y',
        QuestionStat::FIELD_FIRST_CLICK_POINTS_TIME => 'answerItemPoints.click_time',
        QuestionStat::FIELD_FIRST_CLICK_POINTS_AREA => 'answerItemPoints.area_id',
    ];

    /**
     * Конструктор, делаем его недоступным снаружи.
     */
    private function __construct()
    {
    }

    /**
     * @return FoquzPoll
     */
    public function getPoll(): FoquzPoll
    {
        return $this->poll;
    }

    /**
     * Получить экземпляр по внешней ссылки.
     * Можно передать id опроса и id компании для дополнительной проверки доступа.
     *
     * @param string $linkKey
     * @param int|null $pollId
     * @param int|null $companyId
     * @return AnswerStatService
     * @throws BadRequestHttpException
     * @throws ForbiddenHttpException
     * @throws NotFoundHttpException
     */
    public static function getInstanceByLink(
        string $linkKey,
        ?int $pollId = null,
        ?int $companyId = null
    ): AnswerStatService {
        if (strlen($linkKey) !== 32) {
            throw new BadRequestHttpException('Некорректная ссылка');
        }

        $linkPoll = FoquzPollStatsLink::find()
            ->select('foquz_poll_id, link')
            ->where(['right_level' => FoquzPollStatsLink::RIGHT_READ])
            ->andWhere(['like', 'link', '/stats/' . $linkKey])
            ->asArray()->one();

        if (!$linkPoll) {
            throw new NotFoundHttpException('Опрос не найден');
        }
        /** @var FoquzPoll|null $poll */
        $poll = FoquzPoll::find()->with(['statsLink' => function ($query) use ($linkPoll) {
            $query->where(['link' => $linkPoll['link']]);
        }])->where(['id' => $linkPoll['foquz_poll_id']])->one();

        if (empty($poll) || $poll->deleted) {
            throw new NotFoundHttpException('Опрос не найден');
        }

        if (!is_null($pollId) && $poll->id != $pollId) {
            throw new ForbiddenHttpException('Доступ запрещен');
        }
        if (!is_null($companyId) && $poll->company_id != $companyId) {
            throw new ForbiddenHttpException('Доступ запрещен');
        }

        $service = new self();
        $service->poll = $poll;
        $service->query = new AnswerSearchQueryBuilder();
        $service->applyPoll();
        return $service;
    }

    /**
     * Получить экземпляр по id опроса.
     * Можно передать id пользователя для дополнительной проверки прав.
     *
     * @param int $pollId
     * @param ?int $userId
     * @return AnswerStatService
     * @throws ForbiddenHttpException
     * @throws NotFoundHttpException
     */
    public static function getInstanceByPollId(int $pollId, ?int $userId = null): AnswerStatService
    {
        $poll = FoquzPoll::findOne($pollId);
        if (empty($poll) || $poll->deleted) {
            throw new NotFoundHttpException("Опрос не существует");
        }

        if (!empty($userId)) {
            $user = User::findOne($userId);
            if (empty($user) || empty($user->company)) {
                throw new ForbiddenHttpException('Доступ запрещен');
            }
            if ($user->company->id !== $poll->company_id) {
                throw new ForbiddenHttpException('Доступ запрещен');
            }
        }

        $service = new self();
        $service->poll = $poll;
        if (!empty($userId)) {
            $service->checkUser($user);
        }
        $service->query = new AnswerSearchQueryBuilder();
        if (!empty($userId)) {
            $service->applyUser($user);
            $service->applyQuestionsFilters($user);
        }
        $service->applyPoll();
        return $service;
    }

    /**
     * Особая фильтрация вопросов по филиалу для единственного опроса
     * @param ActiveQuery $query
     * @return ActiveQuery
     */
    public function filterFilialQuestion(ActiveQuery $query) : ActiveQuery {
        $filters = $this->validateFilters($this->params);
        if (empty($filters['filials']) || !is_array($filters['filials'])) {
            return $query;
        }

        $filials = Filial::find()->where(['id'=>$filters['filials']])->select('param1')->column();
        if (empty($filials)) {
            return $query;
        }

        $questionIds = FoquzQuestion::find()
            ->leftJoin(FoquzPollQuestionViewLogic::tableName(), FoquzQuestion::tableName().'.id='.FoquzPollQuestionViewLogic::tableName().'.question_id')
            ->where([FoquzQuestion::tableName().'.poll_id'=>$this->poll->id])
            ->andWhere([
                'or',
                [FoquzPollQuestionViewLogic::tableName().'.id'=>null],
                [FoquzPollQuestionViewLogic::tableName().'.parameter'=>'FILIAL.param1', FoquzPollQuestionViewLogic::tableName().'.parameter_value'=>$filials],
            ])
            ->select([FoquzQuestion::tableName().'.id'])
            ->column();
        $query->andWhere([FoquzQuestion::tableName().'.id' => $questionIds]);
        return $query;
    }

    /**
     * Загрузка данных в вопросы для расчета статистики.
     *
     * @param array $questionsID Если статистика нужна только для отдельных вопросов.
     * @return void
     */
    private function prepareForStatistics(array $questionsID = []): void
    {
        $this->poll->setWithDeletedQuestions(true);
        /** @var ActiveQuery $query */
        $query = $this->poll->getFoquzQuestions();

        //фильтрация вопросов по филиалу
        //только для одного опроса
        if ($this->poll->id==290795) {
            $query = $this->filterFilialQuestion($query);
        }

        /** @var FoquzQuestion[] $questions */
        $query->andWhere(['foquz_question.is_tmp' => false])
            ->andWhere(['!=', 'foquz_question.main_question_type', FoquzQuestion::TYPE_INTERMEDIATE_BLOCK])
            ->andFilterWhere(['foquz_question.id' => $questionsID])
            ->with([
                'rightAnswer',
                "questionSmiles",
                "npsRatingSetting",
                "differentialRows",
                'starRatingOptions',
                'questionDetails.file',
                'recipientQuestionDetails.questionDetail',

            ]);

        if ($this->poll->displaySetting?->type === FoquzPollDisplaySetting::MANUAL_SPLIT) {
            $query->joinWith(['pollDisplayPageQuestion.displayPage'], false)
                ->orderBy([
                    'foquz_question.is_deleted' => SORT_ASC,
                    'foquz_poll_display_pages.order' => SORT_ASC,
                    'foquz_question.position' => SORT_ASC,
                ]);
        } else {
            $query->orderBy(['foquz_question.is_deleted' => SORT_ASC, 'foquz_question.position' => SORT_ASC]);
        }

        $this->questions = $query->all();
    }

    /**
     * Создание нужного объекта для расчета статистики по конкретному вопросу.
     *
     * @param FoquzQuestion $question
     * @return QuestionStat|null
     */
    private function createQuestionStat(FoquzQuestion $question): ?QuestionStat
    {
        return match ($question->main_question_type) {
            FoquzQuestion::TYPE_NPS_RATING => $question->set_variants ? new VariantsNPSQuestionStat($question) : new NpsSimpleQuestionStat($question),
            FoquzQuestion::TYPE_TEXT_ANSWER, FoquzQuestion::TYPE_ADDRESS => new TextQuestionStat($question),
            FoquzQuestion::TYPE_STAR_RATING, FoquzQuestion::TYPE_RATING => new RatingQuestionStat($question),
            FoquzQuestion::TYPE_SMILE_RATING => new SmilesQuestionStat($question),
            FoquzQuestion::TYPE_SEM_DIFFERENTIAL => new SemDifferentialQuestionStat($question),
            FoquzQuestion::TYPE_GALLERY_RATING => new GalleryRatingQuestionStat($question),
            FoquzQuestion::TYPE_CHOOSE_MEDIA => new ChooseMediaQuestionStat($question),
            FoquzQuestion::TYPE_FILIAL => new FilialQuestionStat($question),
            FoquzQuestion::TYPE_VARIANTS => new VariantsQuestionStat($question),
            FoquzQuestion::TYPE_FILE_UPLOAD => new FileUploadQuestionStat($question),
            FoquzQuestion::TYPE_SCALE => new ScaleQuestionStat($question, $this->poll->company_id),
            FoquzQuestion::TYPE_FORM => new FormQuestionStat($question, $this->poll->company_id),
            FoquzQuestion::TYPE_DATE => new DateQuestionStat($question),
            FoquzQuestion::TYPE_DICTIONARY => new DictionaryQuestionStat($question),
            FoquzQuestion::TYPE_SIMPLE_MATRIX => new MatrixQuestionStat($question),
            FoquzQuestion::TYPE_PRIORITY => new PriorityQuestionStat($question),
            FoquzQuestion::TYPE_3D_MATRIX => new Matrix3DQuestionStat($question),
            FoquzQuestion::TYPE_VARIANT_STAR => new VariantsStarsQuestionStat($question),
            FoquzQuestion::TYPE_ASSESSMENT => new AssessmentQuestionStat($question, $this->params),
            FoquzQuestion::TYPE_CARD_SORTING_CLOSED => new CardSortingClosedStat($question),
            FoquzQuestion::TYPE_DISTRIBUTION_SCALE => new DistibutionScaleQuestionStat($question, $this->poll->company_id),
            FoquzQuestion::TYPE_FIRST_CLICK => new FirstClickQuestionStat($question),
            default => null
        };
    }



    /**
     * Главный метод расчета статистики для опроса по всем вопросам.
     *
     * @param bool $fromWidget Статистика для виджета
     * @param array $onlyQuestions Конкретные вопросы, по которым нужна статистика.
     * @return array
     */
    public function stat(bool $fromWidget = false, array $onlyQuestions = []): array
    {
        $this->prepareForStatistics($onlyQuestions);

        $questions = [];
        $select = [];
        $relations = [];
        $cachedQuestions = [];

        foreach ($this->questions as $question) {
            if (!$this->hasFilters && YII_DEBUG !== true) {
                $cachedQuestion = Yii::$app->cache->get(['question_stat', 'id' => $question->id]);
                if (!empty($cachedQuestion)) {
                    $cachedQuestions[$question->id] = $cachedQuestion;
                }
            }
            $questionStat = $this->createQuestionStat($question);
            $select = array_unique(array_merge($select, $questionStat->getItemFields()));
            $relations = array_unique(array_merge($relations, $questionStat->getRelations()));
            $questions[$question->id] = $questionStat;
        }

        if (count($cachedQuestions) !== count($this->questions)) {
            //если кэш не для всех вопросов
            $items = $this->getAnswerItems($select, $relations, $onlyQuestions);
            foreach ($items as $item) {
                $questionId = $item[QuestionStat::FIELD_QUESTION_ID];
                if (!empty($cachedQuestions[$questionId])) {
                    //есть кэш для вопроса, можно не считать
                    continue;
                }
                if (empty($questions[$questionId])) {
                    continue;
                }
                $questionStat = $questions[$questionId];
                $questionStat->countItem($item);
                $questionStat->countAll++;
                if ($item[QuestionStat::FIELD_SKIPPED] == 1) {
                    $questionStat->skipped++;
                }
            }
        }

        $data = [];
        foreach ($this->questions as $question) {

            if (!empty($cachedQuestions[$question->id])) {
                //просто берем из кэша
                $row = $cachedQuestions[$question->id];
            } else {
                $row = $this->prepareQuestionStat($questions[$question->id]->finalData(), $question);
                if (!$this->hasFilters) {
                    //сохраняем в кэш, только если без фильтров!
                    Yii::$app->cache->set(['question_stat', 'id' => $question->id], $row, 60 * 60 * 24);
                }
            }
            if ($question->main_question_type === FoquzQuestion::TYPE_VARIANTS && $fromWidget) {
                $row = $this->prepareForWidget($row);
            }
            $data[] = $row;
        }
        return $data;
    }

    /**
     * Подготовка формата данных для статистики по вопросу.
     *
     * @param array $stats
     * @param FoquzQuestion $question
     * @return array
     */
    private function prepareQuestionStat(array $stats, FoquzQuestion $question): array
    {
        $properties = [];
        $statistics = [];
        $variants = [];
        $cardCategories = [];
        $answersCount = $stats['answersCount'];
        $countAll = $stats['countAll'];
        if (isset($stats['statistics'])) {
            $statistics = $stats['statistics'];
        }
        if (isset($stats['variants'])) {
            $variants = $stats['variants'];
        }
        if (isset($stats['properties'])) {
            $properties = $stats['properties'];
        }
        if (isset($stats['matrixElements'])) {
            $matrixElements = $stats['matrixElements'];
        }
        if (isset($stats['cardCategories'])) {
            $cardCategories = $stats['cardCategories'];
        }

        $array = [];
        if ($question->name !== null && $question->name !== '') {
            $array[] = $question->name;
        }
        if ($question->description !== null && $question->description !== '') {
            $array[] = $question->description;
        }

        if ($question->main_question_type === FoquzQuestion::TYPE_STAR_RATING || $question->main_question_type === FoquzQuestion::TYPE_RATING) {
            $clarifyingQuestionVariantsArray = $question->questionDetails;
        } else {
            $clarifyingQuestionVariants = ArrayHelper::index($question->questionDetails, null,
                'extra_question')[1] ?? [];
            $clarifyingQuestionVariantsFiles = FoquzFile::findAll([
                'entity_type' => FoquzFile::TYPE_DETAIL,
                'entity_id'   => ArrayHelper::getColumn($clarifyingQuestionVariants, 'id')
            ]);
            $clarifyingQuestionVariantsFiles = ArrayHelper::index($clarifyingQuestionVariantsFiles,
                'entity_id');
            $clarifyingQuestionVariantsArray = ArrayHelper::toArray($clarifyingQuestionVariants);

            if ($question->extra_question_type === FoquzQuestion::EXTRA_QUESTION_DIFFERENT_EACH || $question->variants_with_files) {
                $clarifyingQuestionVariantsArray = array_map(static function ($item) use (
                    $clarifyingQuestionVariantsFiles
                ) {
                    return array_merge($item, [
                        'file_id' => $clarifyingQuestionVariantsFiles[$item['id']]->id ?? null,
                        'file_url' => $clarifyingQuestionVariantsFiles[$item['id']]->fileUrl ?? null,
                        'preview_url' => $clarifyingQuestionVariantsFiles[$item['id']]->previewUrl ?? null,
                    ]);
                }, $clarifyingQuestionVariantsArray);
            }
        }

        $ret = [
            'id'                         => $question->position,
            'question_id'                => $question->id,
            'point_id'                   => $question->point_id,
            'pointName'                  => $question->point->name ?? null,
            'shortName'                  => $question->service_name,
            'name'                       => implode('. ', $array),
            'text'                       => $question->text,
            'is_required'                => $question->is_required,
            'comment_enabled'            => $question->comment_enabled,
            'comment_required'           => $question->comment_required,
            'assessmentType'             => $question->rating_type - 1,
            'chooseMedia'                => $question->grabChooseContent(),
            'chooseType'                 => $question->choose_type,
            'mediaType'                  => $question->getNumberMediaType(),
            'imageUrls'                  => $question->type === FoquzQuestion::TYPE_IMAGE ? $question->grabMediaContent() : [],
            'videoUrls'                  => $question->type === FoquzQuestion::TYPE_VIDEO ? $question->grabMediaContent() : [],
            'disabled'                   => $question->is_deleted,
            'clarifyingQuestion'         => $question->detail_question !== '' ? $question->detail_question : null,
            'clarifyingQuestionVariants' => $clarifyingQuestionVariantsArray,
            'isSystem'                   => (bool)$question->is_system,
            'forAllRates'                => $question->for_all_rates,
            'isSelfAnswer'               => $question->is_self_answer,
            'variantsType'               => $question->variants_element_type,
            'dateType'                   => $question->date_type,
            'count'                      => $countAll,
            'type'                       => $question->main_question_type,
            'properties'                 => $properties,
            'variants'                   => $variants,
            'matrixElements'             => $matrixElements ?? [],
            'statistics'                 => $statistics,
            'linkWithClientField'        => $question->link_with_client_field,
            'linkedClientField'          => ContactAdditionalField::getText($question->linked_client_field,
                $this->poll->company_id),
            'smileType'                  => $question->smile_type,
            'smiles'                     => $question->questionSmiles,
            'gallery'                    => $question->grabMediaContent(),
            'npsRating'                  => $question->npsRatingSetting ?? null,
            'scaleRatingSetting'         => $question->scaleRatingSetting ?? null,
            'matrixSettings'             => $question->matrix_settings ? json_decode($question->matrix_settings) : null,
            'differentialRows'           => $question->differentialRows,
            'starRatingOptions'          => $question->starRatingOptions,
            'points'                     => $question->poll->point_system ? [
                'max'     => $question->maxPoints,
                'min'     => $question->minPoints,
                'avg'     => $question->avgPoint,
                'percent' => $question->maxPoints > 0 ? ($question->avgPoint / $question->maxPoints) * 100 : 0
            ] : null,
            'rightAnswer'                => $question->rightAnswer ?? null,
            'self_variant_text'          => $question->self_variant_text ?? null,
            'self_variant_file'          => $question->selfVariantFile,
            'onlyDateMonth'              => $question->only_date_month ?? false,
            'skip'                       => $question->skip ?? 0,
            'skip_text'                  => $question->skip_text ?? null,
            'skip_variant'               => $question->skip_variant ?? null,
            'answersCount'               => $answersCount,
            'donor'                      => $question->donor,
            'donor_columns'              => $question->donor_columns,
            'donor_rows'                 => $question->donor_rows,
            'donor_chosen'               => $question->donor_chosen,
            'donor_cols_chosen'          => $question->donor_cols_chosen,
            'dictionary_id'              => $question->dictionary_id,
            'dictionary_list_type'       => $question->dictionary_list_type,
            'set_variants'               => $question->set_variants ?? false,
            'from_one'                   => (bool)($question->from_one ?? 0),
            'extra_question_type'        => $question->extra_question_type,
            'extra_question_rate_from'   => $question->extra_question_rate_from,
            'extra_question_rate_to'     => $question->extra_question_rate_to,
            'cardCategories'             => $cardCategories,
        ];

        if (isset($stats['clickAreas'])) {
            $ret['clickAreas'] = $stats['clickAreas'];
        }
        return $ret;
    }

    /**
     * Подготовка формата данных для виджета по вопросу.
     *
     * @param array $questionData
     * @return array
     */
    private function prepareForWidget(array $questionData): array
    {
        $statistics = $questionData['statistics'];
        $variants = $questionData['variants'];
        foreach ($variants as $key => $variant) {
            if ($variant['is_deleted']) {
                unset($variants[$key], $statistics['variants'][$key]);
            }
        }
        $variantsIDs = array_flip(ArrayHelper::getColumn($variants, 'id'));
        usort($variants, static function ($a, $b) use ($statistics, $variantsIDs) {
            return $statistics['variants'][$variantsIDs[$b['id']]] - $statistics['variants'][$variantsIDs[$a['id']]];
        });
        arsort($statistics['variants']);
        $variants = array_values($variants);
        $statistics['variants'] = array_values($statistics['variants']);
        $questionData['variants'] = $variants;
        $questionData['statistics'] = $statistics;
        $questionData['smiles'] = [];
        return $questionData;
    }

    /**
     * Проверка доступности опроса для пользователя с его ролью.
     * Для редактора и наблюдателя.
     * @throws ForbiddenHttpException
     */
    private function checkUser(User $user): void
    {
        if ($user->isEditor() || $user->isWatcher()) {
            $editorFoldersIds = FoquzPoll::getEditorFolderIdsAll($user->id);
            $editorPolls = FoquzPoll::find()
                ->select('id')
                ->andWhere(['folder_id' => $editorFoldersIds['folders']])
                ->column();
            if (!in_array($this->poll->id, $editorPolls)) {
                throw new ForbiddenHttpException('Доступ запрещен');
            }
        }
    }

    /**
     * Добавление ограничений на ответы для роли Сотрудник филиала.
     * @param User $user
     * @return void
     */
    private function applyUser(User $user): void
    {
        if ($user->isFilialEmployee() && !$user->allFilials) {
            $userFilials = ArrayHelper::getColumn($user->userFilials, 'filial_id');
            if ($this->poll->is_auto) {
                $this->query->addFilter('order_filials', $userFilials);
            } else {
                $this->query->addFilter('filials', $userFilials);
            }
            $this->hasFilters = true;
        }
    }

    /**
     * Особый фильтр по ответам на вопросы.
     * @param User $user
     * @return void
     */
    private function applyQuestionsFilters(User $user): void
    {
        $filters = FoquzPollStatFilterSettings::findOne([
            'foquz_poll_id' => $this->poll->id,
            'user_id'       => $user->id
        ])->filter_settings ?? null;
        if ($filters) {
            $this->query->addFilter('questions_filters', [$this->poll->id, $filters]);
            $this->hasFilters = true;
        }
    }

    /**
     * @return void
     */
    private function applyPoll(): void
    {
        $this->query->addFilter('polls', [$this->poll->id]);
    }

    private array $params = [];

    /**
     * Применить все пользовательские фильтры для статистики.
     * @param array $params
     * @return void
     */
    public function applyParams(array $params): void
    {
        $this->params = $params;
        $filters = $this->validateFilters($params);
        $this->applyFilters($filters);
    }

    /**
     * Валидация фильтров, приведение к общему формату для поиска ответов.
     * Для формирования используется тот же класс, что для страниц с ответами.
     * @param array $params
     * @return array
     */
    private function validateFilters(array $params): array
    {
        $filters = [];
        /* можно подгонять к фильтру AnswerSearchService, но не нужно*/
        if (!empty($params['from']) && is_string($params['from']) && $date = strtotime($params['from'])) {
            $filters['from'] = date('Y-m-d', $date);
        }
        if (!empty($params['to']) && is_string($params['to']) && $date = strtotime($params['to'])) {
            $filters['to'] = date('Y-m-d', $date + 60 * 60 * 24);
        }
        if (!empty($params['questionnaires']) && is_string($params['questionnaires'])) {
            $filters['questionnaires'] = [$params['questionnaires']];
        }
        if (!empty($params['questionnaires']) && is_array($params['questionnaires'])) {
            $filters['questionnaires'] = $params['questionnaires'];
        }
        if (!empty($params['devices']) && is_array($params['devices'])) {
            $filters['devices'] = $params['devices'];
        }
        if (!empty($params['links']) && is_array($params['links'])) {
            $filters['links'] = $params['links'];
        }
        if (!empty($params['quote']) && is_array($params['quote'])) {
            $filters['quote'] = $params['quote'];
        }

        $filials = [];
        if (!empty($params['filialCategories']) && is_array($params['filialCategories'])) {
            $index = array_search(0, $params['filialCategories']);
            if ($index !== false) {
                $params['filialCategories'][$index] = null;
            }
            $filials = Filial::find()->where([
                'category_id' => $params['filialCategories'],
                'company_id'  => $this->poll->company->id,
                'is_active'   => 1
            ])->select('id')->column();
        }
        if (!empty($params['filials']) && is_array($params['filials'])) {
            $index = array_search(0, $params['filials']);
            if ($index !== false) {
                $params['filials'][$index] = null;
            }
            $filials = array_unique($filials);
            $filials = array_merge($filials, $params['filials']);
        }
        if (count($filials) > 0) {
            if ($this->poll->is_auto) {
                $filters['order_filials'] = $filials;
            } else {
                $filters['filials'] = $filials;
            }
        }

        if (!empty($params['tags']) && is_array($params['tags'])) {
            if (!empty($params['tagsOperation']) && $params['tagsOperation'] == 2 && count($params['tags']) > 1)
            {
                $filters['tagsOperation'] = $params['tags'];
            } else   {
                $filters['tags'] = $params['tags'];
            }
        }

        return $filters;
    }

    /**
     * Добавление фильтров в строителя запроса для ответов.
     * @param array $filters
     * @return void
     */
    private function applyFilters(array $filters): void
    {
        foreach ($filters as $filterName => $filter) {
            $this->hasFilters = true;
            switch ($filterName) {
                case 'tags' :
                case 'tagsOperation':
                case 'filials':
                case 'devices':
                case 'questionnaires':
                case 'links':
                case 'quote':
                case 'order_filials':
                    $this->query->addFilter($filterName, $filter);
                    break;
                case 'from':
                    $this->query->addFilter('from', $filter);
                    break;
                case 'to':
                    $this->query->addFilter('to', $filter);
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * Формирование запроса и загрузка всех данных для расчета статистики по вопросам опроса.
     *
     * @param array $select
     * @param array $relations
     * @return array
     */
    private function getAnswerItems(array $select, array $relations, ?array $onlyQuestions = null): array
    {
        $start = microtime(true);
        $query = $this->query->buildStat($relations)
            ->select([
                QuestionStat::FIELD_QUESTION_ID => self::ITEM_FIELDS[QuestionStat::FIELD_QUESTION_ID],
                QuestionStat::FIELD_SKIPPED     => self::ITEM_FIELDS[QuestionStat::FIELD_SKIPPED],
            ]);
        foreach ($select as $field) {
            if ($field == QuestionStat::FIELD_FILES) {
                $query->groupBy('answerItem.id');
            }
            $query->addSelect([$field => self::ITEM_FIELDS[$field]]);
        }
        if (!empty($onlyQuestions)) {
            $query->andWhere(['answerItem.foquz_question_id' => $onlyQuestions]);
        }

        $data = $query->all();
        $duration = (microtime(true) - $start) * 1000;
        if ($this->logging && $duration > self::TIME_LIMIT_LOGGING) {
            Yii::info('Данные для статистики. ' . @$_SERVER['HTTP_HOST'] . ' Время выполнения : ' . $duration . PHP_EOL . ($query->createCommand()->getRawSql()),
                'answer_search');
        }
        return $data;
    }

    /**
     * Данные для сводки на странице Статистика по опросу.
     * @return array|int[]
     */
    public function statuses(): array
    {
        if (!$this->hasFilters) {
            $result = ['done' => $this->poll->filled_answers_count];
            $result['processed'] = $result['done'] + $this->poll->in_progress_answers_count;
            $result['opened'] = $result['processed'] + $this->poll->opened_answers_count;
            $result['sended'] = $result['opened'] + $this->poll->sent_answers_count;
            return $result;
        }
        $start = microtime(true);
        $query = $this->query->buildStatusesStat();
        $data = $query->all();
        $result = [
            'sended'    => 0,
            'opened'    => 0,
            'processed' => 0,
            'done'      => 0
        ];
        foreach ($data as $row) {
            $result['sended'] += $row['number'];
            $status = $row['status'];
            if ($status == FoquzPollAnswer::STATUS_DONE) {
                $result['done'] += $row['number'];
            } elseif
            ($status == FoquzPollAnswer::STATUS_IN_PROGRESS) {
                $result['processed'] += $row['number'];
            } elseif
            ($status == FoquzPollAnswer::STATUS_OPEN) {
                $result['opened'] += $row['number'];
            }
        }
        $result['processed'] += $result['done'];
        $result['opened'] += $result['processed'];
        $duration = (microtime(true) - $start) * 1000;

        /* if (DevHelper::isOn()) {
            //print($query->createCommand()->getRawSql());
            //exit;
        } */

        if ($this->logging && $duration > self::TIME_LIMIT_LOGGING) {
            Yii::info('Сводка статистики. ' . @$_SERVER['HTTP_HOST'] . ' Время выполнения : ' . $duration . PHP_EOL . ($query->createCommand()->getRawSql()),
                'answer_search');
        }
        return $result;
    }


}