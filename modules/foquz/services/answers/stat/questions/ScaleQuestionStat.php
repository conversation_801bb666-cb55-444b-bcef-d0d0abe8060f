<?php
declare(strict_types=1);

namespace app\modules\foquz\services\answers\stat\questions;

use app\models\Filial;
use app\modules\foquz\models\FoquzQuestion;
use app\modules\foquz\models\FoquzQuestionScaleRatingSetting;
use yii\helpers\ArrayHelper;

class ScaleQuestionStat extends QuestionStat
{
    /** @var Filial[] $filials */
    private array $filials;
    private array $statistics = [];

    /** @var array $defaultStat Начальная структура для расчета статистики. */
    private array $defaultStat = [];
    private array $variants = [];

    private static ?array $cacheFilials = null;

    public function __construct(FoquzQuestion $question, int $companyId)
    {
        parent::__construct($question);
        $this->donor = $this->question->getMainDonor();
        if (is_null(self::$cacheFilials)) {
            self::$cacheFilials = ArrayHelper::index(Filial::find()
                ->where(['company_id' => $companyId])
                ->with('category')
                ->all(), 'id');
        }
        $this->filials = self::$cacheFilials;
        $this->setDefaultStat();
    }

    public function getItemFields(): array
    {
        return [QuestionStat::FIELD_FILIAL_ID, QuestionStat::FIELD_RATING, QuestionStat::FIELD_ANSWER];
    }

    /**
     * Полное название филиала.
     * @param $filialId
     * @return string
     */
    private function getFilialName($filialId): string
    {
        if (empty($filialId) || empty($this->filials[$filialId])) {
            return 'anonymous';
        }
        $filial = $this->filials[$filialId];
        $name = $filial->category ? $filial->category->name . "/" : '';
        return $name . $filial->name;
    }

    public function countItem(array $item): void
    {
        $filialName = $this->getFilialName($item[QuestionStat::FIELD_FILIAL_ID]);

        if (!isset($this->statistics[$filialName])) {
            $this->statistics[$filialName] = ['count' => 0, 'scale-ratings' => []];
        }
        $this->statistics[$filialName]['count']++;
        $this->answersCount++;

        if (!$this->question->set_variants) {
            //обычная шкала
            $this->collectStats($filialName, $item[QuestionStat::FIELD_RATING]);
            return;
        }

        $answers = $item[QuestionStat::FIELD_ANSWER];
        if (empty($answers)) {
            return;
        }
        $answers = json_decode($answers, true);
        if (!is_array($answers)) {
            return;
        }

        //шкала для вариантов
        foreach ($answers as $idVariant => $rate) {
            $this->collectStats($filialName, $rate, $idVariant);
            if (isset($this->variants[$idVariant])) {
                continue;
            }
            $variant = $this->getVariant($idVariant);
            if (!empty($variant)) {
                $this->variants[$idVariant] = $this->getVariant($idVariant);
            }
        }
    }

    /**
     * Расчет статистики для филиала.
     * @param string $filialName
     * @param string|int|null $rate
     * @param int $numberVariant
     * @return void
     */
    function collectStats(string $filialName, string|int|null $rate, int $numberVariant = 0): void
    {
        $data = $this->statistics[$filialName]['scale-ratings'];

        if ($numberVariant !== 0) {
            //для вариантов
            $data = $data[$numberVariant] ?? null;
        }
        if (empty($data)) {
            $data = $this->defaultStat;
        }
        if (in_array($rate, ['', 'null']) && $numberVariant !== 0) {
            $rate = 'Отказался от оценки';
            if (!isset($data[$rate])) { //так было в старой версии
                $data[$rate] = 0;
            }
        }
        if (isset($data[$rate])) {
            $data[$rate]++;
        }
        if ($numberVariant !== 0) {
            $this->statistics[$filialName]['scale-ratings'][$numberVariant] = $data;
        } else {
            $this->statistics[$filialName]['scale-ratings'] = $data;
        }
    }

    /**
     * Начальная структура для статистика.
     * @return void
     */
    private function setDefaultStat(): void
    {
        $settings = $this->question->scaleRatingSetting;
        if (empty($settings)) {
            $settings = new FoquzQuestionScaleRatingSetting;
            $settings->start = 1;
            $settings->end = 5;
            $settings->step = 1;
        }
        $stepCount = ($settings->end - $settings->start) / $settings->step;
        $this->defaultStat[$settings->start] = 0;
        for ($i = 1; $i <= $stepCount; $i++) {
            $this->defaultStat[$settings->step * $i] = 0;
        }
        $this->defaultStat[$settings->end] = 0;
        if ($this->question->skip_variant) {
            $this->defaultStat['Отказался от оценки'] = 0;
        }
    }

    protected function getData(): array
    {
        $data = ['statistics' => $this->statistics];
        if ($this->question->set_variants) {
            $data['variants'] = $this->variants;
        }
        return $data;
    }
}