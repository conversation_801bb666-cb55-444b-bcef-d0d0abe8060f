<?php
declare(strict_types=1);

namespace app\modules\foquz\services\answers\stat\questions;

use app\modules\foquz\models\FoquzPollAnswerItemPoints;
use app\modules\foquz\models\FoquzQuestion;
use app\modules\foquz\models\FoquzQuestionFirstClick;

class FirstClickQuestionStat extends QuestionStat
{
    private array $statistics = [];
    private array $clickAreas;
    private FoquzQuestionFirstClick $firstClickParams;
    private int $timeExpired = 0;
    private array $answers = [];
    private array $points = [];
    private int $pointsCount = 0;
    private int $clickTimeSumm = 0;

    public function __construct(FoquzQuestion $question)
    {
        parent::__construct($question);
        $this->clickAreas = $question->firstClickArea;
        $this->firstClickParams = $question->firstClick;
    }

    public function getRelations(): array
    {
        return ['answerItem.answerItemPoints'];
    }

    public function getItemFields(): array
    {
        return [
            QuestionStat::FIELD_DETAIL_ITEM,
            QuestionStat::FIELD_ITEM_ID,
            QuestionStat::FIELD_ANSWER,
            QuestionStat::FIELD_FIRST_CLICK_POINTS_X,
            QuestionStat::FIELD_FIRST_CLICK_POINTS_Y,
            QuestionStat::FIELD_FIRST_CLICK_POINTS_TIME,
            QuestionStat::FIELD_FIRST_CLICK_POINTS_AREA,
        ];
    }

    public function countItem($item): void
    {
        if (!empty($item[QuestionStat::FIELD_DETAIL_ITEM]) && !$item[QuestionStat::FIELD_SKIPPED]) {
            $answer = $item[QuestionStat::FIELD_DETAIL_ITEM];
            $details = json_decode($answer, true);
            if (isset($details['time_expired'])) {
                $this->timeExpired++;
            }
            $areaId = (int)$item['fcpa'];
            if ($item['fcpx'] && $item['fcpy']) {
                $this->points[$areaId][] = ['x' => $item['fcpx'], 'y' => $item['fcpy'], 't' => $item['fcpt']];
                $this->clickTimeSumm += $item['fcpt'];
                $this->pointsCount++;
            }
            if (!isset($this->answers[$item['id']])) {
                $this->answers[$item['id']] = $item['id'];
                $this->answersCount++;
            }
        }
    }

    protected function getData(): array
    {
        $this->statistics['points'] = $this->points;
        if ($this->pointsCount > 0) {
            $this->statistics['time_expired'] = $this->timeExpired;
            $this->statistics['time_click_avg'] = $this->clickTimeSumm / $this->pointsCount;
            $this->statistics['clicks_count'] = $this->pointsCount;
            $this->statistics['imageSize'] = ['w' => $this->firstClickParams->image_width, 'h' => $this->firstClickParams->image_height];
            $heatmap = FoquzPollAnswerItemPoints::getHeatMap(
                $this->question->id,
                $this->firstClickParams->image_width,
                $this->firstClickParams->image_height,
            );
            //$this->statistics['heatmap'] = $heatmap['heatmap'];
            $this->statistics['gridWidth'] = $heatmap['gridWidth'];
            $this->statistics['gridHeight'] = $heatmap['gridHeight'];
        }
        return [
            'statistics' => $this->statistics,
            'clickAreas' => $this->clickAreas,
        ];
    }
}