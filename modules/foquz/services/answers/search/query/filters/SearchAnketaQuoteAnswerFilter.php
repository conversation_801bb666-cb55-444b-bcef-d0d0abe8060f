<?php
declare(strict_types=1);

namespace app\modules\foquz\services\answers\search\query\filters;

use app\modules\foquz\models\quotes\FoquzPollAnswerQuote;
use app\modules\foquz\models\quotes\FoquzPollAnswerQuotes;
use app\modules\foquz\services\search\filters\Filter;
use yii\db\Query;

/**
 * Поиск по строке в названии квоты к которой подошла анкета.
 */
class SearchAnketaQuoteAnswerFilter extends Filter
{
    public function apply(Query $query): void
    {
        if (!empty($this->values[0])) {
            $query->distinct();
            $query->leftJoin(['fpaiq' => FoquzPollAnswerQuote::tableName()], 'fpaiq.answer_id=answer.id');
            $query->leftJoin(['fpaq' => FoquzPollAnswerQuotes::tableName()], 'fpaq.id=fpaiq.quote_id');
            $query->andWhere(['is', 'fpaq.deleted_at', null]);
            $query->andWhere(['like', 'fpaq.name', $this->values[0]]);
        }
    }
}