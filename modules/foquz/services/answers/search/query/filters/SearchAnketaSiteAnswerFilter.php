<?php

declare(strict_types=1);

namespace app\modules\foquz\services\answers\search\query\filters;

use app\modules\foquz\models\FoquzPollMailingListSend;
use app\modules\foquz\models\FoquzPollWidgetSite;
use app\modules\foquz\services\search\filters\Filter;
use yii\db\Query;

/**
 * Поиск по строке в названии сайта с которого пришла анкета.
 */
class SearchAnketaSiteAnswerFilter extends Filter
{
    public function apply(Query $query): void
    {
        if (!empty($this->values[0]['value']) && !empty($this->values[0]['companyId'])) {
            $sendsIds = FoquzPollWidgetSite::find()
                ->select(['id'])
                ->where(['company_id' => $this->values[0]['companyId']])
                ->andWhere(['like', 'site', $this->values[0]['value']])
                ->column();
            if ($sendsIds) {
                if (!SearchAnketaWidgetAnswerFilter::isSendJoined($query)) {
                    $query->leftJoin(['fpmls' => FoquzPollMailingListSend::tableName()], 'fpmls.answer_id=answer.id');
                }
                $query->andWhere(['in', 'fpmls.site_id', $sendsIds]);
            } else {
                $query->andWhere('1=0');
            }
        }
    }
}