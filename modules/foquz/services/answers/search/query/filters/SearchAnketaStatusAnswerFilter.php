<?php
declare(strict_types=1);

namespace app\modules\foquz\services\answers\search\query\filters;

use app\modules\foquz\models\FoquzPollAnswer;
use app\modules\foquz\services\search\filters\Filter;
use yii\db\Query;

/**
 * Поиск по строке в названии статуса анкеты.
 */
class SearchAnketaStatusAnswerFilter extends Filter
{
    public function apply(Query $query): void
    {
        $statusSearch = [];

        foreach ($this->values as $value) {
            foreach (FoquzPollAnswer::getStatuses() as $statusCode => $statusName) {
                if (!in_array($statusCode, $statusSearch) && mb_stripos($statusName, $value) !== false) {
                    $statusSearch[] = $statusCode;
                }
            }
        }

        if (count($statusSearch) == 0) {
            self::applyNotFound($query);
            return;
        }

        $query->andWhere([$this->getFilterExpression() => $statusSearch]);
    }
}