<?php
declare(strict_types=1);

namespace app\modules\foquz\services\answers\search\query\filters;

use app\modules\foquz\models\FoquzPollAnswer;
use app\modules\foquz\models\quotes\FoquzPollLinkQuotes;
use app\modules\foquz\services\search\filters\Filter;
use yii\db\Query;

/**
 * Поиск по строке в названии ссылки по которой была пройдена анкета.
 */
class SearchAnketaLinkAnswerFilter extends Filter
{
    public function apply(Query $query): void
    {
        if (!empty($this->values[0])) {
            $query->leftJoin(['fplq' => FoquzPollLinkQuotes::tableName()], 'fplq.id=quote_id');
            $query->andWhere(['like', 'fplq.name', $this->values[0]]);
        }
    }
}