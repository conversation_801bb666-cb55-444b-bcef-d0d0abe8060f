<?php
declare(strict_types=1);

namespace app\modules\foquz\services\answers\search\query\filters;

use app\modules\foquz\models\FoquzPollMailingListSend;
use app\modules\foquz\models\FoquzPollWidget;
use app\modules\foquz\services\search\filters\Filter;
use yii\db\Query;

/**
 * Поиск по строке в названии виджета с которого пришла анкета.
 */
class SearchAnketaWidgetAnswerFilter extends Filter
{
    public function apply(Query $query): void
    {
        if (!empty($this->values[0])) {
            if (!self::isSendJoined($query)) {
                $query->leftJoin(['fpmls' => FoquzPollMailingListSend::tableName()], 'fpmls.answer_id=answer.id');
            }
            $query->leftJoin(['fpw' => FoquzPollWidget::tableName()], 'fpw.id=fpmls.widget_id');
            $query->andWhere(['is not', 'fpmls.widget_id', null]);
            $query->andWhere(['like', 'fpw.name', $this->values[0]]);
        }
    }

    public static function isSendJoined(Query $query): bool
    {
        if (!$query->join) {
            return false;
        }
        foreach ($query->join as $join) {
            if (isset($join[1]['fpmls'])) {
                return true;
            }
        }
        return false;
    }
}