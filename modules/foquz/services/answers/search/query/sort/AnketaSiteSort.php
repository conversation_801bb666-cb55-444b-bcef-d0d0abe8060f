<?php

declare(strict_types=1);

namespace app\modules\foquz\services\answers\search\query\sort;

use app\modules\foquz\models\FoquzPollMailingListSend;
use app\modules\foquz\services\answers\search\query\filters\SearchAnketaWidgetAnswerFilter;
use yii\db\Query;

readonly class AnketaSiteSort implements AnswerSortInterface
{
    public function __construct(private mixed $values){}

    public function apply(Query $query): void
    {
        if (!empty($this->values['type']) && !empty($this->values['params']['companyId'])) {
            if (!SearchAnketaWidgetAnswerFilter::isSendJoined($query)) {
                $query->leftJoin(['fpmls' => FoquzPollMailingListSend::tableName()], 'fpmls.answer_id=answer.id');
            }
            $company_id = (int)$this->values['params']['companyId'];
            if ($this->values['type'] === SORT_DESC) {
                $query->leftJoin('foquz_poll_widget_site fpws','fpmls.site_id = fpws.id AND fpws.company_id=:company_id',[':company_id' => $company_id]);
                $query->orderBy('`fpws`.`site` DESC');

            } else {
                $query->leftJoin('foquz_poll_widget_site fpws','fpmls.site_id = fpws.id AND fpws.company_id=:company_id',[':company_id' => $company_id]);
                $query->orderBy('`fpws`.`site`');
            }
        }
    }
}