<?php
declare(strict_types=1);

namespace app\modules\foquz\services\answers\search\query;

use app\modules\foquz\models\ContactAdditionalFieldValue;
use app\modules\foquz\models\FoquzPollAnswer;
use app\modules\foquz\services\answers\search\query\filters\ClientCustomFieldsAnswerFilter;
use app\modules\foquz\services\answers\search\query\filters\PointsAnswerFilter;
use app\modules\foquz\services\answers\search\query\filters\QuestionsAnswerFilter;
use app\modules\foquz\services\answers\search\query\filters\ScoreAnswerFilter;
use app\modules\foquz\services\answers\search\query\filters\SearchAnketaSiteAnswerFilter;
use app\modules\foquz\services\answers\search\query\filters\SearchAnketaWidgetAnswerFilter;
use app\modules\foquz\services\answers\search\query\filters\SearchAnketaLinkAnswerFilter;
use app\modules\foquz\services\answers\search\query\filters\SearchAnketaQuoteAnswerFilter;
use app\modules\foquz\services\answers\search\query\filters\SearchAnketaStatusAnswerFilter;
use app\modules\foquz\services\answers\search\query\filters\SearchProcessingStatusAnswerFilter;
use app\modules\foquz\services\answers\search\query\filters\SearchScoreAnswerFilter;
use app\modules\foquz\services\answers\search\query\filters\TagsAnswerFilter;
use app\modules\foquz\services\search\filters\Filter;
use app\modules\foquz\services\search\filters\ManyFieldsLikeFilter;
use app\modules\foquz\services\search\SearchQueryBuilder;
use Yii;
use yii\db\Query;

/**
 * Построитель запрос с фильтрами и сортировками для анкет.
 */
class AnswerSearchQueryBuilder extends SearchQueryBuilder
{
    /**
     * Конфиг запроса. Стандартные фильтры и сортировки можно добавить через него.
     */
    use AnswerSearchQueryBuilderConfigTrait;

    /**
     * Особые фильтры для анкет.
     */
    public const FILTER_ANSWER_USER = 'answer_user';
    public const FILTER_ANSWER_ANSWER_COMMENT = 'answer_comment';
    public const FILTER_ANSWER_CONTACT = 'answer_contact';
    public const FILTER_ANSWER_ORDER_NUMBER = 'answer_order_number';
    public const FILTER_ANSWER_QUERY = 'answer_query';
    public const FILTER_ANSWER_PROCESSING_STATUS = 'answer_processing_status';
    public const FILTER_ANSWER_SEARCH_SCORE = 'answer_search_score';
    public const FILTER_ANSWER_POINTS = 'answer_points';
    public const FILTER_ANSWER_CLIENT_CUSTOM_FIELD = 'answer_client_additional_custom_field';
    public const FILTER_ANSWER_CLIENT_TAGS = 'answer_client_tag';
    public const FILTER_ANSWER_TAGS = 'answer_tag';
    public const FILTER_ANSWER_SCORE = 'answer_score';
    public const FILTER_ANSWER_QUESTIONS = 'answer_questions';
    public const FILTER_ANSWER_WIDGET = 'answer_widget';
    public const FILTER_ANSWER_SITE = 'answer_site';
    public const FILTER_ANSWER_STATUS = 'answer_status';
    public const FILTER_ANSWER_LINK = 'answer_link';
    public const FILTER_ANSWER_QUOTE = 'answer_quote';


    // константы для массива конфига сортировки
    public const SORT_FIELD_INDEX = 0;
    public const SORT_TABLE_INDEX = 1;
    public const SORT_CALC_FIELD_INDEX = 2;
    public const SORT_SPECIAL_INDEX = 3;
    public const SORT_NEED_GROUP_INDEX = 4;
    public const SORT_CUSTOM_INDEX = 5;


    /**
     * Специальный составной индекс по опросу+статусу+дате обновление анкеты.
     */
    private const INDEX_POLL_STATUS_UPDATED_NAME = 'answer_poll_id_status';
    /**
     * @var bool Есть ли автоматические опросы у компании.
     */
    public bool $hasAuto = false;
    private ?string $indexName = null;
    protected const SORTING_CUSTOM_CLIENT_FIELD = 'sortingAdditional';

    /**
     * @var bool
     */
    private bool $allStatuses = false;

    /**
     * Задаем псевдоним базовой таблицы и конфиги.
     */
    public function __construct()
    {
        $this->baseTable = 'answer';
        $this->configFilters = $this->getFiltersConfig();
        $this->configLinks = $this->getLinksConfig();
        $this->configSorting = $this->getSortingConfig();
        $this->configCalcFields = $this->getCalcFieldsConfig();
    }

    protected function baseQuery(): void
    {
        $this->query = (new Query());
        if (!empty($this->polls)) {
            if (count($this->polls) > 50 || count($this->polls) == 1) {
                $this->indexName = self::INDEX_POLL_STATUS_UPDATED_NAME;
            }
        }

        $useIndex = !empty($this->indexName) ? 'USE INDEX (' . self::INDEX_POLL_STATUS_UPDATED_NAME . ') ' : '';

        $this->query->from(FoquzPollAnswer::tableName() . ' as answer  ' . $useIndex);

        $this->query->select("answer.*");

        if (!$this->allStatuses) {
            $this->query->where([
                "answer.status" => [
                    FoquzPollAnswer::STATUS_DONE,
                    FoquzPollAnswer::STATUS_IN_PROGRESS,
                    FoquzPollAnswer::STATUS_QUOTE_FULL,
                    FoquzPollAnswer::STATUS_SCREEN_OUT,
                ]
            ]);
        }
    }

    protected function createCustomFilter(string $filterType): ?Filter
    {
        return match ($filterType) {
            self::FILTER_ANSWER_USER => new ManyFieldsLikeFilter(['name', 'username']),
            self::FILTER_ANSWER_ANSWER_COMMENT => new ManyFieldsLikeFilter(['answer', 'self_variant', 'detail_item']),
            self::FILTER_ANSWER_CONTACT => new ManyFieldsLikeFilter(['last_name', 'first_name', 'patronymic']),
            self::FILTER_ANSWER_ORDER_NUMBER => new ManyFieldsLikeFilter(['number', 'id']),
            self::FILTER_ANSWER_QUERY => new ManyFieldsLikeFilter([
                'last_name',
                'first_name',
                'patronymic',
                'phone',
                'email'
            ]),
            self::FILTER_ANSWER_PROCESSING_STATUS => new SearchProcessingStatusAnswerFilter(),
            self::FILTER_ANSWER_SEARCH_SCORE => new SearchScoreAnswerFilter(),
            self::FILTER_ANSWER_POINTS => new PointsAnswerFilter(),
            self::FILTER_ANSWER_CLIENT_TAGS => new TagsAnswerFilter('contact_id', 'contact_id'),
            self::FILTER_ANSWER_TAGS => new TagsAnswerFilter('answer_id', 'id'),
            self::FILTER_ANSWER_CLIENT_CUSTOM_FIELD => new ClientCustomFieldsAnswerFilter(),
            self::FILTER_ANSWER_QUESTIONS => new QuestionsAnswerFilter(),
            self::FILTER_ANSWER_SCORE => new ScoreAnswerFilter(),
            self::FILTER_ANSWER_WIDGET => new SearchAnketaWidgetAnswerFilter(),
            self::FILTER_ANSWER_SITE => new SearchAnketaSiteAnswerFilter(),
            self::FILTER_ANSWER_STATUS => new SearchAnketaStatusAnswerFilter(),
            self::FILTER_ANSWER_LINK => new SearchAnketaLinkAnswerFilter(),
            self::FILTER_ANSWER_QUOTE => new SearchAnketaQuoteAnswerFilter(),
            default => null,
        };
    }

    protected function applyCustomSorting(?string $tableName, string $name, int $number, ?array $params): ?string
    {
        if ($name !== self::SORTING_CUSTOM_CLIENT_FIELD || empty($params)) {
            return null;
        }

        $fieldId = $params['fieldId'] ?? null;
        if (empty($fieldId)) {
            return null;
        }

        $tableValue = "sortClientValue" . $number;
        $fieldId = Yii::$app->db->quoteValue($fieldId);
        $this->query->leftJoin([$tableValue => ContactAdditionalFieldValue::tableName()],
            $tableValue . '.contact_id=' . $tableName . '.id AND ' . $tableValue . '.additional_field_id=' . $fieldId);
        return $tableValue . ".value";
    }

    /**
     * Запрос для статистики по статусам обработки.
     * @return Query
     */
    public function buildProcessingStat(): Query
    {
        $this->build();
        $this->joinTable('processing');
        $this->query->groupBy('processing.status')->select(['status' => 'processing.status']);
        if ($this->groupBy) {
            $this->query->addSelect(['number' => 'count(DISTINCT answer.id)']);
        } else {
            $this->query->addSelect(['number' => 'count( answer.id)']);
        }
        return $this->query;
    }

    /**
     * Запрос для статистики по статусам обработки.
     * @return Query
     */
    public function buildStatusesStat(): Query
    {
        $this->allStatuses = true;
        $this->build();
        $this->query->groupBy('answer.status')->select(['status' => 'answer.status']);
        if ($this->groupBy) {
            $this->query->addSelect(['number' => 'count(DISTINCT answer.id)']);
        } else {
            $this->query->addSelect(['number' => 'count(answer.id)']);
        }
        return $this->query;
    }

    public function buildStat(array $relations = []): Query
    {
        $this->build();
        $this->joinTable("answerItem");
        foreach ($relations as $relation) {
            $this->joinTable($relation);
        }
        return $this->query;
    }

    private ?array $polls = null;

    /**
     * Переопределен, чтобы посчитать опросы
     * @param string $key
     * @param mixed $value
     * @return void
     */
    public function addFilter(string $key, mixed $value): void
    {
        if ($key == "polls") {
            if (!is_array($value)) {
                $value = [$value];
            }
            if (empty($this->polls)) {
                $this->polls = $value;
            } else {
                $this->polls = array_values(array_intersect($this->polls, $value));
            }

        }
        parent::addFilter($key, $value);
    }


}
