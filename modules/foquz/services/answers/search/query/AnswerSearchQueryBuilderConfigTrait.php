<?php
declare(strict_types=1);

namespace app\modules\foquz\services\answers\search\query;

use app\models\company\Company;
use app\models\Filial;
use app\models\FilialCategory;
use app\models\Order;
use app\models\User;
use app\modules\foquz\models\FoquzCompanyTag;
use app\modules\foquz\models\FoquzComplaint;
use app\modules\foquz\models\FoquzContact;
use app\modules\foquz\models\FoquzContactComputedField;
use app\modules\foquz\models\FoquzContactFilial;
use app\modules\foquz\models\FoquzContactTag;
use app\modules\foquz\models\FoquzOrderSource;
use app\modules\foquz\models\FoquzOrderType;
use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\FoquzPollAnswerItem;
use app\modules\foquz\models\FoquzPollAnswerItemFile;
use app\modules\foquz\models\FoquzPollAnswerItemPoints;
use app\modules\foquz\models\FoquzPollMailingList;
use app\modules\foquz\models\FoquzPollMailingListContact;
use app\modules\foquz\models\FoquzPollMailingListSend;
use app\modules\foquz\models\FoquzPollWidget;
use app\modules\foquz\models\FoquzQuestion;
use app\modules\foquz\models\PollLang;
use app\modules\foquz\models\processing\FoquzPollAnswerProcessing;
use app\modules\foquz\services\answers\search\query\sort\AnketaSiteSort;
use app\modules\foquz\models\quotes\FoquzPollAnswerQuote;
use app\modules\foquz\models\quotes\FoquzPollAnswerQuotes;
use app\modules\foquz\models\quotes\FoquzPollLinkQuotes;
use app\modules\foquz\services\search\filters\Filter;


/**
 * Все варианты фильтров, сортировок и связей анкет.
 */
trait AnswerSearchQueryBuilderConfigTrait
{
    /**
     * @return array[]
     * Все варианты фильтров
     * [тип фильтра, название таблицы (null-базовая таблица), название поля, название выражения, boolean-нужна ли группировка]
     */
    private function getFiltersConfig(): array
    {
        return [
            'polls'                         => [Filter::FILTER_TYPE_IN, null, 'foquz_poll_id'],
            'questionnaires'                => [Filter::FILTER_TYPE_IN, null, 'status'],
            'links'                         => [Filter::FILTER_TYPE_IN, null, 'quote_id'],
            'quote'                         => [Filter::FILTER_TYPE_IN, 'answerItemQuote', 'quote_id'],
            'devices'                       => [Filter::FILTER_TYPE_IN, null, 'device'],
            'type'                          => [Filter::FILTER_TYPE_IN, 'poll', 'is_auto'],
            'points_type'                   => [Filter::FILTER_TYPE_IN, 'poll', 'point_system'],
            'company'                       => [Filter::FILTER_TYPE_IN, 'poll', 'company_id'],
            'executors'                     => [Filter::FILTER_TYPE_IN, 'processing', 'executor_id'],
            'moderators'                    => [Filter::FILTER_TYPE_IN, 'processing', 'moderator_id'],
            'statuses'                      => [Filter::FILTER_TYPE_IN, 'processing', 'status'],
            'processing_time_expired'       => [Filter::FILTER_TYPE_LESS, 'processing', 'process_up'],
            'from'                          => [Filter::FILTER_TYPE_MORE_OR_EQUAL, null, 'updated_at'],
            'to'                            => [Filter::FILTER_TYPE_LESS, null, 'updated_at'],
            'tags'                          => [Filter::FILTER_TYPE_IN, 'contact.tags', 'tag_id'],
            'tagsOperation'                 => [AnswerSearchQueryBuilder::FILTER_ANSWER_CLIENT_TAGS, null, null],
            //'answerTags'                    => [Filter::FILTER_TYPE_IN, 'answerTags', 'tag_id', null, true],
            'answerTags'                    => [AnswerSearchQueryBuilder::FILTER_ANSWER_TAGS, 'answerTags', 'tag_id'],
            'answerTagsOperation'           => [AnswerSearchQueryBuilder::FILTER_ANSWER_TAGS, null, null],
            'filials'                       => [Filter::FILTER_TYPE_IN, null, 'answer_filial_id'],
            'folders'                       => [Filter::FILTER_TYPE_IN, 'poll', 'folder_id'],
            //'clientFilials'                 => [Filter::FILTER_TYPE_IN, 'contact.clientFilials', 'filial_id', null, true],
            'channels'                      => [Filter::FILTER_TYPE_IN, 'send', 'channel_name'],
            //сильно замедляет связка
            'mailings'                      => [Filter::FILTER_TYPE_IN, 'send.listContact', 'mailing_list_id'],
            'hasComplaint'                  => [Filter::FILTER_TYPE_NOT_NULL, 'complaint', 'id'],
            'withComment'                   => [Filter::FILTER_TYPE_EXPRESSION, null, null, 'withComment'],
            'answer_id'                     => [Filter::FILTER_TYPE_IN, null, 'id'],
            'points'                        => [AnswerSearchQueryBuilder::FILTER_ANSWER_POINTS, 'poll', null],
            'score'                         => [AnswerSearchQueryBuilder::FILTER_ANSWER_SCORE, null, null],
            'query'                         => [AnswerSearchQueryBuilder::FILTER_ANSWER_QUERY, 'contact', null],
            'questions_filters'             => [AnswerSearchQueryBuilder::FILTER_ANSWER_QUESTIONS, null, null],
            'order_filials'                 => [Filter::FILTER_TYPE_IN, 'orders', 'filial_id'],
            'search_pollName'               => [Filter::FILTER_TYPE_LIKE, 'poll', 'name'],
            'search_name'                   => [Filter::FILTER_TYPE_LIKE, 'poll', 'name'],
            'search_filial'                 => [Filter::FILTER_TYPE_LIKE, null, null, 'filialName'],
            'search_complaint'              => [Filter::FILTER_TYPE_LIKE, 'complaint', 'text'],
            'search_executor'               => [AnswerSearchQueryBuilder::FILTER_ANSWER_USER, 'processing.executor', null],
            'search_moderator'              => [AnswerSearchQueryBuilder::FILTER_ANSWER_USER, 'processing.moderator', null],
            'search_channel'                => [Filter::FILTER_TYPE_LIKE, 'send', 'channel_name'],
            'search_device'                 => [Filter::FILTER_TYPE_LIKE, null, null, 'deviceText'],
            'search_answerTime'             => [Filter::FILTER_TYPE_LIKE, null, null, 'answerTime'],
            'search_mailing'                => [Filter::FILTER_TYPE_LIKE, 'send.listContact.mailingList', 'name'],
            'search_lang'                   => [Filter::FILTER_TYPE_LIKE, 'lang', 'name'],
            'search_passedAt'               => [Filter::FILTER_LIKE_DATETIME, null, 'updated_at'],
            'search_processingTimeAt'       => [Filter::FILTER_LIKE_DATETIME, 'processing', 'process_up'],
            'search_processingStatus'       => [AnswerSearchQueryBuilder::FILTER_ANSWER_PROCESSING_STATUS, 'processing', 'status'],
            'search_rate'                   => [AnswerSearchQueryBuilder::FILTER_ANSWER_SEARCH_SCORE, 'answerItem.question', null, null, true],
            'search_comment'                => [AnswerSearchQueryBuilder::FILTER_ANSWER_ANSWER_COMMENT, 'answerItem', null, null, true],
            'search_orderSum'               => [Filter::FILTER_TYPE_LIKE, 'orders', 'sum'],
            'search_orderNumber'            => [AnswerSearchQueryBuilder::FILTER_ANSWER_ORDER_NUMBER, 'orders', null],
            'search_orderType'              => [Filter::FILTER_TYPE_LIKE, 'orders.ordersType', 'name'],
            'search_sourceType'             => [Filter::FILTER_TYPE_LIKE, 'orders.orderSource', 'name'],
            'search_orderTime'              => [Filter::FILTER_TYPE_LIKE, 'orders', 'created_time'],
            'search_orderAddress'           => [Filter::FILTER_TYPE_LIKE, 'orders', 'address'],
            'search_hash'                   => [Filter::FILTER_TYPE_LIKE, null, 'hash_id', false, true],
            'search_client_birthday'        => [Filter::FILTER_LIKE_DATETIME, 'contact', 'birthday'],
            'search_client_addedAt'         => [Filter::FILTER_LIKE_DATETIME, 'contact', 'created_at'],
            'search_client_updatedAt'       => [Filter::FILTER_LIKE_DATETIME, 'contact', 'updated_at'],
            'search_client_last_order_date' => [Filter::FILTER_LIKE_DATETIME, 'contact.computedField', 'last_order_date'],
            'search_clientPhone'            => [Filter::FILTER_TYPE_LIKE, 'contact', 'phone'],
            'search_clientEmail'            => [Filter::FILTER_TYPE_LIKE, 'contact', 'email'],
            'search_clientName'             => [AnswerSearchQueryBuilder::FILTER_ANSWER_CONTACT, 'contact', null],
            'search_client_filials'         => [Filter::FILTER_TYPE_LIKE, null, null, 'clientFilialsSearch', true],
            'search_client_gender'          => [Filter::FILTER_TYPE_LIKE, null, null,  'clientGender'],
            'search_client_tags'            => [Filter::FILTER_TYPE_LIKE, 'contact.tags.tag', 'tag', null, true],
            'search_answer_tag'                   => [Filter::FILTER_TYPE_LIKE, 'answerTags.answerTag', 'tag', null, true],
            'search_client_personalData'    => [Filter::FILTER_TYPE_IN, null, 'user_agreement'],
            'search_client_additional'      => [AnswerSearchQueryBuilder::FILTER_ANSWER_CLIENT_CUSTOM_FIELD, 'contact', null],
            'search_client_ltv_amount'      => [Filter::FILTER_TYPE_LIKE, 'contact.computedField', 'ltv_amount'],
            'search_anketaWidget'           => [AnswerSearchQueryBuilder::FILTER_ANSWER_WIDGET, 'answer', 'widget_id'],
            'search_anketaSite'             => [AnswerSearchQueryBuilder::FILTER_ANSWER_SITE, 'answer', 'widget_id'],
            'search_anketaStatus'           => [AnswerSearchQueryBuilder::FILTER_ANSWER_STATUS, 'answer', 'status'],
            'search_anketaLink'             => [AnswerSearchQueryBuilder::FILTER_ANSWER_LINK, 'answer', 'quote_id'],
            'search_anketaQuote'             => [AnswerSearchQueryBuilder::FILTER_ANSWER_QUOTE, 'answer', 'quote_id'],
        ];
    }

    /**
     * Все возможные сортировки [поле для сортировки, таблица (null-базовая), SQL-выражение,
     * boolean - особая сортировка, boolean-нужна ли группировка, кастомный класс сортировки]
     * @return array[]
     */
    private function getSortingConfig(): array
    {
        return [
            'pollType'                    => ['is_auto', 'poll'],
            'pollName'                    => ['name', 'poll'],
            'name'                        => ['name', 'poll'],
            'passedAt'                    => ['updated_at'],
            'passed_at'                   => ['updated_at'],
            'status'                      => ['status', 'processing'],
            'anketaStatus'                => ['status'],
            'anketaLink'                  => ['name', 'linkQuotes'],
            'anketaQuote'                 => ['name', 'answerItemQuote.answerQuotes'],
            'clientName'                  => ['first_name', 'contact'],
            'clientPhone'                 => ['phone', 'contact'],
            'clientGender'                => ['clientGender'],
            'clientComputedLastOrderDate' => ['last_order_date', 'contact.computedField'],
            'clientComputedLtv'           => ['ltv_amount', 'contact.computedField'],
            'clientLtv'                   => ['ltv_amount', 'contact.computedField'],
            'clientBirthday'              => ['birthday', 'contact'],
            'clientEmail'                 => ['email', 'contact'],
            'clientAddedAt'               => ['created_at', 'contact'],
            'clientUpdatedAt'             => ['updated_at', 'contact'],
            'executorName'                => [null, null, 'executorName'],
            'moderatorName'               => [null, null, 'moderatorName'],
            'filial'                      => [ null, null, 'filialName'],
            'complaint'                   => ['text', 'complaint'],
            'orderNumber'                 => [null, null, 'orderNumber'],
            'orderSum'                    => ['sum', 'orders'],
            'orderTime'                   => ['created_time', 'orders'],
            'orderAddress'                => ['address', 'orders'],
            'orderType'                   => ['name', 'orders.ordersType'],
            'sourceType'                  => [null, null, 'sourceType'],
            'channel'                     => ['channel_name', 'send', null, true],
            'pointPercents'               => [null, null, 'pointPercents'],
            'device'                      => [null, null, 'deviceText'],
            'mailing'                     => ['name', 'send.listContact.mailingList', null, true],
            'clientFilials'               => [null, null, 'clientFilials', true],
            'clientTags'                  => [null, null, 'clientTags', true],
            'processingTimeAt'            => ['process_up', 'processing'],
            'id'                          => ['id', 'answer'],
            'lang'                        => ['name', 'lang'],
            'clientPersonalData'          => ['user_agreement', 'answer'],
            'answerTime'                  => [null, null, 'answerTime'],
            'sortingAdditional'           => [null, 'contact'],
            'comment'                     => [null, null, 'withCommentSort', true],
            'anketaWidget'                => ['name', 'mailingListSend.pollWidget'],
            'anketaSite'                  => ['a.sort_order', null, '', true, false, AnketaSiteSort::class],
        ];
    }

    /**
     * Связанные таблицы, которые могут быть использованы для фильтров и сортировки.
     * Подключаем только по необходимости.
     * [связь по полю, связь с таблицей-моделью, boolean - обратная связь]
     * @return array[]
     */
    private function getLinksConfig(): array
    {
        return [
            'poll'                                                     => ['foquz_poll_id', FoquzPoll::class],
            'poll.company'                                             => ['company_id', Company::class],
            'processing'                                               => ['poll_answer_id', FoquzPollAnswerProcessing::class, true],
            'answerTags.answerTag'                                     => ['tag_id', FoquzCompanyTag::class],
            'answerTags'                                               => ['answer_id', FoquzContactTag::class, true],
            'contact.tags'                                             => ['contact_id', FoquzContactTag::class, true],
            'contact.tags.tag'                                         => ['tag_id', FoquzCompanyTag::class],
            'contact'                                                  => ['contact_id', FoquzContact::class],
            'contact.clientFilials'                                    => ['contact_id', FoquzContactFilial::class, true],
            'contact.clientFilials.clientFilial'                       => ['filial_id', Filial::class],
            'contact.clientFilials.clientFilial.clientFilialsCategory' => ['category_id', FilialCategory::class],
            'send'                                                     => ['answer_id', FoquzPollMailingListSend::class, true],
            'send.listContact'                                         => ['contact_id', FoquzPollMailingListContact::class],
            'send.listContact.mailingList'                             => ['mailing_list_id', FoquzPollMailingList::class],
            'complaint'                                                => ['foquz_poll_answer_id', FoquzComplaint::class, true],
            'contact.computedField'                                    => ['contact_id', FoquzContactComputedField::class, true],
            'processing.moderator'                                     => ['moderator_id', User::class],
            'processing.executor'                                      => ['executor_id', User::class],
            'answerFilial.answerFilialCategory'                        => ['category_id', FilialCategory::class],
            'answerFilial'                                             => ['answer_filial_id', Filial::class],
            'orders'                                                   => ['order_id', Order::class],
            'orders.ordersType'                                        => ['delivery_type', FoquzOrderType::class],
            'orders.orderSource'                                       => ['source_type', FoquzOrderSource::class],
            'lang'                                                     => ['language', PollLang::class],
            'answerItem'                                               => ['foquz_poll_answer_id', FoquzPollAnswerItem::class, true],
            'answerItem.files'                                         => ['foquz_poll_answer_item_id', FoquzPollAnswerItemFile::class, true],
            'answerItem.question'                                      => ['foquz_question_id', FoquzQuestion::class],
            'comment'                                                  => ['foquz_poll_answer_id', FoquzPollAnswerItem::class, true],
            'mailingListSend'                                          => ['answer_id', FoquzPollMailingListSend::class, true],
            'mailingListSend.pollWidget'                               => ['widget_id', FoquzPollWidget::class],
            'answerItem.answerItemPoints'                              => ['answer_item_id', FoquzPollAnswerItemPoints::class, true],
            'answerItemQuote'                                          => ['answer_id', FoquzPollAnswerQuote::class, true],
            'answerItemQuote.answerQuotes'                             => ['quote_id', FoquzPollAnswerQuotes::class, false, "AND answerQuotes.deleted_by IS NULL"],
            'linkQuotes'                                               => ['quote_id', FoquzPollLinkQuotes::class],
        ];
    }

    /**
     * Именованные sql-выражения, которые могут быть использованы в фильтрах и сортировках.
     * @return array[]
     */
    private function getCalcFieldsConfig(): array
    {
        return [
            'clientGender'        => [
                'links'      => ['contact'],
                'expression' => "
                    CASE WHEN contact.gender = " . FoquzContact::GENDER_MALE . " 
                    THEN 'муж.'
                    WHEN contact.gender = " . FoquzContact::GENDER_FEMALE . "
                    THEN 'жен.'
                    END
                "
            ],
            'moderatorName'       => [
                'links'      => ['processing.moderator'],
                'expression' => 'IF(moderator.name = "" OR moderator.name is null, moderator.username, moderator.name)'
            ],
            'executorName'        => [
                'links'      => ['processing.executor'],
                'expression' => 'IF(executor.name = "" OR executor.name is null, executor.username, executor.name)'
            ],
            'filialName'          => [
                'links'      => ['answerFilial.answerFilialCategory'],
                'expression' => 'IF(answerFilial.category_id IS NULL, answerFilial.name, CONCAT(answerFilialCategory.name, "/", answerFilial.name))'
            ],
            'orderNumber'         => [
                'links'      => ['orders'],
                'expression' => 'IF(orders.number is null or orders.number = "", orders.id, orders.number)'
            ],
            'sourceType'          => [
                'links'      => ['orders.orderSource'],
                'expression' => 'IF(orderSource.name IS NULL, "", orderSource.name)'
            ],
            'pointPercents'       => [
                'links'      => ['poll'],
                'expression' => '(answer.points / poll.max_points) * 100'
            ],
            'deviceText'          => [
                'links'      => [],
                'expression' => 'CASE WHEN answer.device = "desktop" THEN "Десктоп" WHEN answer.device = "tablet" THEN "Планшет" WHEN answer.device = "mobile" THEN "Смартфон" ELSE "" END'
            ],
            'clientFilials'       => [
                'links'      => [],
                'expression' => '(SELECT IF(f.category_id IS NULL, f.name, CONCAT(fc.name, "/", f.name)) as filialName 
                            FROM filials f
                            LEFT JOIN foquz_contact_filial fcf ON fcf.filial_id = f.id
                            LEFT JOIN filial_category fc ON fc.id = f.category_id
                            WHERE fcf.contact_id = answer.contact_id
                            LIMIT 1)'
            ],
            'clientTags'          => [
                'links'      => [],
                'expression' => '(SELECT tag 
                            FROM foquz_company_tag fct
                            LEFT JOIN foquz_contact_tag fctg ON fctg.tag_id = fct.id
                            WHERE fctg.contact_id = answer.contact_id
                            ORDER BY tag {type_sort}
                            LIMIT 1) '
            ],
            'answerTime'          => [
                'links'      => [],
                'expression' => 'sec_to_time(UNIX_TIMESTAMP(answer.updated_at) - UNIX_TIMESTAMP(answer.created_at))'
            ],
            'clientFilialsSearch' => [
                'links'      => ['contact.clientFilials.clientFilial.clientFilialsCategory'],
                'expression' => 'IF(clientFilial.category_id IS NULL, clientFilial.name, CONCAT(clientFilialsCategory.name, "/", clientFilial.name))'
            ],
            //перенесено из старой версии
            'withComment'         => [
                'links'      => ['answerItem.question'],
                'expression' => '
                 question.main_question_type IN (0, 1, 2, 5, 15, 18, 19, 24) AND (answerItem.answer != "" OR answerItem.self_variant IS NOT NULL)
                OR
                question.main_question_type IN (7, 8, 9, 10, 11, 12, 13, 14, 17, 21, 22, 23) AND (answerItem.self_variant IS NOT NULL AND answerItem.self_variant != "")
                OR
                question.main_question_type = 7 AND (answerItem.answer LIKE \'%answer%\' OR answerItem.answer LIKE \'%self_variant%\')
                OR
                question.main_question_type = 13 AND (answerItem.detail_item LIKE \'%answer%\' OR answerItem.detail_item LIKE \'%self_variant%\')
                OR
                question.main_question_type = 11 AND (answerItem.detail_item LIKE \'%text_answer%\' OR answerItem.detail_item LIKE \'%self_variant%\')
                '
            ],
            // сортировка по количеству комментов
            'withCommentSort' => [
                'links'      => [],
                'expression' => "(
                 SELECT COUNT(*) FROM `foquz_poll_answer_item` `answerItem` 
	                LEFT JOIN `foquz_question` `question` ON answerItem.foquz_question_id=question.id
	                WHERE (question.main_question_type IN (0, 1, 2, 5, 15, 18, 19, 24) AND (answerItem.answer != '' OR answerItem.self_variant IS NOT NULL)
                    OR
                    question.main_question_type IN (7, 8, 9, 10, 11, 12, 13, 14, 17, 21, 22, 23) AND (answerItem.self_variant IS NOT NULL AND answerItem.self_variant != '')
                    OR
                    question.main_question_type = 7 AND (answerItem.answer LIKE '%answer%' OR answerItem.answer LIKE '%self_variant%')
                    OR
                    question.main_question_type = 13 AND (answerItem.detail_item LIKE '%answer%' OR answerItem.detail_item LIKE '%self_variant%')
                    OR
                    question.main_question_type = 11 AND (answerItem.detail_item LIKE '%text_answer%' OR answerItem.detail_item LIKE '%self_variant%')
                    )
                    AND answerItem.foquz_poll_answer_id = answer.id
                    
                )"
            ],

        ];
    }
}