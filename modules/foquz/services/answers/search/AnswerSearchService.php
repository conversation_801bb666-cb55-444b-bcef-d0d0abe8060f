<?php
declare(strict_types=1);

namespace app\modules\foquz\services\answers\search;

use app\helpers\DevHelper;
use app\models\company\Company;
use app\models\company\CompanyRequestProcessingSettings;
use app\models\Filial;
use app\models\User;
use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\FoquzPollStatsLink;
use app\modules\foquz\models\FoquzQuestion;
use app\modules\foquz\models\processing\FoquzPollAnswerProcessing;
use app\modules\foquz\services\answers\search\query\AnswerSearchQueryBuilder;
use RuntimeException;
use Yii;
use yii\db\ActiveQuery;
use yii\helpers\ArrayHelper;
use yii\web\ForbiddenHttpException;

class AnswerSearchService
{
    private const TIME_LIMIT_LOGGING = 100;
    private const ITEMS_ON_PAGE = 30;
    private ?Company $company;
    private ?User $user = null;
    private ?int $page;

    private AnswerSearchQueryBuilder $query;
    private string $baseTypeSelect = "polls";
    private bool $singlePoll = true;

    private bool $logging = true;
    private bool $enabledProcessing;

    private array $params;

    /**
     * Конструктор, делаем егр недоступным снаружи.
     */
    private function __construct()
    {
    }

    /**
     * Получить экземпляр по ID компании, выгрузки, по внешней ссылки и т. п.
     * @param int $companyId
     * @return AnswerSearchService
     * @throws ForbiddenHttpException
     *
     */
    public static function getInstanceByCompany(int $companyId): AnswerSearchService
    {
        $service = new self;
        $service->company = Company::findOne($companyId);
        //print("6\n");
        if (empty($service->company)) {
            throw new ForbiddenHttpException('Доступ запрещен');
        }
        //int("6\n");
        $service->query = new AnswerSearchQueryBuilder();
        //print("6\n");
        $service->setHasAuto();
        ///print("6\n");
        $service->applyCompany();
        //print("6\n");
        return $service;
    }

    /**
     * Получить экземпляр по авторизованному пользователю.
     * @param int|null $userId
     * @return AnswerSearchService
     * @throws ForbiddenHttpException
     */
    public static function getInstanceByUser(?int $userId = null): AnswerSearchService
    {
        $service = new self;
        $service->setUser($userId);
        $service->query = new AnswerSearchQueryBuilder();
        $service->setHasAuto();
        $service->applyCompany();
        $service->applyUser();
        return $service;
    }

    /**
     * Получить экземпляр по коду внешней ссылки.
     * @param string $key
     * @param int $pollID
     * @return AnswerSearchService
     * @throws ForbiddenHttpException
     */
    public static function getInstanceByLink(string $key, int &$pollID): AnswerSearchService
    {
        /** @var FoquzPollStatsLink|null $linkPoll */
        $linkPoll = FoquzPollStatsLink::find()
            ->where(['right_level' => FoquzPollStatsLink::RIGHT_READ])
            ->andWhere(['like', 'link', '/stats/' . $key])
            ->one();

        if (!$linkPoll || !$linkPoll->poll) {
            throw new ForbiddenHttpException('Доступ запрещён');
        }

        $pollID = $linkPoll->poll->id;

        if (!$linkPoll->created_by) {
            return self::getInstanceByCompany($linkPoll->poll->company_id);
        }

        return self::getInstanceByUser($linkPoll->created_by);
    }

    public function applyParams(array $params): void
    {
        $this->params = $params;
        $page = $this->params['page'] ?? 1;
        $this->page = intval($page);
        $this->applySinglePoll();
        $this->applyFilters();
        $this->applySorting();
    }

    /**
     * Проверка пользователя и компании
     * @param int|null $userId
     * @return void
     * @throws ForbiddenHttpException
     */
    private function setUser(?int $userId = null): void
    {
        /**
         * @var User|null $user
         */
        $user = null;
        if (!empty($userId)) {
            $user = User::findOne($userId);
            if (!$user) {
                throw new RuntimeException('Пользователь не найден');
            }
        } elseif (empty(Yii::$app->user->identity)) {
            throw new ForbiddenHttpException('Доступ запрещен');
        }
        $this->user = $user ?? User::findOne(Yii::$app->user->identity->getId());
        $this->company = $this->user->company;
        if (empty($this->company)) {
            throw new ForbiddenHttpException('Доступ запрещен');
        }
    }

    public function setPollID(int $pollID): void
    {
        $this->params['id'] = $pollID;
    }

    private function applyCompany(): void
    {
        $processingSettings = CompanyRequestProcessingSettings::find()->where(['company_id' => $this->company->id])->asArray()->one();

        $this->enabledProcessing = $processingSettings && $processingSettings['request_processing_enabled'];

        if ($this->baseTypeSelect == "polls") {
            $pollIds = $this->getBasePollsQuery()->select(['id'])->column();
            if (count($pollIds) == 0) {
                $pollIds = [0];
            }
            $this->query->addFilter("polls", $pollIds);
        } else {
            //нигде не используется
            $this->query->addFilter("company", $this->company->id);
        }
    }

    private function applyUser(): void
    {
        if (!$this->user) {
            return;
        }
        if ($this->user->isEditor() || $this->user->isWatcher()) {
            $editorFoldersIds = FoquzPoll::getEditorFolderIdsAll($this->user->id, $this->company->id);
            $folders = $editorFoldersIds['folders'];
            if (count($folders) == 0) {
                $folders = [0];
            }
            $polls = $this->getPollsByFolders($folders);
            if (count($polls) == 0) {
                $polls = [0];
            }
            $this->query->addFilter("polls", $polls);
        }

        if ($this->user->isExecutor()) {
            $this->query->addFilter("executors", [$this->user->id]);
            $this->query->addFilter("polls", $this->getPollsByExecutor($this->user->id));
        }

        if ($this->user->isFilialEmployee() && !$this->user->allFilials && $this->user->getUserFilials()->count() > 0) {
            $filials = ArrayHelper::getColumn($this->user->userFilials, 'filial_id');
            $this->query->addFilter('filials', $filials);
        }
    }

    private function setHasAuto(): void
    {
        $this->query->hasAuto = !empty($this->getBasePollsQuery()->andWhere(["is_auto" => 1])->one());
    }

    private function getBasePollsQuery(): ActiveQuery
    {
        return FoquzPoll::find()->where([
            'foquz_poll.company_id' => $this->company->id,
            'foquz_poll.deleted'    => 0,
            'foquz_poll.status'     => FoquzPoll::STATUS_NEW
        ])->andWhere(["or", "in_progress_answers_count > 0", "filled_answers_count > 0"]);
    }

    private function getPollsByFolders(array $folders): array
    {
        return $this->getBasePollsQuery()->andWhere(["folder_id" => $folders])->select(["id"])->column();
    }

    private ?int $pollId = null;
    private function getPollsByExecutor(int $executor): array
    {
        return $this->getBasePollsQuery()
            ->joinWith('foquzAnswer')
            ->joinWith('foquzAnswer.processing')
            ->andWhere(['foquz_poll_answer_processings.executor_id' => $executor])
            ->column();
    }

    private function applySinglePoll(): void
    {
        $filters = $this->params['filters'] ?? [];
        $id = $this->params['id'] ?? null;
        $this->singlePoll = (is_array($filters) && isset($filters["polls"]) && is_array($filters["polls"]) && count($filters["polls"]) == 1) || !empty($id);
        if (!empty($id)) {
            $this->query->addFilter("polls", $id);
            $this->pollId = (int) $id;
        } elseif ($this->singlePoll) {
            $this->pollId = (int)$filters['polls'][0];
        }
    }

    private static function applyNullFilterValue($values)
    {
        $index = array_search(0, $values);
        if ($index !== false) {
            $values[$index] = null;
        }
        return $values;
    }

    private function applyFiltersFilialCategories(array $filters, string $name, string $categoryName): array
    {
        if (!isset($filters[$categoryName]) || !is_array($filters[$categoryName]) || count($filters[$categoryName]) == 0) {
            return $filters;
        }
        if (!isset($filters[$name]) || !is_array($filters[$name])) {
            $filters[$name] = [];
        }

        $filials = Filial::find()->where([
            "category_id" => $filters[$categoryName],
            "company_id"  => $this->company->id
        ])->select("id")->column();

        unset($filters[$categoryName]);
        $filters[$name] = array_unique(array_merge($filters[$name], $filials));

        return $filters;
    }

    /**
     * Валидация фильтров по баллам за анкету
     * @param mixed $points
     * @return bool
     */
    private function validateFilterPoints(mixed $points): bool
    {
        if (empty($points['type']) || empty($points['value'])) {
            return false;
        }
        if (!isset($points['value'][0]) || !is_numeric($points['value'][0])) {
            return false;
        }
        if (!isset($points['value'][1]) || !is_numeric($points['value'][1])) {
            return false;
        }
        if ($points['value'][0] == 0 && $points['value'][1] == 100) {
            return false;
        }
        return true;
    }

    /**
     * Валидация фильтров по оценкам
     * @param mixed $score
     * @return array|null
     */
    private function validateFilterScore(mixed $score): ?array
    {
        if (!is_array($score)) {
            return null;
        }
        $filters = [];
        foreach ($score as $point => $value) {
            if (!is_array($value) || !isset($value[0]) || !isset($value[1])) {
                continue;
            }
            if (!is_numeric($value[0]) || !is_numeric($value[1])) {
                continue;
            }
            if (!is_numeric($point) && $value[0] == 0 && $value[1] == 100) {
                continue;
            }
            if (is_numeric($point)) {
                $question = FoquzQuestion::findOne($point);
                if ($question->main_question_type === FoquzQuestion::TYPE_SMILE_RATING) {
                    if ($value[0] == 1 && $value[1] == count($question->questionSmiles)) {
                        continue;
                    }
                } elseif ($question->main_question_type === FoquzQuestion::TYPE_NPS_RATING) {
                    if ($value[0] == ($question->from_one ? 1 : 0) && ($value[1] == 10)) {
                        continue;
                    }
                } else {
                    if ($value[0] == 1 && $value[1] == 5) {
                        continue;
                    }
                }
            }
            $filters[] = [$point, $value[0], $value[1]];
        }
        return count($filters) > 0 ? $filters : null;
    }

    /**
     * Фильтры по столбцам
     * @return array
     */
    private function getColumnsFiltersFromParams(): array
    {
        $columns = $this->params['search'] ?? [];
        $result = [];
        foreach ($columns as $columnName => $columnValue) {
            if ($columnName == "clientSubstrings") {
                continue;
            }
            //print_r($columnValue); exit;
            $columnValue = trim(strval($columnValue));
            if (!empty($columnValue)) {
                $result["search_" . $columnName] = $columnValue;
            }
        }

        if (isset($columns['clientSubstrings']) && is_array($columns['clientSubstrings'])) {
            foreach ($columns['clientSubstrings'] as $columnName => $columnValue) {
                if (is_string($columnValue)) {
                    $columnValue = trim($columnValue);
                    if (!empty($columnValue)) {
                        $result["search_client_" . $columnName] = $columnValue;
                    }
                } else {
                    $result["search_client_" . $columnName] = $columnValue;
                }
            }

        }
        return $result;
    }

    /**
     * Часть фильтров не включена в filters и search
     * @return array
     */
    public function getFiltersNonArrayFromParams(): array
    {
        $filters = [];

        $score = $this->params['points'] ?? null;
        if (!empty($score)) {
            $score = self::validateFilterScore($score);
            if (!empty($score)) {
                $filters['score'] = $score;
            }
        }

        $query = $this->params['query'] ?? null;
        if (!empty($query) && is_string($query)) {
            $query = trim($query);
        }
        if (!empty($query)) {
            $filters["query"] = $query;
        }

        $answerId = $this->params["answer_id"] ?? null;
        if (!empty($answerId)) {
            $filters['answer_id'] = $answerId;
        }

        $withComment = $this->params["withComment"] ?? null;
        if (!empty($withComment) && $withComment == 1) {
            $filters['withComment'] = 1;
        }

        return $filters;
    }

    /**
     * Список фильтров из параметров
     * @return array
     */
    private function getFiltersFromParams(): array
    {
        $filters = $this->params['filter'] ?? null;
        if (is_null($filters)) {
            $filters = $this->params['filters'] ?? [];
        }

        if (isset($filters["tagsOperation"]) && isset($filters["tags"]) && $filters["tagsOperation"] == 2 && count($filters["tags"]) > 1) {
            $filters["tagsOperation"] = $filters["tags"];
            unset($filters["tags"]);
        } elseif (isset($filters["tagsOperation"])) {
            unset($filters["tagsOperation"]);
        }

        if (isset($filters["answerTagsOperation"]) && isset($filters["answerTags"]) && $filters["answerTagsOperation"] == 2 && count($filters["answerTags"]) > 1) {
            $filters["answerTagsOperation"] = $filters["answerTags"];
            unset($filters["answerTags"]);
        } elseif (isset($filters["answerTagsOperation"])) {
            unset($filters["answerTagsOperation"]);
        }


        if (isset($filters["period"]["from"])) {
            $filters["from_2"] = $filters["period"]["from"];
        }
        if (isset($filters["period"]["to"])) {
            $filters["to_2"] = $filters["period"]["to"];
        }
        if (isset($filters["questionnaires"]) && is_string($filters["questionnaires"]) && $filters["questionnaires"]) {
            $filters["questionnaires"] = [$filters["questionnaires"]];
        }
        if (isset($filters["widget"]) && is_string($filters["widget"]) && $filters["widget"]) {
            $filters["widget"] = [$filters["widget"]];
        }
        if (isset($filters["links"]) && is_string($filters["links"]) && $filters["links"]) {
            $filters["links"] = [$filters["links"]];
        }
        if (isset($filters["quote"]) && is_string($filters["quote"]) && $filters["quote"]) {
            $filters["quote"] = [$filters["quote"]];
        }
        if (isset($filters['type'])) {
            $filters['type'] = boolval($filters['type']);
        }

        if (isset($filters['points']['type']) && !$this->singlePoll) {
            $filters['points_type'] = $filters['points']['type'] !== '0';
        }


        foreach ($filters as $filterName => $filter) {
            if (in_array($filterName, ["clientFilials", "clientFilialCategories", "filials", "filialCategories"])) {
                $filters[$filterName] = self::applyNullFilterValue($filter);
            }
        }
        $filters = $this->applyFiltersFilialCategories($filters, "filials", "filialCategories");
        $filters = $this->applyFiltersFilialCategories($filters, "clientFilials", "clientFilialCategories");

        if (!empty($filters['points'])) {
            if (!self::validateFilterPoints($filters['points'])) {
                unset($filters['points']);
            }
        }

        return array_merge($this->getColumnsFiltersFromParams(),
            $this->getFiltersNonArrayFromParams(),
            $filters);
    }

    private function applyFilters(): void
    {
        $filters = $this->getFiltersFromParams();

        foreach ($filters as $filterName => $filter) {
            switch ($filterName) {
                case "folders" :
                    if (is_array($filter) && count($filter) > 0) {
                        $polls = $this->getPollsByFolders($filter);
                        if (count($polls) == 0) {
                            $polls = [0];
                        }
                        $this->query->addFilter('polls', $polls);
                    }
                    break;

                case "polls":
                case "tags" :
                case "tagsOperation": //когда все теги
                case "answerTags" :
                case "answerTagsOperation": //когда все теги
                case "clientFilials" :
                case "filials":
                case "channels":
                case "executors":
                case "moderators":
                case "mailings":
                case "statuses":
                case "questionnaires":
                case "links":
                case "quote":
                case "devices":
                case "widget":
                case "search_client_additional" :
                    if (is_array($filter) && count($filter) > 0) {
                        $this->query->addFilter($filterName, $filter);
                    }
                    break;
                case "folders":
                    if (is_array($filter) && count($filter) > 0) {
                        $ids = [];
                        foreach ($filter as $folder_id) {
                            $ids[] = (int)$folder_id;
                            $children = FoquzPoll::getFolderRecursive($folder_id, 0);
                            $children = array_filter($children, fn ($item) => $item['level'] > 0);
                            $children = ArrayHelper::getColumn($children, 'id');
                            $ids = array_merge($ids, $children);
                        }
                        $this->query->addFilter($filterName, array_unique($ids));
                    }
                    break;
                case "type": //fixme через опросы
                case "points_type": //fixme  через опросы
                    $this->query->addFilter($filterName, [$filter]);
                    break;
                case "from":
                case "from_2":
                    if (is_string($filter) && $filter) {
                        $filter = strtotime($filter);
                        if ($filter) {
                            $this->query->addFilter("from", date("Y-m-d", $filter));
                        }
                    }
                    break;
                case "to":
                case "to_2":
                    if (is_string($filter) && $filter) {
                        $filter = strtotime($filter);
                        if ($filter) {
                            $this->query->addFilter("to", date("Y-m-d", $filter + 60 * 60 * 24));
                        }
                    }
                    break;
                case "processing_time_expired" :
                    $this->query->addFilter("processing_time_expired", date("Y-m-d H:i:s"));
                    break;
                case "hasComplaint":
                    if ($filter == "1") {
                        $this->query->addFilter("hasComplaint", 1);
                    }
                    break;

                case "search_pollName": //fixme нужно оптимизировать выбрав опросы
                case "search_name"://fixme нужно оптимизировать выбрав опросы
                case "search_clientPhone":
                case "search_clientEmail":
                case "search_clientName":
                case "search_client_filials":
                case "search_client_gender":
                case "search_client_tags":
                case "search_answer_tag":
                case 'search_client_ltv_amount';
                case 'query':
                case 'search_passedAt':
                case 'search_filial':
                case 'search_complaint':
                case 'search_processingTimeAt':
                case 'search_executor':
                case 'search_moderator':
                case 'search_channel':
                case 'search_device':
                case 'search_answerTime':
                case 'search_mailing':
                case 'search_lang':
                case 'search_processingStatus':
                case 'search_rate':
                case 'search_comment':
                case 'search_orderSum':
                case 'search_orderNumber':
                case 'search_orderType':
                case 'search_sourceType':
                case 'search_orderTime':
                case 'search_orderAddress':
                case 'search_hash':
                case 'withComment':
                case 'points':
                case 'answer_id':
                case 'search_anketaStatus':
                case 'search_anketaLink':
                case 'search_anketaQuote':
                case 'search_anketaWidget':
                    $this->query->addFilter($filterName, $filter);
                    break;
                case 'search_anketaSite':
                    $this->query->addFilter($filterName, ['value' => $filter, 'companyId' => $this->company->id]);
                    break;
                case 'score' :
                    if ($this->singlePoll && !empty($this->pollId)) {
                        $questionIds = FoquzQuestion::find()
                            ->where([
                                'poll_id'            => $this->pollId,
                                'is_tmp'             => 0,
                                'main_question_type' => [
                                    FoquzQuestion::TYPE_STAR_RATING,
                                    FoquzQuestion::TYPE_SEM_DIFFERENTIAL,
                                    FoquzQuestion::TYPE_GALLERY_RATING,
                                    FoquzQuestion::TYPE_SMILE_RATING,
                                    FoquzQuestion::TYPE_NPS_RATING,
                                    FoquzQuestion::TYPE_VARIANT_STAR,
                                    FoquzQuestion::TYPE_ASSESSMENT
                                ]
                            ])
                            ->select('id')->column();
                        if (!empty($questionIds)) {
                            $this->query->addFilter($filterName, ['questions' => $questionIds, 'filters' => $filter]);
                        }
                    }
                    break;

                case 'search_client_birthday' :
                case 'search_client_addedAt' :
                case 'search_client_updatedAt' :
                case 'search_client_last_order_date' :
                    $format = '%d.%m.%Y';
                    if (isset($filters[$filterName . 'Format'])) {
                        $format = $filters[$filterName . 'Format'];
                    }
                    $this->query->addFilter($filterName, ['value' => $filter, 'format' => $format, 'unixtime' => true]);
                    break;

                case "search_client_personalData":
                    $filter = mb_strtolower($filter) === 'да' ? 1 : null;
                    $this->query->addFilter($filterName, $filter);
                    break;

                case 'period':
                case 'search_client_birthdayFormat':
                case 'search_client_addedAtFormat':
                case 'search_client_updatedAtFormat':
                default:
                    break;
            }
        }
    }

    /**
     * Добавляем сортировку
     * @return void
     */
    private function applySorting(): void
    {
        $sortingAdditional = $this->params['sortAdditional'] ?? null;

        $type = SORT_DESC;

        if (!empty($sortingAdditional)) {
            $sorting = "sortingAdditional";
            $fieldId = $sortingAdditional;
            if ($sortingAdditional[0] !== '-') {
                $type = SORT_ASC;
            } else {
                $fieldId = substr($sortingAdditional, 1);
            }
            $this->query->addSortingCustom($sorting, $type, ['fieldId' => $fieldId]);
            return;
        }

        $sortingParam = $this->params['sort'] ?? null;
        if (is_null($sortingParam)) {
            $sortingParam = $this->params['order'] ?? null;
        }
        $sorting = "passedAt";

        if (!empty($sortingParam)) {
            $sorting = $sortingParam;
            if ($sortingParam[0] !== '-') {
                $type = SORT_ASC;
            } else {
                $sorting = substr($sortingParam, 1);
            }
        }

        $this->query->addSorting($sorting, $type, ['companyId' => $this->company->id]);
    }

    /**
     * Записи для страницы
     * @return array
     */
    public function page(): array
    {
        $start = microtime(true);
        $query = $this->query->buildList()->limit(self::ITEMS_ON_PAGE)->offset(($this->page - 1) * self::ITEMS_ON_PAGE);
        $data = $query->all();
        $duration = (microtime(true) - $start) * 1000;
        if ($this->logging && $duration > self::TIME_LIMIT_LOGGING) {
            Yii::info('Список анкет на страницу. ' . @$_SERVER['HTTP_HOST'] . ' Время выполнения : ' . $duration . PHP_EOL . ($query->createCommand()->getRawSql()),
                'answer_search');
        }
        //print($query->createCommand()->getRawSql());
        return $data;
    }

    /**
     * Все записи по фильтру
     * @return array
     */
    public function all(): array
    {
        $start = microtime(true);
        $query = $this->query->buildList(['id' => 'answer.id']);
        $data = $query->all();
        $duration = (microtime(true) - $start) * 1000;
        if ($this->logging && $duration > self::TIME_LIMIT_LOGGING) {
            Yii::info('Список анкет для выгрузки.  ' . @$_SERVER['HTTP_HOST'] . ' Время выполнения : ' . $duration . PHP_EOL . ($query->createCommand()->getRawSql()),
                'answer_search');
        }
        return $data;
    }

    /**
     * Общее количество записей
     * @return int
     */
    public function count(): int
    {
        $start = microtime(true);
        $query = $this->query->buildCount();
        $data = $query->scalar();
        $duration = (microtime(true) - $start) * 1000;
        if ($this->logging && $duration > self::TIME_LIMIT_LOGGING) {
            Yii::info('Количество анкет. ' . @$_SERVER['HTTP_HOST'] . '  Время выполнения : ' . $duration . PHP_EOL . ($query->createCommand()->getRawSql()),
                'answer_search');
        }
        return $data;
    }

    /**
     * Статистика по статусам обработки
     * @return array
     */
    public function processingStat(): array
    {
        $start = microtime(true);

        $query = $this->enabledProcessing ? $this->query->buildProcessingStat() : $this->query->buildCount();
        $data = $this->enabledProcessing ? ArrayHelper::map($query->all(), 'status',
            'number') : ['total' => $query->scalar()];

        $result = ['items' => [], 'total' => array_sum($data)];
        foreach (FoquzPollAnswerProcessing::getStatuses() as $id => $name) {
            $result['items'][] = [
                'id'    => $id,
                'name'  => $name,
                'total' => $data[$id] ?? 0
            ];
        }


        $duration = (microtime(true) - $start) * 1000;
        if ($this->logging && $duration > self::TIME_LIMIT_LOGGING) {
            Yii::info('Количество анкет.' . @$_SERVER['HTTP_HOST'] . '  Время выполнения : ' . $duration . PHP_EOL . ($query->createCommand()->getRawSql()),
                'answer_search');
        }
        return $result;
    }


}
