<?php

namespace app\modules\foquz\services\webhook\type;

use app\modules\foquz\models\FoquzPollAnswer;
use app\modules\foquz\models\v2\answers\Answer;
use app\modules\foquz\models\v2\answers\AnswersResponse;
use app\modules\foquz\models\Webhook;
use app\modules\foquz\services\webhook\Send;
use yii\helpers\ArrayHelper;

class NewAnswer extends Send
{
    protected FoquzPollAnswer $answer;

    public function __construct(FoquzPollAnswer $answer, Webhook $webhook)
    {
        $this->answer = $answer;
        $this->webhook = $webhook;
        $this->entityID = $answer->id;
    }

    protected function check(): bool
    {
        return FoquzPollAnswer::find()->where(['id' => $this->answer->id, 'updated_at' => $this->answer->updated_at])->exists();
    }

    protected function getData(): array
    {
        if ($this->answer->foquz_poll_id==320134 || $this->answer->foquz_poll_id==342515) { //кастомный костыль
            $data  = ['customer' => ['email'=>$this->answer->contact->email ?? null]];
            \Yii::info(['Webhook custom mindbox', $data], 'debug');
            return $data;
        }

        $model = new Answer($this->answer, new AnswersResponse(
            ['answer_id' => $this->answer->id],
            $this->answer->foquzPoll->company_id
        ));
        return ArrayHelper::toArray($model->getResponse());
    }
}
