<?php
declare(strict_types=1);

namespace app\modules\foquz\services\custom;

use app\models\company\Company;
use app\models\DictionaryElement;
use app\models\Filial;
use app\models\FilialCategory;
use app\modules\foquz\models\Channel;
use app\modules\foquz\models\FoquzCompanyTag;
use app\modules\foquz\models\FoquzContact;
use app\modules\foquz\models\FoquzContactFilial;
use app\modules\foquz\models\FoquzContactTag;
use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\FoquzPollAnswer;
use app\modules\foquz\models\FoquzPollAnswerItem;
use app\modules\foquz\models\FoquzPollMailingList;
use app\modules\foquz\models\FoquzPollMailingListContact;
use app\modules\foquz\models\FoquzPollMailingListSend;
use app\modules\foquz\models\FoquzPollShortLink;
use app\modules\foquz\models\FoquzQuestion;
use app\modules\foquz\services\ShortLinkServiceUshortener;
use Yii;
use yii\db\Exception;
use yii\helpers\ArrayHelper;
use yii\helpers\Url;

/**
 * Особый кейс для одной компании.
 * Рассылки и опросы о качестве работы отделов, список отделов определяется ответом руководителя на другой опрос.
 */
class HRManagerService
{
    private readonly ?Company $company;
    /**
     * Id руководителей сотрудника.
     * @var int[]
     */
    private array $managerIds;
    private readonly ?FoquzContact $contact;
    /**
     * Текст письма с замененными переменными
     * @var string
     */
    public string $textLetter;
    /**
     * Логи об отправке сообщения по одному на каждый филиал.
     * @var array
     */
    public array $sends;

    /**
     * Переменные, которые не применимы в тексте письма в общем блоке, так как письмо сразу на несколько филиалов.
     */
    private const VARIABLES_OF_FILIAL = [
        'FILIAL.param1',
        'FILIAL.param2',
        'FILIAL.param3',
        'FILIAL.name',
        'FILIAL.category_name',
        'scoresInterpretationRanges',
        'codes'
    ];
    private ?array $variables = null;

    /**
     * @param FoquzContact $contact
     * @param int|null $companyId
     */
    public function __construct(FoquzContact $contact, ?int $companyId = null)
    {
        if (!$companyId && isset(Yii::$app->params['hr_manager_company_id'])) {
            $companyId = (int)Yii::$app->params['hr_manager_company_id'];
        }
        $this->company = $companyId ? Company::findOne($companyId) : null;
        if ($this->company && $contact->company_id == $this->company->id) {
            $this->contact = $contact;
            $this->managerIds = $this->getManagerIds();
        } else {
            $this->contact = null;
        }
    }

    /**
     * Особая компания
     *
     * @param int $companyId
     * @return bool
     *
     */
    public static function isCompanyHR(int $companyId): bool
    {
        return isset(Yii::$app->params['hr_manager_company_id']) && Yii::$app->params['hr_manager_company_id'] == $companyId;
    }


    public static function applyCustomVariablesLetter(FoquzPollAnswer $answer, string $text): string
    {
        $vars = $answer->getVariables();
        unset($vars['fio']);
        unset($vars['codes']);
        unset($vars['scoresInterpretationRanges']);
        $names = [];
        foreach (array_keys($vars) as $var) {
            $names[] = "{" . $var . "}";
        }
        return str_replace($names, array_values($vars), $text);
    }


    /**
     * Получить параметры особой переменной в тексте письма, которая будет заменяться на ссылки на опросы по отдельным филиалам.
     *
     * @param string $text
     * @return array|null
     */
    private function getPollFilialsLinksParam(string $text): ?array
    {
        $result = null;
        if (preg_match_all("@(\{(poll_filials\|\d+\|\d+.*?[^}])})[^}]@", $text, $arr)) {
            $name = $arr[1][0];
            $var = explode("|", $arr[2][0]);
            if (count($var) < 3) {
                return null;
            }
            if (count($var) > 3) {
                $var[3] = str_replace(["{{", "}}", "&lt;", "&gt;"], ["{", "}", "<", ">"], $var[3]);
            } else {
                $var[] = '<a href="' . '{Ссылка}' . '>{filial.name}</a>';
            }
            if (count($var) > 4) {
                $var[4] = str_replace(["{{", "}}", "&lt;", "&gt;"], ["{", "}", "<", ">"], $var[4]);
            } else {
                $var[] = "< br/>";
            }
            $result = [
                "poll"      => intval($var[1]),
                "text"      => $var[3],
                "separator" => $var[4],
                "question"  => intval($var[2]),
                "name"      => $name
            ];
        }
        return $result;
    }

    /**
     * Является ли рассылка частью этого кейса.
     *
     * @param FoquzPollMailingList $list
     * @param Channel[] $channels
     * @return bool
     */
    public static function isMailingHR(FoquzPollMailingList $list, array $channels): bool
    {
        if (!isset(Yii::$app->params['hr_manager_company_id']) || $list->foquzPoll->company_id !== Yii::$app->params['hr_manager_company_id']) {
            return false;
        }
        foreach ($channels as $channel) {
            if (preg_match("@(\{(poll_filials\|\d+\|\d+.*?[^}])})[^}]@", $channel->text)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Получить список руководителей для сотрудника.
     *
     * @return array
     */
    private function getManagerIds(): array
    {
        $tags = ArrayHelper::getColumn($this->contact->contactTags, "tag.tag");

        if (!in_array("Сотрудник", $tags) /*|| in_array("Руководитель", $tags)*/) {
            return [];
        }

        $filialsId = ArrayHelper::getColumn($this->contact->filials, "id");
        if (count($filialsId) == 0) {
            return [];
        }

        //тут надо кэш fixme
        return FoquzContactFilial::find()
            ->alias("f")
            ->innerJoin(["t" => FoquzContactTag::tableName()], "f.contact_id=t.contact_id AND t.is_deleted=0")
            ->innerJoin(["ct" => FoquzCompanyTag::tableName()], "ct.id=t.tag_id AND ct.tag = 'Руководитель'")
            ->innerJoin(["fc" => FoquzContact::tableName()],
                "fc.id=f.contact_id AND fc.is_deleted=0 AND fc.company_id={$this->company->id}") //тут компания точно число
            ->where(["f.filial_id" => $filialsId])
            ->select(["fc.id"])
            ->column();
    }

    /**
     * Получить список руководителей для сотрудника.
     *
     * @param int $filialId
     * @return array
     */
    private function getManagerFilialIds(int $filialId): array
    {
        $filialsId = [$filialId];

        //тут надо кэш fixme
        return FoquzContactFilial::find()
            ->alias("f")
            ->innerJoin(["t" => FoquzContactTag::tableName()], "f.contact_id=t.contact_id AND t.is_deleted=0")
            ->innerJoin(["ct" => FoquzCompanyTag::tableName()], "ct.id=t.tag_id AND ct.tag = 'Руководитель'")
            ->innerJoin(["fc" => FoquzContact::tableName()],
                "fc.id=f.contact_id AND fc.is_deleted=0 AND fc.company_id={$this->company->id}") //тут компания точно число
            ->where(["f.filial_id" => $filialsId])
            ->select(["fc.id"])
            ->column();
    }

    /**
     * Получить ответ руководителя на конкретный вопрос в конкретном опросе.
     *
     * @param int $pollId
     * @param int $questionId
     * @param int $filiald
     * @return string
     */
    public function getAnswerFilial(int $pollId, int $questionId, int $filiald): string
    {
        $managerIds = $this->getManagerFilialIds($filiald);

        $poll = FoquzPoll::findOne($pollId);
        if (!$poll || $poll->company_id !== $this->company->id) {
            return "";
        }

        $question = FoquzQuestion::findOne($questionId);
        if (!$question || $poll->id !== $question->poll_id) {
            return "";
        }

        $varValue = FoquzPollAnswer::find()
            ->alias("a")
            ->leftJoin(["i" => FoquzPollAnswerItem::tableName()], "i.foquz_poll_answer_id=a.id")
            ->where([
                "foquz_question_id" => $questionId,
                "contact_id"        => $managerIds,
            ])
            ->orderBy("a.updated_at DESC")
            ->limit(1);
        $varValue->andWhere("i.answer IS NOT NULL ")->select(["i.answer"]);
        return $varValue->scalar();
    }

    /**
     * Получить ответ руководителя на конкретный вопрос в конкретном опросе.
     *
     * @param int $pollId
     * @param int $questionId
     * @param bool $detailItem
     * @return string
     */
    public function getAnswer(int $pollId, int $questionId, bool $detailItem = false): string
    {
        if (count($this->managerIds) == 0) {
            return "";
        }

        $poll = FoquzPoll::findOne($pollId);
        if (!$poll || $poll->company_id !== $this->company->id) {
            return "";
        }

        $question = FoquzQuestion::findOne($questionId);
        if (!$question || $poll->id !== $question->poll_id) {
            return "";
        }

        $varValue = FoquzPollAnswer::find()
            ->alias("a")
            ->leftJoin(["i" => FoquzPollAnswerItem::tableName()], "i.foquz_poll_answer_id=a.id")
            ->where([
                "foquz_question_id" => $questionId,
                "contact_id"        => $this->managerIds,
            ])
            ->orderBy("a.updated_at DESC")
            ->limit(1);
        if ($detailItem) {
            $varValue->andWhere("i.detail_item IS NOT NULL ")->select(["i.detail_item"]);
        } else {
            $varValue->andWhere("i.answer IS NOT NULL ")->select(["i.answer"]);
        }
        return $varValue->scalar();
    }

    /**
     * По классификаторам получить филиалы, где имена элементов и категорий совпадают.
     * Нужен, так как на тип вопроса Филиал нельзя выбрать несколько филиалов.
     *
     * @param string $answer
     * @param int $dictionaryId
     * @return Filial[]
     */
    private function getFilials(string $answer, int $dictionaryId): array
    {
        $elementIds = json_decode($answer);
        if (!$elementIds || !is_array($elementIds) || count($elementIds) == 0) {
            return [];
        }

        /** @var DictionaryElement[] $elements */
        $elements = DictionaryElement::find()->where([
            "dictionary_id" => $dictionaryId,
            "type"          => DictionaryElement::TYPE_ELEMENT,
            "id"            => $elementIds,
            "is_active"     => 1,
        ])->with("parent")->all();
        $filials = [];
        foreach ($elements as $element) {
            $query = Filial::find()->where([
                Filial::tableName() . ".company_id" => $this->company->id,
                Filial::tableName() . ".name"       => trim($element->title),
                Filial::tableName() . ".is_active"  => 1
            ]);
            if ($element->parent) {
                $query->leftJoin(FilialCategory::tableName(), "filials.category_id=filial_category.id")
                    ->andWhere([
                        FilialCategory::tableName() . ".company_id" => $this->company->id,
                        FilialCategory::tableName() . ".name"       => trim($element->parent->title),
                        FilialCategory::tableName() . ".is_deleted" => 0,
                    ]);
            }
            if ($filial = $query->one()) {
                $filials[] = $filial;
            }
        }
        return $filials;
    }

    /**
     * Проверить не отправляли ли в этой рассылке уже контакту.
     * И если не отправляли, создать для каждого филиала анкету и ссылку на него.
     *
     * @param Filial $filial
     * @param FoquzPollMailingListContact $listContact
     * @param Channel $channel
     * @param int $repeatId
     * @return FoquzPollMailingListSend|null
     * @throws Exception
     */
    private function createAnswerMailing(
        Filial $filial,
        FoquzPollMailingListContact $listContact,
        Channel $channel,
        int $repeatId
    ): ?FoquzPollMailingListSend {
        $send = FoquzPollMailingListSend::find()->joinWith("answer")
            ->where([
                FoquzPollMailingListSend::tableName() . ".contact_id" => $listContact->id,
                "channel_id"                                          => $channel->id,
                "repeat_id"                                           => $repeatId,
                "answer_filial_id"                                    => $filial->id
            ])
            ->one();
        if (!$send) {
            $answer = new FoquzPollAnswer;
            $answer->foquz_poll_id = $listContact->mailingList->foquz_poll_id;
            $answer->status = FoquzPollAnswer::STATUS_NEW;
            $answer->contact_id = $this->contact->id;
            $answer->auth_key = md5(time() . uniqid());
            $answer->answer_filial_id = $filial->id;
            $answer->setProcessingTimeByChannelType();
            if ($answer->save()) {
                $send = new FoquzPollMailingListSend;
                $send->channel_id = $channel->id;
                $send->repeat_id = $repeatId;
                $send->contact_id = $listContact->id;
                $send->status = FoquzPollMailingListSend::STATUS_NOT_SEND;
                $send->answer_id = $answer->id;
                $send->key = md5(time() . uniqid());
                $send->sended = null;
                $send->channel_name = $channel->name;
                $send->save();
            }
        }
        if ($send && $send->status == FoquzPollMailingListSend::STATUS_NOT_SEND) {
            return $send;
        }
        return null;
    }

    /**
     * Список общих переменных и значений для письма.
     *
     * @param FoquzPollAnswer $answer
     * @return void
     */
    private function setCommonVariablesLetter(FoquzPollAnswer $answer): void
    {
        /**
         * Тут меньше переменных, нет ссылки отписаться, шкал и QR кодов
         * письма по своим сотрудникам
         * ссылки внутри других переменных
         */
        $this->variables = $answer->getVariables();
        foreach (self::VARIABLES_OF_FILIAL as $varName) {
            //убираем переменные, индивидуальные для филиала, для этого письма не применимы
            if (isset($this->variables[$varName])) {
                unset($this->variables[$varName]);
            }
        }
        $this->variables["ФИО"] = implode(' ',
            array_filter([$this->contact->first_name, $this->contact->patronymic, $this->contact->last_name]));
        $this->variables["ФИО"] = mb_convert_case(mb_strtolower(trim($this->variables["ФИО"])), MB_CASE_TITLE);
        $this->variables["Имя"] = $this->contact->first_name;
        $this->variables["Фамилия"] = $this->contact->last_name;
        $this->variables["Отчество"] = $this->contact->patronymic;
        foreach ($this->contact->customFields as $key => $value) {
            $this->variables["client.$key"] = $value;
        }
    }

    /**
     * Заменить общие переменные на значения в тексте.
     *
     * @param string $text
     * @return string
     */
    private function applyCommonVariables(string $text): string
    {
        $names = [];
        foreach (array_keys($this->variables) as $var) {
            $names[] = "{" . $var . "}";
        }
        return str_replace($names, array_values($this->variables), $text);
    }

    /**
     * Заменить переменные по филиалам в тексте.
     *
     * @param string $text
     * @param FoquzPollMailingListSend $send
     * @param Filial $filial
     * @return string
     */
    private function applyFilialVariables(string $text, FoquzPollMailingListSend $send, Filial $filial): string
    {
        $vars = [
            'FILIAL.param1'        => $filial->param1 ?? '',
            'FILIAL.param2'        => $filial->param2 ?? '',
            'FILIAL.param3'        => $filial->param3 ?? '',
            'FILIAL.name'          => $filial->name ?? '',
            'FILIAL.category_name' => $filial->category ? $filial->category->name : ''
        ];
        $link = Url::to(['/foquz/default/anonymous', 'id' => $send->key], true);
        $link = str_replace("foquz.ru", $this->company->alias, $link);
        $vars["Ссылка"] = $link;
        if (stristr($text, '{Короткая ссылка}') !== false) {
            $shortLink = ShortLinkServiceUshortener::getShortLinkFromUShortener($link);

           /* $shortLink = FoquzPollShortLink::find()->where(['link' => $link])->one();
            if (!$shortLink) {
                $shortLink = FoquzPollShortLink::create($link);
            }
            $vars["Короткая ссылка"] = $this->company->shortLink . $shortLink->code;*/

            $vars["Короткая ссылка"] = $this->company->shortLink . $shortLink;
        }
        $names = [];
        foreach (array_keys($vars) as $var) {
            $names[] = "{" . $var . "}";
        }
        return str_replace($names, array_values($vars), $text);
    }

    /**
     * Получаем все анкеты, которые нужно разослать.
     * Полный текст переменной, которая меняется на ссылки на анкеты по филиалам.
     * Формируем текст письма, заменяя все переменные.
     *
     * @param Filial[] $filials
     * @param FoquzPollMailingListContact $listContact
     * @param Channel $channel
     * @param int $repeatId
     * @param array $varFilialsLinks
     * @return void
     * @throws Exception
     */
    private function createAnswersMailing(
        array $filials,
        FoquzPollMailingListContact $listContact,
        Channel $channel,
        int $repeatId,
        array $varFilialsLinks
    ): void {
        $varFilialsLinkValue = null;
        $varFilialsLinkValueText = null;
        $varFilialsLinkValueSeparator = null;
        $sends = [];
        foreach ($filials as $filial) {
            $send = $this->createAnswerMailing($filial, $listContact, $channel, $repeatId);
            if ($send) {
                $sends[] = $send;
                if (is_null($this->variables)) {
                    $this->setCommonVariablesLetter($send->answer);
                }
                if (is_null($varFilialsLinkValueText)) {
                    $varFilialsLinkValueText = $this->applyCommonVariables($varFilialsLinks["text"]);
                    $varFilialsLinkValueSeparator = $this->applyCommonVariables($varFilialsLinks["separator"]);
                }

                if (!is_null($varFilialsLinkValue)) {
                    $varFilialsLinkValue .= $this->applyFilialVariables($varFilialsLinkValueSeparator, $send, $filial);
                } else {
                    $varFilialsLinkValue = "";
                }
                $varFilialsLinkValue .= $this->applyFilialVariables($varFilialsLinkValueText, $send, $filial);
            }
        }
        $this->sends = $sends;

        if (count($this->sends) > 0) {
            $textLetter = $channel->text;
            $textLetter = str_replace($varFilialsLinks["name"], $varFilialsLinkValue, $textLetter);
            $this->textLetter = $this->applyCommonVariables($textLetter);
        }
    }

    /**
     * Все проверки, нужно ли отправлять письмо клиенту.
     * Формирование письма со всеми нужными ссылками.
     *
     * @param Channel $channel
     * @param FoquzPollMailingListContact $listContact
     * @param int $repeatId
     * @return bool
     * @throws Exception
     */
    public function needSendMailing(Channel $channel, FoquzPollMailingListContact $listContact, int $repeatId): bool
    {
        if (count($this->managerIds) == 0) {
            return false;
        }
        $varFilialsLinks = $this->getPollFilialsLinksParam($channel->text);
        if (!$varFilialsLinks) {
            return false;
        }

        $question = FoquzQuestion::findOne($varFilialsLinks["question"]);
        if (!$question
            || $question->main_question_type !== FoquzQuestion::TYPE_DICTIONARY
            || !$question->dictionary_id) {
            return false;
        }


        $answer = $this->getAnswer($varFilialsLinks["poll"], $question->id, true);
        if (!$answer) {
            return false;
        }

        $filials = $this->getFilials($answer, $question->dictionary_id);
        $this->createAnswersMailing($filials, $listContact, $channel, $repeatId, $varFilialsLinks);

        if (count($this->sends) > 0) {
            return true;
        }
        return false;
    }
}



