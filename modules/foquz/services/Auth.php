<?php

namespace app\modules\foquz\services;

use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\FoquzPollStatsLink;
use Yii;
use yii\filters\auth\QueryParamAuth;
use yii\web\ForbiddenHttpException;

trait Auth
{
    /**
     * Авторизует пользователя по токену или хешу ссылки
     * @throws ForbiddenHttpException
     * @return int
     */
    public function authByHashOrToken(): int
    {
        $accessToken = Yii::$app->request->get('access-token');
        $linkKey = Yii::$app->request->get('link') ?: Yii::$app->request->get('link-key');
        if (is_string($accessToken) && !empty($accessToken) && $accessToken !== 'default') {
            Yii::$app->user->loginByAccessToken($accessToken, QueryParamAuth::class);
            if (!Yii::$app->user->isGuest && !empty(Yii::$app->user->identity->company->id)) {
                return Yii::$app->user->identity->company->id;
            }
        } elseif (is_string($linkKey) && strlen($linkKey) === 32) {
            $linkPollID = FoquzPollStatsLink::find()
                ->select('foquz_poll_id')
                ->where(['right_level' => FoquzPollStatsLink::RIGHT_READ])
                ->andWhere(['like', 'link', '/stats/' . $linkKey])
                ->scalar();
            if ($linkPollID && $poll = FoquzPoll::find()->where(['id' => $linkPollID])->one()) {
                return $poll->company_id;
            }
        }
        throw new ForbiddenHttpException('Доступ запрещен');
    }
}