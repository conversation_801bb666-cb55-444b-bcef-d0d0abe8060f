<?php

namespace app\modules\foquz\queue;

use app\modules\foquz\models\FoquzPollAnswerDeleted;
use app\modules\foquz\models\quotes\FoquzPollLinkQuotes;
use app\modules\foquz\services\quotes\QuoteService;
use Yii;
use yii\base\BaseObject;
use yii\helpers\Json;
use yii\queue\JobInterface;
use app\components\RabbitMQComponent;

class LinkQuoteLimitRefreshJob extends BaseObject implements JobInterface
{
    public array $answer_ids = [];

    public function execute($queue)
    {
        $pollLinksData = [];
        foreach ($this->answer_ids as $answerId) {
            $answer = FoquzPollAnswerDeleted::findOne(['answer_id' => $answerId]);
            if ($answer && $answer->data) {
                $data = Json::decode($answer->data);
                $quoteId = $data['quote_id'] ?? null;
                if ($quoteId) {
                    $link = FoquzPollLinkQuotes::findOne($quoteId);
                    if ($link && $link->limit) {
                        if ($link->answers_count >= $link->limit) {
                            $link->is_answer_limited = true;
                        } else {
                            $link->is_answer_limited = false;
                        }
                        $link->save();
                        // данные для синхронизации с виджетами
                        $pollLinksData[] = [
                            'poll_link_id' => $link->id,
                            'is_active' => (int)$link->active,
                            'datetime_end' => $link->datetime_end,
                            'is_answer_limited' => (int)$link->is_answer_limited,
                        ];
                    }
                }
            }
        }

        if (isset(Yii::$app->rabbit) && count($pollLinksData)) {
            /** @var RabbitMQComponent $rabbit */
            $rabbit = Yii::$app->rabbit;
            $rabbit->queue('widget.poll_link_quotes')
                ->type('sync_settings')
                ->push($pollLinksData);
        }
    }
}