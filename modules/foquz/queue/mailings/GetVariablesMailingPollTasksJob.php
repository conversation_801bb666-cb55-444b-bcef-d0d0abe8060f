<?php

namespace app\modules\foquz\queue\mailings;

use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\FoquzPollMailingList;
use app\modules\foquz\models\FoquzPollMailingListSend;
use app\modules\foquz\services\ShortLinkServiceUshortener;
use Da\QrCode\QrCode;
use Pi<PERSON><PERSON>er\Barcode\BarcodeGeneratorPNG;
use Yii;
use yii\base\BaseObject;
use yii\base\Exception;
use yii\helpers\FileHelper;
use yii\helpers\Url;
use yii\queue\amqp_interop\Queue;
use yii\queue\JobInterface;

class GetVariablesMailingPollTasksJob extends BaseObject implements JobInterface
{
    public string $listKey;
    public array $variables;
    public int $sendId;

    private const LOG_CATEGORY = 'yii\queue\mailing_list\StartMailingPollTasksJob';
    private ?FoquzPollMailingList $list = null;

    private function log(string $text): void
    {
        $message = 'send=' . ($this->sendId ?? 'null');
        if (!empty($this->list)) {
            $model = $this->list;
            $message .= ' ' . $model->foquzPoll->company->alias . ' poll=' . $model->foquz_poll_id . ' ' . $model->foquzPoll->name . '  list=' . $model->id . ' ' . $model->name . ' ';
        }
        $message .= ' ' . $text;
        Yii::info($message, self::LOG_CATEGORY);
        print($message . PHP_EOL);
    }

    public function execute($queue): bool
    {
        if (empty($this->sendId) || empty($this->listKey)) {
            $this->log('Некорректно переданы параметры');
            return false;
        }

        $send = FoquzPollMailingListSend::findOne($this->sendId);
        if (empty($send)) {
            $this->log('Некорректно переданы параметры');
            return false;
        }

        $this->list = $send->contact->mailingList;
        if (empty($this->list) || $this->listKey !== $this->list->redis_key || $this->list->status !== FoquzPollMailingList::STATUS_STARTED) {
            $this->log('Рассылка остановлена');
        }

        $result = [];
        foreach ($this->variables as $variable) {
            $value = match ($variable) {
                'QR' => self::varQrCode($send->key, $this->list->foquzPoll->company->alias),
                'Промокод' => $this->varPromoCode($send),
                'Штрихкод промокода' => $this->varBarPromoCode($send, $this->list->foquzPoll->company->alias),
                'Шкала оценок' => self::varScale($send->key, $this->list->foquzPoll),
                'Короткая ссылка' => self::varShortLink($send->key, $this->list->foquzPoll->company->alias),
                default => null
            };
            if (!is_null($value)) {
                $result[$variable] = $value;
            }
        }

        /** @var Queue $queue */
        /** @noinspection PhpUndefinedFieldInspection */
        $queueMailingExternal = Yii::$app->queue_mailings_out;
        $queueMailingExternal->priority(8)->push([
            'job'       => 'invites\\SendPollContactJob',
            'id'        => $send->contact->id,
            'sendId'    => $send->id,
            'variables' => $result,
            'list_key'  => $this->list->redis_key,
        ]);


        return true;
    }

    private static function varScale(string $key, FoquzPoll $poll): string
    {
        $ratingScale = '';
        $firstRatingQuestion = $poll->getFirstRatingQuestion();
        if ($firstRatingQuestion) {
            $npsRatingSetting = $firstRatingQuestion->npsRatingSetting;
            $smiles = [];
            if ($firstRatingQuestion->questionSmiles) {
                foreach ($firstRatingQuestion->questionSmiles as $smile) {
                    $smiles[] = Url::base(true) . $smile->smile_url;
                }
            }
            $link = self::varLink($key, $poll->company->alias);
            $ratingScale = Yii::$app->controller->renderPartial('@app/mail/templates/foquz-mail-rating.php', [
                'questionName'   => $firstRatingQuestion->description ? $firstRatingQuestion->description : $firstRatingQuestion->name,
                'questionType'   => $firstRatingQuestion->main_question_type,
                'starsCount'     => $firstRatingQuestion->starRatingOptions ? $firstRatingQuestion->starRatingOptions->count : null,
                'smilesCount'    => count($firstRatingQuestion->questionSmiles),
                'smileType'      => $firstRatingQuestion->smile_type,
                'smiles'         => $smiles,
                'fromOne'        => $firstRatingQuestion->from_one,
                'pollLink'       => $link,
                'assessmentType' => 0,
                'npsRating'      => $npsRatingSetting ? [
                    'design'            => $npsRatingSetting->design,
                    'end_point_color'   => $npsRatingSetting->end_point_color,
                    'start_point_color' => $npsRatingSetting->start_point_color,
                ] : null,
            ]);
        }
        return $ratingScale;
    }

    private ?string $promoCode = null;

    private function varBarPromoCode(FoquzPollMailingListSend $send, string $alias): string
    {
        $code = $this->varPromoCode($send);
        if (empty($code)) {
            return $code;
        }
        $key = md5($code);
        $path = '/uploads/br/' . substr($key, 0, 2) . '/' . substr($key, 2, 2);
        $brLink = $path . '/' . $key . '.png';
        $path = Yii::getAlias('@app/web' . $path);
        $brFile = $path . '/' . $key . '.png';
        if (!file_exists($brFile)) {
            FileHelper::createDirectory($path);
            $redColor = [0, 0, 0];
            $generator = new BarcodeGeneratorPNG();
            file_put_contents($brFile, $generator->getBarcode($code, $generator::TYPE_CODE_128, 3, 50, $redColor));
        }
        $url = str_replace("foquz.ru", $alias, Url::to([$brLink]));
        return '<img src="' . $url . '">';
    }

    private function varPromoCode(FoquzPollMailingListSend $send): string
    {
        if (!is_null($this->promoCode)) {
            return $this->promoCode;
        }
        $code = $send->channel->getCode($send->contact->mailingList->foquzPoll, $send->answer) ?? '';
        $this->promoCode = trim($code, " \t\n\r");
        return $this->promoCode;
    }

    /**
     * @throws Exception
     */
    private static function varQrCode(string $key, string $alias): string
    {
        $path = '/uploads/qr/' . substr($key, 0, 2) . '/' . substr($key, 2, 2);
        $qrLink = $path . '/' . $key . '.png';
        $path = Yii::getAlias('@app/web' . $path);
        $qrFile = $path . '/' . $key . '.png';
        if (!file_exists($qrFile)) {
            FileHelper::createDirectory($path);
            $qrCode = (new QrCode(Url::to(['/foquz/default/anonymous', 'id' => $key], 'https')))
                ->useEncoding('UTF-8')
                ->setLogoWidth(60)
                ->setSize(300)
                ->setMargin(5);
            $qrCode->writeFile($qrFile);
        }
        $link = Url::to([$qrLink]);
        $link = str_replace("foquz.ru", $alias, $link);
        return '<img src="' . $link . '">';
    }

    private static function varLink(string $key, string $alias): string
    {
        return str_replace("foquz.ru", $alias, Url::to(['/foquz/default/anonymous', 'id' => $key], true));
    }

    private static function varShortLink(string $key, string $alias): string
    {
        $link = self::varLink($key, $alias);

        $shortLink = ShortLinkServiceUshortener::getShortLinkFromUShortener($link);

        if (!empty($poll->company)) {
            $shortLink = $poll->company->shortLink . $shortLink;
        } else {
            $shortLink = Yii::$app->params['short_link'] . $shortLink;
        }

        return $shortLink;
    }
}