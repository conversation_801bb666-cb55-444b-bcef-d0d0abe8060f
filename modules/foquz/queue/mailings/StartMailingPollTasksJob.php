<?php

namespace app\modules\foquz\queue\mailings;

use app\modules\foquz\models\FoquzPollMailingList;
use app\modules\foquz\models\FoquzPollMailingListContact;
use Yii;
use yii\base\BaseObject;
use yii\queue\amqp_interop\Queue;
use yii\queue\JobInterface;

/**
 * Запуск рассылки
 */
class StartMailingPollTasksJob extends BaseObject implements JobInterface
{
    public int $id;
    public string $queueJobUID;
    public string $listKey;
    public const LOG_CATEGORY = 'yii\queue\mailing_list\StartMailingPollTasksJob';
    private ?FoquzPollMailingList $list = null;

    private function log(string $text): void
    {
        $message = 'list=' . $this->id . ' uid=' . $this->queueJobUID;
        if (!empty($this->list)) {
            $model = $this->list;
            $message .= ' ' . $model->foquzPoll->company->alias . ' poll=' . $model->foquz_poll_id . ' ' . $model->foquzPoll->name . '  listName=' . $model->name . ' ';
        }
        $message .= ' ' . $text;
        Yii::info($message, self::LOG_CATEGORY);
        print($message . PHP_EOL);
    }

    public function execute($queue): bool
    {
        if (empty($this->id) || empty($this->queueJobUID) || empty($this->listKey)) {
            return false;
        }

        $this->list = FoquzPollMailingList::find()
         //   ->with(["listContacts"])
            ->where(['foquz_poll_mailing_list.id' => $this->id])
            ->one();

        if (empty($this->list)) {
            $this->log('Рассылка не найдена');
            return false;
        }

        if ($this->list->queue_job_uid !== $this->queueJobUID || $this->list->redis_key !== $this->listKey) {
            $this->log('Задача отменена');
            return false;
        }

        if ($this->list->status !== FoquzPollMailingList::STATUS_STARTED) {
            $this->log('Рассылка остановлена');
            return false;
        }

        $listContacts = FoquzPollMailingListContact::find()
            ->where(["mailing_list_id" => $this->list->id])
            ->select(['id'])
            ->column();

        $this->log(sprintf('Постановка задач по контактам. Всего контактов: %d', count($listContacts)));
        /** @var Queue $queue */
        /** @noinspection PhpUndefinedFieldInspection */
        $queueMailingExternal = Yii::$app->queue_mailings_out;
        foreach ($listContacts as $listContactId) {
            $queueMailingExternal->priority(mt_rand(2, 7))->push([
                'job'      => 'invites\\SendPollContactJob',
                'id'       => $listContactId,
                'list_key' => $this->list->redis_key,
            ]);
        }
        $this->log('Все задачи поставлены в очередь');

        return false;
    }
}