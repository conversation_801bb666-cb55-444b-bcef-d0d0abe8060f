<?php

namespace app\modules\foquz\queue;

use Yii;
use yii\base\BaseObject;
use yii\db\Exception;
use yii\httpclient\Client;

class ShortLinksUpdateBase extends BaseObject
{

    public function addLinkToUrlShortener(string $url, string $shortUrl)
    {
        $client = new Client();
        $response = $client->createRequest()
            ->setMethod('POST')
            ->setUrl(Yii::$app->params['urlShortenerHostPort'] . '/api/v1/add?access_token=' . urlencode(Yii::$app->params['urlShortenerAuthKey']))
            ->setFormat(Client::FORMAT_JSON)
            ->setData([
                'url' => $url,
                'short' => $shortUrl
            ])
            ->send();

        if (!$response->isOk) {
            throw new Exception('Service urlshortener returns nonOK answer: ' . $response->getContent());
        }
    }

    protected function logInfo(string $msg)
    {
        echo $msg . PHP_EOL;
        Yii::info($msg);
    }

    protected function logWarning(string $msg)
    {
        echo $msg . PHP_EOL;
        Yii::warning($msg);
    }

    protected function logError(string $msg)
    {
        echo $msg . PHP_EOL;
        Yii::error($msg);
    }
}