<?php

namespace app\modules\foquz\queue;


use Yii;
use yii\db\Query;
use yii\queue\JobInterface;

class ShortLinksFilialUpdateJob extends ShortLinksUpdateBase implements JobInterface
{
    public int $batchSize = 1000;
    public int $lastProcessedId = 0;
    public int $counter = 1;

    // Задержка между заданиями (сек)
    private const DELAY = 20;

    public function execute($queue)
    {
        $records = (new Query())
            ->select(['id', 'key'])
            ->from('filial_poll_key')
            ->where(['and',
                ['>', 'id', $this->lastProcessedId],
                ['is not', 'key', null],
                ['is', 'short_link', null]
            ])
            ->orderBy('id' )
            ->limit($this->batchSize)
            ->all();

        $ids = array_column($records, 'id');
        if (!count($ids)) {
            $this->logInfo('Обновление коротких ссылок у филиалов завершено');
            return;
        }
        $this->logInfo(
            'Обновление коротких ссылок у филиалов, записи от: '. (($this->counter-1) * $this->batchSize) . ' по: ' . ($this->counter * $this->batchSize)
        );

        foreach ($records as $filial) {
            $shortLink = (new Query())
                ->select(['id', 'code', 'link'])
                ->from('foquz_poll_short_links')
                ->where('link LIKE :pattern', [':pattern' => '%/p/' . $filial['key']])
                ->one();
            if (!$shortLink) {
                $this->logWarning('Для филиала ' . $filial['id'] . ' не найдена короткая ссылка');
                continue;
            }

            $transaction = Yii::$app->db->beginTransaction();
            try {
                Yii::$app->db->createCommand()
                    ->update('filial_poll_key',
                        ['short_link' => $shortLink['code']],
                        ['id' => $filial['id']]
                    )->execute();

                //file_put_contents(\Yii::$app->getRuntimePath() . '/filials.txt', $filial['id'] . ': ' . $shortLink['code'] . PHP_EOL, FILE_APPEND);

                // send links to urlshortener
                $this->addLinkToUrlShortener($shortLink['link'], $shortLink['code']);

                $this->logInfo('Ссылка для филиала ' . $filial['id'] . ' обновлена и успешно перенесена в сокращатель');
                $transaction->commit();
            } catch (\Throwable $e) {
                $this->logError($e->getMessage());
                $transaction->rollBack();
            }
        }
        $this->counter++;

        $this->lastProcessedId = max($ids);
        $nextJob = new self([
            'batchSize' => $this->batchSize,
            'lastProcessedId' => $this->lastProcessedId,
            'counter' => $this->counter,
        ]);
        Yii::$app->rabbit_queue->delay(self::DELAY)->push($nextJob);
    }
}