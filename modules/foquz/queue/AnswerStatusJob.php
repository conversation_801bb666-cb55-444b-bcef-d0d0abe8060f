<?php

namespace app\modules\foquz\queue;

use app\modules\foquz\models\FoquzPollAnswer;
use Yii;
use yii\base\BaseObject;
use yii\httpclient\Client;
use yii\queue\JobInterface;

class AnswerStatusJob extends BaseObject implements JobInterface
{
    public $id;
    public $status;
    public $key;
    public $widget_id;
    public $updated_at;

    public function execute($queue)
    {
        if (
            empty($this->key) ||
            empty($this->widget_id) ||
            empty($this->status) ||
            empty($this->id) ||
            empty(Yii::$app->params['widgetsUrl'])
        ) {
            return;
        }

        $key = $this->key;
        echo "Send answer status to widget service\n";
        $client = new Client();
        $response = $client->createRequest()
            ->setMethod('POST')
            ->setUrl(Yii::$app->params['widgetsUrl'] . '/v1/answer/status?authKey=' . Yii::$app->params['widgetsAuthKey'])
            ->setHeaders(['Content-Type' => 'application/json'])
            ->setData(['key' => $key, 'status' => $this->status, 'updated_at' => $this->updated_at])
            ->send();

        if (!$response->isOk && $response->statusCode !== '404') {
            Yii::error('Error sending answer status to widget service');
            $queue
                ->delay(60 * 2)
                ->push(new self([
                    'id' => $this->id,
                    'status' => $this->status,
                    'updated_at' => $this->updated_at,
                ]));
        } elseif (!$response->isOk) {
            Yii::error('Error sending answer status to widget service:' .  $response->content);

        }
    }
}
