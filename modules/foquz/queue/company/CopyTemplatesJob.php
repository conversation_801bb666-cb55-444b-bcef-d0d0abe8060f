<?php

namespace app\modules\foquz\queue\company;

use app\models\company\Company;
use app\modules\foquz\models\FoquzPoll;
use Yii;
use yii\base\BaseObject;
use yii\queue\JobInterface;

class CopyTemplatesJob extends BaseObject implements JobInterface
{
    public int $company_id;
    public int $company_user_id;


    public function execute($queue): bool
    {
        if (Yii::$app->params['template_company_id']) {
            $tCompany = Company::findOne(Yii::$app->params['template_company_id']);
            if (count($tCompany->designTemplates) > 0) {
                $tCompany->copyDesignTemplates($this->company_id);
            }
        }

        $templatePolls = FoquzPoll::find()
            ->where([
                'company_id' => Yii::$app->params['template_company_id'],
                'status' => FoquzPoll::STATUS_NEW,
                'is_tmp' => 0,
                'deleted' => 0,
                'is_active' => 1,
            ])->orderBy('id');


        if (Yii::$app->params['template_company_folder_id']) {
            $templatePolls->andWhere(['folder_id' => Yii::$app->params['template_company_folder_id']]);
        }

        $templatePolls = $templatePolls->all();

        $companyTemplatesFolder = FoquzPoll::find()->where([
            'company_id' => $this->company_id,
            'is_folder' => true,
            'name' => 'Шаблоны'
        ])->one();

        if (!$companyTemplatesFolder) {
            $companyTemplatesFolder = new FoquzPoll([
                'created_at' => time(),
                'updated_at' => time(),
                'created_by' => $this->company_user_id,
                'updated_by' => $this->company_user_id,
                'name' => 'Шаблоны',
                'is_tmp' => false,
                'status' => FoquzPoll::STATUS_NEW,
                'company_id' => $this->company_id,
                'is_folder' => true,
            ]);
            $companyTemplatesFolder->save();
        }

        $this->copyPolls($templatePolls, $this->company_user_id, $this->company_id, $companyTemplatesFolder->id, true);

        return true;
    }
}
