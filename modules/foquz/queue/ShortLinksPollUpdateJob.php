<?php

namespace app\modules\foquz\queue;

use Yii;
use yii\db\Query;
use yii\queue\JobInterface;

class ShortLinksPollUpdateJob extends ShortLinksUpdateBase implements JobInterface
{
    public int $batchSize = 1000;
    public int $lastProcessedId = 0;
    public int $counter = 1;

    // Задержка между заданиями (сек)
    private const DELAY = 20;

    public function execute($queue)
    {
        $records = (new Query())
            ->select(['id', 'key'])
            ->from('foquz_poll')
            ->where(['and',
                ['>', 'id', $this->lastProcessedId],
                ['is not', 'key', null],
                ['is', 'short_link', null]
            ])
            ->orderBy('id' )
            ->limit($this->batchSize)
            ->all();

        $ids = array_column($records, 'id');

        if (!count($ids)) {
            $this->logInfo('Обновление коротких ссылок у опросов завершено');
            return;
        }
        $this->logInfo(
            'Обновление коротких ссылок у опросов, записи от: '. (($this->counter-1) * $this->batchSize) . ' по: ' . ($this->counter * $this->batchSize)
        );

        foreach ($records as $poll) {
            $shortLink = (new Query())
                ->select(['id', 'code', 'link'])
                ->from('foquz_poll_short_links')
                ->where('link LIKE :pattern', [':pattern' => '%/p/' . $poll['key']])
                ->one();

            if (!$shortLink) {
                $this->logError('Для опроса ' . $poll['id'] . ' не найдена короткая ссылка');
                continue;
            }

            $transaction = Yii::$app->db->beginTransaction();
            try {
                Yii::$app->db->createCommand()
                    ->update('foquz_poll',
                        ['short_link' => $shortLink['code']],
                        ['id' => $poll['id']]
                    )->execute();

                //file_put_contents(\Yii::$app->getRuntimePath() . '/polls.txt', $poll['id'] . ': ' . $shortLink['code'] . PHP_EOL, FILE_APPEND);

                // send links to urlshortener
                $this->addLinkToUrlShortener($shortLink['link'], $shortLink['code']);

                $this->logInfo('Ссылка для опроса ' . $poll['id'] . ' обновлена и успешно перенесена в сокращатель');
                $transaction->commit();

            } catch (\Throwable $e) {
                $this->logError($e->getMessage());
                $transaction->rollBack();
            }
        }
        $this->counter++;


        $this->lastProcessedId = max($ids);
        $nextJob = new self([
            'batchSize' => $this->batchSize,
            'lastProcessedId' => $this->lastProcessedId,
            'counter' => $this->counter,
        ]);
        Yii::$app->rabbit_queue->delay(self::DELAY)->push($nextJob);
    }
}