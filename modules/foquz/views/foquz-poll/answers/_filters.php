<!-- ko if: filtersController.valueFilters.hasVisibleFilters -->
<div class="f-card__section reviews-page-filters">

  <div class="reviews-page-filters__container" data-bind="using: filtersController">

    <!-- ko foreach: { data: valueFilters.list, as: 'item' } -->

    <!-- ko if: item.id === 'period' -->
    <div class="reviews-filter reviews-filter--period">
      <div class="form-group dense-form-group">
        <label class="form-label"><?= \Yii::t('main', 'Период') ?></label>
        <period-picker params="value: item.filter.value, autosize: true, allowClear: true, ranges: true"></period-picker>
      </div>
    </div>
    <!-- /ko -->

    <!-- ko if: item.id === 'moderators' -->
    <div class="reviews-filter reviews-filter--moderator">
      <div class="form-group dense-form-group">
        <label class="form-label"><?= \Yii::t('answers', 'Модератор') ?></label>

        <moderators-select params="value: item.filter.value,
                  url: $root.moderatorsDirectoryUrl,
                  multiple: true,
                  dense: true, fixedOption: {
                    id: CURRENT_USER.id,
                    text: _t('Вы')
                  }"></moderators-select>
      </div>
    </div>
    <!-- /ko -->

    <?php if (!isset($executor) || !$executor) : ?>
      <!-- ko if: item.id === 'executors' -->
      <!-- ko if: $root.directories.executors.loaded -->
      <div class="reviews-filter reviews-filter--executor">
        <div class="form-group dense-form-group">
          <label class="form-label"><?= \Yii::t('answers', 'Исполнитель') ?></label>

          <select multiple data-bind="
            selectedOptions: item.filter.value,
            allowUnset: true,
            lazySelect2: {
                wrapperCssClass: 'select2-container--form-control',
                dropdownCssClass: 'dense-form-group__dropdown',
                containerCss: { 'min-width': '120px' },
                templateResult: $root.userResultTemplate,
            }
          " data-placeholder="<?= \Yii::t('answers', 'Все исполнители') ?>">
            <!-- ko foreach: $root.directories.executors.data -->
            <option data-bind="value: '' + $data.id, text: $data.name, attr: {'data-userpic': $data.avatar ? $data.avatar : ''}">
            </option>
            <!-- /ko -->
          </select>
        </div>
      </div>
      <!-- /ko -->
      <!-- /ko -->
    <?php endif; ?>

    <!-- ko if: item.id === 'statuses' -->
    <!-- ko if: $root.currentStats -->
    <div class="reviews-filter reviews-filter--status">
      <div class="form-group dense-form-group">
        <label class="form-label"><?= \Yii::t('answers', 'Статус обработки') ?></label>

        <select multiple data-bind="
            selectedOptions: item.filter.value,
            allowUnset: true,
            options: $root.currentStats,
            optionsText: 'name',
            optionsValue: 'id',
            select2: {
                wrapperCssClass: 'select2-container--form-control',
                dropdownCssClass: 'dense-form-group__dropdown',
                containerCss: { 'min-width': '91px' }
            }
        " data-placeholder="<?= \Yii::t('answers', 'Все статусы') ?>"></select>
      </div>
    </div>
    <!-- /ko -->
    <!-- /ko -->

    <!-- ko if: $root.hasFilials() && $root.directories.filials.loaded() -->
    <!-- ko if: item.id === 'filials' -->
    <div class="reviews-filter reviews-filter--filial" data-bind="log: 'filials'">
      <div class="form-group dense-form-group">
  
        <label class="form-label"><?= \Yii::t('main', 'Филиал') ?></label>


        <div>
          <fc-select class="categorized" params="inline: true, 
            options: $root.directories.filials.list, 
            value: item.filter.value, 
            placeholder: 'Все филиалы', 
            multiple: true, 
            categorized: true,
            blockSelectedGroup: true"></fc-select>
        </div>


      </div>
    </div>
    <!-- /ko -->
    <!-- /ko -->

    <!-- ko if: item.id === 'anketaLink' || item.id === 'anketaQuote' -->
    <div class="reviews-filter reviews-filter--anketaLink">
      <!-- ko if: $root.directories.linksWithQuotes.loaded() -->
      <!-- ko if: $root.directories.linksWithQuotes.list().length -->
      <div class="form-group dense-form-group">
        <label class="form-label"><?= \Yii::t('main', 'Ссылки') ?></label>
        <div>
          <fc-select class="categorized" params="inline: true, 
            options: $root.directories.linksWithQuotes.list, 
            value: item.filter.value, 
            placeholder: 'Все ссылки', 
            multiple: true, 
            categorized: true,
            blockSelectedGroup: true"></fc-select>
        </div>
      </div>
      <!-- /ko -->
      <!-- /ko -->
    </div>
    <!-- /ko -->

    <?php if (!isset($executor) || !$executor) : ?>
      <!-- ko ifnot: $root.single -->
      <!-- ko if: $root.directories.folders.loaded -->
      <!-- ko if: item.id === 'folders' -->
      <div class="reviews-filter reviews-filter--folders">
        <div class="form-group dense-form-group">
          <label class="form-label"><?= \Yii::t('main', 'Папки') ?></label>

          <select multiple data-bind="
          selectedOptions: item.filter.value,
          allowUnset: true,
          lazySelect2: {
              wrapperCssClass: 'select2-container--form-control',
              dropdownCssClass: 'dense-form-group__dropdown',
              containerCss: { 'min-width': '91px' },
              templateResult: $root.folderResultTemplate,
              templateSelection: $root.folderSelectionTemplate,
          }
      " data-placeholder="<?= \Yii::t('main', 'Все папки') ?>">

            <!-- ko foreach: { data: $root.directories.folders.data, as: 'folder' } -->
            <!-- ko template: {
              name: 'folder-template',
              data: {
              folder: folder,
              level: 0
              }
          } -->
            <!-- /ko -->
            <!-- /ko -->
          </select>
        </div>
      </div>
      <!-- /ko -->
      <!-- /ko -->
      <!-- /ko -->
    <?php endif; ?>

    <?php if (!isset($executor) || !$executor) : ?>
      <!-- ko ifnot: $root.single -->
      <!-- ko if: $root.directories.polls.loaded -->
      <!-- ko if: item.id === 'polls' -->
      <div class="reviews-filter reviews-filter--poll">
        <div class="form-group dense-form-group" data-bind="if: $root.directories.polls.loaded">
          <label class="form-label"><?= \Yii::t('main', 'Опросы') ?></label>

          <select multiple data-bind="
          selectedOptions: item.filter.value,
          allowUnset: true,
          options: $root.directories.polls.data() && $root.directories.polls.data().map(({id, ...data}) => ({id: String(id), ...data})),
          optionsText: 'name',
          optionsValue: 'id',
          select2: {
              wrapperCssClass: 'select2-container--form-control select2-container--limit',
              dropdownCssClass: 'dense-form-group__dropdown',
              containerCss: { 'min-width': '91px' },
          }
      " data-placeholder="<?= \Yii::t('main', 'Все опросы') ?>"></select>
        </div>
      </div>
      <!-- /ko -->
      <!-- /ko -->
      <!-- /ko -->
    <?php endif; ?>


    <!-- ko ifnot: $root.single -->
    <!-- ko if: item.id === 'type' -->
    <div class="reviews-filter reviews-filter--type">
      <div class="form-group dense-form-group">
        <label class="form-label"><?= \Yii::t('main', 'Тип опроса') ?></label>
        <select data-bind="
              value: item.filter.value,
              allowUnset: true,
              select2: {
                  wrapperCssClass: 'select2-container--form-control',
                  dropdownCssClass: 'dense-form-group__dropdown',
                  allowClear: true
              }
          " data-placeholder="<?= \Yii::t('main', 'Все типы') ?>">
          <option></option>
          <option value="0"><?= \Yii::t('main', 'Ручной') ?></option>
          <option value="1"><?= \Yii::t('main', 'Автоматический') ?></option>
        </select>
      </div>
    </div>
    <!-- /ko -->
    <!-- /ko -->

    <!-- ko if: $root.withPoints -->
    <!-- ko if: item.id === 'points' -->
    <div class="reviews-filter reviews-filter--points">
      <div class="form-group dense-form-group">
        <label class="form-label"><?= \Yii::t('main', 'Набрано баллов') ?></label>
        <div class="d-flex">
          <select data-bind="
                value: item.filter.value.type,
                allowUnset: true,
                select2: {
                    wrapperCssClass: 'select2-container--form-control',
                    dropdownCssClass: 'dense-form-group__dropdown',
                    allowClear: !$root.single
                }
            " data-placeholder="<?= \Yii::t('main', 'Не указан') ?>">

            <!-- ko ifnot: $root.single -->
            <option></option>
            <option value="0"><?= \Yii::t('answers', 'Без баллов') ?></option>
            <!-- /ko -->
            <option value="1"><?= \Yii::t('answers', 'Баллы') ?></option>
            <option value="2"><?= \Yii::t('answers', 'Проценты') ?></option>
          </select>

          <!-- ko if: item.filter.value.type() == 1 -->
          <foquz-number-interval class="ml-3" params="dense: true, interval: item.filter.value.range, formControlErrorStateMatcher: $root.filtersController.formControlErrorStateMatcher"></foquz-number-interval>
          <!-- /ko -->

          <!-- ko if: item.filter.value.type() == 2 -->
          <foquz-number-interval class="ml-3" params="dense: true, interval: item.filter.value.range, max: 100, formControlErrorStateMatcher: $root.filtersController.formControlErrorStateMatcher"></foquz-number-interval>
          <!-- /ko -->
        </div>
      </div>
    </div>
    <!-- /ko -->
    <!-- /ko -->

    <!-- ko if: item.id === 'channels' -->
    <div class="reviews-filter reviews-filter--folders">
      <div class="form-group dense-form-group">
        <label class="form-label"><?= \Yii::t('main', 'Канал связи') ?></label>

        <select multiple data-bind="
          selectedOptions: item.filter.value,
          allowUnset: true,
          lazySelect2: {
              wrapperCssClass: 'select2-container--form-control',
              dropdownCssClass: 'dense-form-group__dropdown',
              containerCss: { 'min-width': '91px' },
          }
      " data-placeholder="<?= \Yii::t('main', 'Все каналы') ?>">

          <!-- ko foreach: [
            { id: 'Email', name: _t('Email') },
            { id: 'SMS', name: _t('SMS') },
            { id: 'Telegram', name: _t('Telegram') },
            { id: 'Viber', name: _t('Viber') },
            { id: 'Push', name: _t('Push') },
            { id: 'Ссылка', name: _t('Ссылка') },
          ] -->
          <option data-bind="value: id, text: name"></option>
          <!-- /ko -->
        </select>
      </div>
    </div>
    <!-- /ko -->

    <!-- ko if: item.id === 'questionnaires' -->
    <div class="reviews-filter reviews-filter--folders">
      <div class="form-group dense-form-group">
        <label class="form-label"><?= \Yii::t('main', 'Статус анкеты') ?></label>

        <select multiple data-bind="
          selectedOptions: item.filter.value,
          valueAllowUnset: true,
          lazySelect2: {
              wrapperCssClass: 'select2-container--form-control',
              dropdownCssClass: 'dense-form-group__dropdown',
          }
      " data-placeholder="<?= \Yii::t('main', 'Все анкеты') ?>">

          <option></option>
          <option value="done"><?= \Yii::t('answers', 'Заполнена') ?></option>
          <option value="in-progress"><?= \Yii::t('answers', 'В процессе') ?></option>
          <option value="quote-full"><?= \Yii::t('answers', 'Квотафул') ?></option>
          <option value="screen-out"><?= \Yii::t('answers', 'Скринаут') ?></option>
        </select>
      </div>
    </div>
    <!-- /ko -->

    <!-- ko if: item.id === 'mailings' -->
    <div class="reviews-filter reviews-filter--malings">
      <!-- ko if: $parent.valueFilters.collections.mailings.loaded -->
      <!-- ko if: $parent.valueFilters.collections.mailings.list().length -->
      <div class="form-group dense-form-group">
        <label class="form-label"><?= \Yii::t('answers', 'Приглашения к опросу') ?></label>
        <collection-select params="collection: $parent.valueFilters.collections.mailings, value: item.filter.value, multiple: true, dense: true, placeholder: _t('anwers', 'Все приглашения')"></collection-select>
      </div>
      <!-- /ko -->

      <!-- ko ifnot: $parent.valueFilters.collections.mailings.list().length -->
      <div class="form-group dense-form-group" data-bind="tooltip, tooltipText: !$parent.valueFilters.collections.mailings.list().length ? _t('answers', 'В этом опросе пока не создано ни одного приглашения') : ''" style="opacity: 0.5;">
        <label class="form-label"><?= \Yii::t('answers', 'Приглашения к опросу') ?></label>
        <div class="f-fs-2 f-color-service">Приглашений нет</div>
      </div>
      <!-- /ko -->

      <!-- /ko -->
    </div>
    <!-- /ko -->

    <!-- ko if: item.id === 'tags' -->
    <div class="reviews-filter reviews-filter--tags">
      <tags-filter
        params="
          tags: item.filter.value,
          operation: $parent.valueFilters.getFilter('tagsOperation'),
          placeholder: _t('Все теги'),
          label: _t('Тег контакта'),
          disabled: !isContactsEnabled
        "
      ></tags-filter>
    </div>
    <!-- /ko -->

    <!-- ko if: item.id === 'answerTags' -->
    <div class="reviews-filter reviews-filter--answerTags">
      <tags-filter
        params="
          tags: item.filter.value,
          withoutConditionOnly: true,
          operation: $parent.valueFilters.getFilter('answerTagsOperation'),
          placeholder: _t('Все теги'),
          label: _t('Теги анкеты'),
          disabled: !isContactsEnabled,
          disabledText: 'Фильтр «Теги анкеты» доступен при подключенном разделе'
        "
      ></tags-filter>
    </div>
    <!-- /ko -->

    <!-- ko if: $root.directories.filials.loaded() -->
    <!-- ko if: item.id === 'clientFilials' -->
    <div class="reviews-filter reviews-filter--filial">
      <div class="form-group dense-form-group">
        <label class="form-label"><?= \Yii::t('main', 'Филиал контакта') ?></label>

        <div>
          <fc-select class="categorized" params="inline: true, 
            options: $root.directories.filials.list, 
            value: item.filter.value, 
            placeholder: 'Все филиалы', 
            multiple: true, 
            blockSelectedGroup: true"></fc-select>
        </div>


      </div>
    </div>
    <!-- /ko -->
    <!-- /ko -->

    <!-- ko if: item.id === 'devices' -->
    <div class="reviews-filter reviews-filter--devices">
      <div class="form-group dense-form-group">
        <label class="form-label"><?= \Yii::t('main', 'Устройство') ?></label>

        <select data-bind="
          selectedOptions: item.filter.value,
          valueAllowUnset: true,
          lazySelect2: {
              wrapperCssClass: 'select2-container--form-control',
              dropdownCssClass: 'dense-form-group__dropdown',
          }
      " data-placeholder="<?= \Yii::t('main', 'Все устройства') ?>" multiple>

          <option></option>
          <option value="desktop"><?= \Yii::t('main', 'Десктоп') ?></option>
          <option value="tablet"><?= \Yii::t('main', 'Планшет') ?></option>
          <option value="mobile"><?= \Yii::t('main', 'Смартфон') ?></option>

        </select>
      </div>
    </div>
    <!-- /ko -->

    <!-- ko if: item.id === 'hasComplaint' -->
    <div class="reviews-filter reviews-filter--has-complaints">
      <input-checkbox params="checked: item.filter.value"><?= \Yii::t('main', 'Есть жалобы') ?></input-checkbox>
    </div>
    <!-- /ko -->

    <!-- ko if: $root.hasProcessingOptions -->
    <!-- ko if: item.id === 'withProcessing' -->
    <div class="reviews-filter reviews-filter--with-comments">
      <input-checkbox params="checked: item.filter.value"><?= \Yii::t('answers', 'Анкеты с обработкой') ?></input-checkbox>
    </div>
    <!-- /ko -->
    <!-- /ko -->

    <!-- ko if: item.id === 'withComment' -->
    <div class="reviews-filter reviews-filter--with-comments">
      <input-checkbox params="checked: item.filter.value"><?= \Yii::t('main', 'Есть комментарий') ?></input-checkbox>
    </div>
    <!-- /ko -->

    <!-- ko ifnot: $root.single -->
    <!-- ko if: item.id === 'groupByPolls' -->
    <div class="reviews-filter reviews-filter--group-by-polls">
      <input-checkbox params="checked: item.filter.value"><?= \Yii::t('answers', 'Группировать по опросам') ?></input-checkbox>
    </div>
    <!-- /ko -->
    <!-- /ko -->

    <!-- ko if: item.id === 'processing_time_expired' -->
    <div class="reviews-filter reviews-filter--expired">
      <input-checkbox params="checked: item.filter.value"><?= \Yii::t('answers', 'Истёк срок обработки') ?></input-checkbox>
    </div>
    <!-- /ko -->



    <!-- /ko -->


    <div class="reviews-filter reviews-page-filters__actions">
      <button type="button" class="f-btn f-btn-link mr-3 d-none d-md-flex" data-bind="click: function() { $root.resetFilters() }"><?= \Yii::t('main', 'Сбросить') ?></button>

      <button type="button" class="f-btn d-md-none f-btn-lg" data-bind="click: function() { $root.resetFilters() }">
        <span class="f-btn-prepend">
          <svg-icon params="name: 'bin'"></svg-icon>
        </span>
        <?= \Yii::t('main', 'Сбросить') ?>
      </button>

      <button type="button" class="f-btn f-btn-success f-btn-lg" data-bind="click: function() { $root.applyFilters() }">
        <span class="f-btn-prepend">
          <svg-icon params="name: 'check'"></svg-icon>
        </span>
        <?= \Yii::t('main', 'Применить') ?>
      </button>
    </div>

  </div>


</div>
<!-- /ko -->
<div class="f-card__divider d-none d-md-block"></div>
