<template id="reviews-list-head-cell-template">
  <!-- ko if: $root.table.columns.has(column) -->
  <!-- ko let: { col: $root.table.columns.get(column) } -->
  <th class="reviews-list__cell reviews-list__head-cell" data-bind="class: 'reviews-list__cell--' + cellName">
    <div class="reviews-list__cell-content">
      <div class="reviews-list__head-cell-name" data-bind="click: function() { 
        if (!$root.sort.fields.includes(sortName)) {
          return;
        } 
        $root.sort.sortBy(sortName); },
      css: { 'cursor-pointer': $root.sort.fields.includes(sortName), 
        'foq-table__head-cell-title--active': $root.sort.field() === sortName}">
        <!-- ko text: col.shortName ? col.shortName : col.name -->
        <!-- /ko -->

        <!-- ko if: $root.sort.fields.includes(sortName) -->
        <i class="reviews-list__order icon icon-sorting icon-sorting--default" data-bind="css: {
      'reviews-list__order--asc': $root.sort.asc() && $root.sort.field() === sortName,
      'reviews-list__order--desc': !$root.sort.asc() && $root.sort.field() === sortName,
      'icon-sorting--active': $root.sort.field() === sortName,
    }"></i>
        <!-- /ko -->

        <!-- ko if: $root.search[searchName] -->
        <!-- ko if: $root.search[searchName].value().length > 0 -->
        <i class="reviews-list__filter icon icon-filtering icon-filtering--active"></i>
        <!-- /ko -->
        <!-- /ko -->
      </div>

      <!-- ko if: $root.search[searchName] -->
      <input class="reviews-list__search" data-bind="textInput: $root.search[searchName].value, event: {
    keydown: $root.search.onKeydown.bind($root.search)
    }, attr: {placeholder: col.placeholder}">
      <!-- /ko -->
    </div>
  </th>
  <!-- /ko -->
  <!-- /ko -->
</template>

<template id="reviews-list-client-head-cell-template">
  <!-- ko if: $root.table.columns.has(column.fullName) -->

  <th class="reviews-list__cell reviews-list__head-cell reviews-list__cell--clientField" data-bind="attr: {
    'data-field-id': column.id,
    'data-field': column.fullName,
  }">
    <div class="reviews-list__cell-content" data-bind="style: {
      'width': column.width + 'px'
    }">
      <div class="reviews-list__head-cell-name cursor-pointer" data-bind="click: function() {
        if (column.id === 'customerId') return;
        if (column.system) $root.sort.sortBy(column.fullName);
        else $root.sort.sortByAdditional(column) },
      css: {
        'cursor-pointer': column.id !== 'customerId'
      }">
        <!-- ko if: column.short -->
        <!-- ko text: column.short -->
        <!-- /ko -->
        <!-- /ko -->
        <!-- ko ifnot: column.short -->
        <!-- ko text: column.text -->
        <!-- /ko -->
        <!-- /ko -->

        <!-- ko if: $root.sort.field() === column.fullName ||( $root.sort.additional && $root.sort.field() === column.id) -->
        <i class="reviews-list__order icon icon-sorting icon-sorting--active" data-bind="css: {
          'reviews-list__order--asc': $root.sort.asc(),
          'reviews-list__order--desc': !$root.sort.asc(),
        }"></i>
        <!-- /ko -->

        <!-- ko if: $root.search.client[column.id] -->
        <!-- ko if: $root.search.client[column.id].value().length > 0 -->
        <i class="reviews-list__filter icon icon-filtering icon-filtering--active"></i>
        <!-- /ko -->
        <!-- /ko -->


      </div>

      <!-- ko if: $root.search.client[column.id] -->
      <input
        class="reviews-list__search"
        data-bind="
          textInput: $root.search.client[column.id].value,
          event: {
            keydown: $root.search.onKeydown.bind($root.search)
          },
          attr: {
            placeholder: column.placeholder,
          },
        "
      />
      <!-- /ko -->
    </div>
  </th>

  <!-- /ko -->
</template>


<template id="answers-table-head-template">
  <!-- Шапка таблицы -->
  <div class="reviews-list__header">
    <!-- ko let: { container: ko.observable(null) } -->
    <div class="reviews-list__header-content" data-bind="component: { name: 'scroll-container-content'}">

      <table class="reviews-table reviews-table--header table">
        <thead>

          <tr class="reviews-list__row" data-bind="using: table.columns">
            <th class="outer-cell"></th>
            <!-- ko if: CURRENT_USER.admin -->
            <!-- Check -->
            <th class="reviews-list__cell reviews-list__head-cell reviews-list__cell--check">
              <div class="reviews-list__cell-content">
                <div class="review-check">
                  <!-- <input type="checkbox" id="all-check" data-bind="checked: $root.reviews.isAllChecked, event: {
                    change: function(_, e) { $root.reviews.toggleAll(e.target.checked) }
                    }" class="form-check-input">
                  <label class="form-check-label" for="all-check"></label>-->
                </div>
              </div>
            </th>
            <!-- /Check -->
            <!-- /ko -->

            <!-- ko if: $root.processingEnabled -->
            <th class="reviews-list__cell reviews-list__head-cell reviews-list__cell--problems">
              <div class="reviews-list__cell-content">
              </div>
            </th>
            <!-- /ko -->

            <!-- ko if: $root.hasProcessingOptions -->
            <th class="reviews-list__cell reviews-list__head-cell reviews-list__cell--processing">
              <div class="reviews-list__cell-content">
              </div>
            </th>
            <!-- /ko -->

            <!-- ID -->
            <!-- ko template: {
                  name: 'reviews-list-head-cell-template',
                  data: {
                    column: 'id',
                    cellName: 'id',
                    searchName: 'id',
                    sortName: 'id',
                  }
                  } -->
            <!-- /ko -->
            <!-- /ID -->



            <!-- Тип -->
            <!-- ko template: {
                  name: 'reviews-list-head-cell-template',
                  data: {
                    column: 'type',
                    cellName: 'type',
                    searchName: 'type',
                    sortName: 'pollType',
                  }
                  } -->
            <!-- /ko -->
            <!-- /Тип -->

            <!-- Название опроса -->
            <!-- ko template: {
                name: 'reviews-list-head-cell-template',
                data: {
                column: 'pollName',
                cellName: 'pollName',
                searchName: 'pollName',
                sortName: 'pollName',
                }
                } -->
            <!-- /ko -->
            <!-- /Название опроса -->

            <!-- Пройден -->
            <!-- ko template: {
                  name: 'reviews-list-head-cell-template',
                  data: {
                  column: 'passedAt',
                  cellName: 'passedAt',
                  searchName: 'passedAt',
                  sortName: 'passedAt',
                  }
                  } -->
            <!-- /ko -->
            <!-- /Пройден -->

            <!-- Статус анкеты -->
            <!-- ko template: {
                  name: 'reviews-list-head-cell-template',
                  data: {
                  column: 'anketaStatus',
                  cellName: 'anketaStatus',
                  searchName: 'anketaStatus',
                  sortName: 'anketaStatus',
                  }
                  } -->
            <!-- /ko -->
            <!-- /Статус анкеты -->
            
            <!-- Ссылка -->
            <!-- ko template: {
                  name: 'reviews-list-head-cell-template',
                  data: {
                  column: 'anketaLink',
                  cellName: 'anketaLink',
                  searchName: 'anketaLink',
                  sortName: 'anketaLink',
                  }
                  } -->
            <!-- /ko -->
            <!-- /Ссылка -->

            <!-- Квота -->
            <!-- ko template: {
                  name: 'reviews-list-head-cell-template',
                  data: {
                  column: 'anketaQuote',
                  cellName: 'anketaQuote',
                  searchName: 'anketaQuote',
                  sortName: 'anketaQuote',
                  }
                  } -->
            <!-- /ko -->
            <!-- /Квота -->
            <!-- ko template: {

                  name: 'reviews-list-head-cell-template',
                  data: {
                  column: 'filial',
                  cellName: 'filial',
                  searchName: 'filial',
                  sortName: 'filial',
                  }
                  } -->
            <!-- /ko -->
            <!-- /Филиал -->


            <!-- Статус обработки -->
            <!-- ko template: {
                    name: 'reviews-list-head-cell-template',
                    data: {
                    column: 'processingStatus',
                    cellName: 'processingStatus',
                    searchName: 'status',
                    sortName: 'status',
                    }
                    } -->
            <!-- /ko -->
            <!-- /Статус обработки -->

            <!-- Срок обработки -->
            <!-- ko template: {
                    name: 'reviews-list-head-cell-template',
                    data: {
                    column: 'processingTime',
                    cellName: 'processingTime',
                    searchName: 'processingTimeAt',
                    sortName: 'processingTimeAt',
                    }
                    } -->
            <!-- /ko -->
            <!-- /Срок обработки -->

            <!-- Канал -->
            <!-- ko template: {
                    name: 'reviews-list-head-cell-template',
                    data: {
                    column: 'channel',
                    cellName: 'channel',
                    searchName: 'channel',
                    sortName: 'channel',
                    }
                    } -->
            <!-- /ko -->
            <!-- /Канал -->

            <!-- Устройство -->
            <!-- ko template: {
              name: 'reviews-list-head-cell-template',
              data: {
              column: 'device',
              cellName: 'device',
              searchName: 'device',
              sortName: 'device',
              }
              } -->
            <!-- /ko -->
            <!-- /Устройство -->

            <!-- Время заполнения анкеты -->
            <!-- ko template: {
              name: 'reviews-list-head-cell-template',
              data: {
              column: 'answerTime',
              cellName: 'answerTime',
              searchName: 'answerTime',
              sortName: 'answerTime',
              }
              } -->
            <!-- /ko -->
            <!-- /Время заполнения анкеты -->

            <!-- Язык -->
            <!-- ko template: {
              name: 'reviews-list-head-cell-template',
              data: {
              column: 'lang',
              cellName: 'lang',
              searchName: 'lang',
              sortName: 'lang',
              }
              } -->
            <!-- /ko -->
            <!-- /Язык -->

            <!-- Рассылка -->
            <!-- ko template: {
              name: 'reviews-list-head-cell-template',
              data: {
              column: 'mailing',
              cellName: 'mailing',
              searchName: 'mailing',
              sortName: 'mailing',
              }
              } -->
            <!-- /ko -->
            <!-- /Рассылка -->

                        <!-- Виджет -->
            <!-- ko template: {
              name: 'reviews-list-head-cell-template',
              data: {
                column: 'anketaWidget',
                cellName: 'anketaWidget',
                searchName: 'anketaWidget',
                sortName: 'anketaWidget',
              }
            } -->
            <!-- /ko -->
            <!-- /Виджет -->

            <!-- Сайт -->
            <!-- ko template: {
              name: 'reviews-list-head-cell-template',
              data: {
                column: 'anketaSite',
                cellName: 'anketaSite',
                searchName: 'anketaSite',
                sortName: 'anketaSite',
              }
            } -->
            <!-- /ko -->
            <!-- /Сайт -->

            <!-- Источник -->
            <!-- ko template: {
              name: 'reviews-list-head-cell-template',
              data: {
                column: 'anketaSource',
                cellName: 'anketaSource',
                searchName: 'anketaSource',
                sortName: 'anketaSource',
              }
            } -->
            <!-- /ko -->
            <!-- /Источник -->

            <!-- Оценки -->
            <!-- ko template: {
              name: 'reviews-list-head-cell-template',
              data: {
              column: 'points',
              cellName: 'points',
              searchName: 'points',
              sortName: 'points',
              }
              } -->
            <!-- /ko -->
            <!-- /Оценки -->

            <!-- Комментарий -->
            <!-- ko template: {
                  name: 'reviews-list-head-cell-template',
                  data: {
                  column: 'comments',
                  cellName: 'comments',
                  searchName: 'comment',
                  sortName: 'comment',
                  }
                  } -->
            <!-- /ko -->
            <!-- /Комментарий -->

            <!-- Тэги ответа -->
            <!-- ko template: {
              name: 'reviews-list-head-cell-template',
              data: {
                column: 'answerTags',
                cellName: 'answerTags',
                searchName: 'answerTags',
                sortName: 'answerTags',
              }
            } -->
            <!-- /ko -->
            <!-- /Тэги ответа -->

            <!-- Жалоба -->
            <!-- ko template: {
              name: 'reviews-list-head-cell-template',
              data: {
              column: 'complaint',
              cellName: 'complaint',
              searchName: 'complaint',
              sortName: 'complaint',
              }
              } -->
            <!-- /ko -->
            <!-- /Жалоба -->

            <!-- Баллов набрано -->
            <!-- ko template: {
                  name: 'reviews-list-head-cell-template',
                  data: {
                  column: 'pointsCollect',
                  cellName: 'pointsCollect',
                  searchName: 'pointsCollect',
                  sortName: 'pointPercents',
                  }
                  } -->
            <!-- /ko -->
            <!-- /Баллов набрано -->

            <!-- Клиент -->
            <!-- ko template: {
                name: 'reviews-list-head-cell-template',
                data: {
                column: 'clientName',
                cellName: 'clientName',
                searchName: 'clientName',
                sortName: 'clientName',
                }
                } -->
            <!-- /ko -->
            <!-- /Клиент -->

            <!-- Телефон -->
            <!-- ko template: {
              name: 'reviews-list-head-cell-template',
              data: {
              column: 'clientPhone',
              cellName: 'clientPhone',
              searchName: 'clientPhone',
              sortName: 'clientPhone',
              }
              } -->
            <!-- /ko -->
            <!-- /Телефон -->

            <!-- Email -->
            <!-- ko template: {
              name: 'reviews-list-head-cell-template',
              data: {
              column: 'clientEmail',
              cellName: 'clientEmail',
              searchName: 'clientEmail',
              sortName: 'clientEmail',
              }
              } -->
            <!-- /ko -->
            <!-- /Email -->

            <!-- ko foreach: $root.clientSystemFields -->
            <!-- ko template: {
                name: 'reviews-list-client-head-cell-template',
                data: {
                  column: $data,
                }
                } -->
            <!-- /ko -->
            <!-- /ko -->


            <!-- ko foreach: $root.clientFields -->
            <!-- ko template: {
                name: 'reviews-list-client-head-cell-template',
                data: {
                  column: $data,
                }
                } -->
            <!-- /ko -->
            <!-- /ko -->

            <!-- Модератор -->
            <!-- ko template: {
                name: 'reviews-list-head-cell-template',
                data: {
                column: 'moderator',
                cellName: 'moderator',
                searchName: 'moderator',
                sortName: 'moderatorName',
                }
                } -->
            <!-- /ko -->
            <!-- /Модератор -->

            <!-- Исполнитель -->
            <!-- ko template: {
                name: 'reviews-list-head-cell-template',
                data: {
                column: 'executor',
                cellName: 'executor',
                searchName: 'executor',
                sortName: 'executorName',
                }
                } -->
            <!-- /ko -->
            <!-- /Исполнитель -->

            <!-- Номер заказа -->
            <!-- ko template: {
              name: 'reviews-list-head-cell-template',
              data: {
              column: 'orderNumber',
              cellName: 'orderNumber',
              searchName: 'orderNumber',
              sortName: 'orderNumber',
              }
              } -->
            <!-- /ko -->
            <!-- /Номер заказа -->

            <!-- Время заказа -->
            <!-- ko template: {
              name: 'reviews-list-head-cell-template',
              data: {
              column: 'orderTime',
              cellName: 'orderTime',
              searchName: 'orderTime',
              sortName: 'orderTime',
              }
              } -->
            <!-- /ko -->
            <!-- /Время заказа -->

            <!-- Сумма заказа -->
            <!-- ko template: {
              name: 'reviews-list-head-cell-template',
              data: {
              column: 'orderSum',
              cellName: 'orderSum',
              searchName: 'orderSum',
              sortName: 'orderSum',
              }
            } -->
            <!-- /ko -->
            <!-- /Сумма заказа -->

            <!-- Тип доставки -->
            <!-- ko template: {
              name: 'reviews-list-head-cell-template',
              data: {
              column: 'orderType',
              cellName: 'orderType',
              searchName: 'orderType',
              sortName: 'orderType',
              }
              } -->
            <!-- /ko -->
            <!-- /Тип доставки -->

            <!-- Способ оформления -->
            <!-- ko template: {
              name: 'reviews-list-head-cell-template',
              data: {
              column: 'sourceType',
              cellName: 'sourceType',
              searchName: 'sourceType',
              sortName: 'sourceType',
              }
            } -->
            <!-- /ko -->
            <!-- /Способ оформления -->


            <!-- Адрес доставки -->
            <!-- ko template: {
              name: 'reviews-list-head-cell-template',
              data: {
              column: 'orderAddress',
              cellName: 'orderAddress',
              searchName: 'orderAddress',
              sortName: 'orderAddress',
              }
              } -->
            <!-- /ko -->
            <!-- /Адрес доставки -->

            <th class="outer-cell"></th>
          </tr>
        </thead>
      </table>
    </div>
    <!-- /ko -->
  </div>
  <!-- /Шапка таблицы -->
</template>

<template id="review-row-template">
  <tr
    class="reviews-list__row cursor-pointer"
    data-bind="
      css: { 'reviews-list__row--highlighted': review.checked },
      click: function(_, e) {
        var isDropdown = e.target.closest('[data-toggle]');
        if (isDropdown) return;
        $root.openDetailsModal(review);
      }
    "
  >
    
    <td class="outer-cell">
      <a
        class="reviews-list__row-custom-link"
        data-bind="attr: { href: window.location.href + '&reviewId=' + review.id }"
      ></a>
    </td>
    <!-- ko if: CURRENT_USER.admin -->
    <!-- Check -->
    <td class="reviews-list__cell reviews-list__cell--check">
      <div class="reviews-list__cell-content">
        <div class="review-check">
          <input type="checkbox" value="1" data-bind="checked: review.checked, attr: {id: review.id + '-checked'}, click: function(_, e) { e.stopPropagation(); return true; }" class="form-check-input">
          <label class="form-check-label" data-bind="attr: {'for': review.id + '-checked'}, click: function(_, e) { e.stopPropagation(); return true; }"></label>
        </div>
      </div>
    </td>
    <!-- /Check -->
    <!-- /ko -->

    <!-- ko if: $root.processingEnabled -->
    <td class="reviews-list__cell reviews-list__cell--problems">
      <div class="reviews-list__cell-content">

      </div>
    </td>
    <!-- /ko -->

    <!-- ko if: $root.hasProcessingOptions -->
    <td class="reviews-list__cell reviews-list__cell--processing" align="center">
      <div class="reviews-list__cell-content">
        <!-- ko if: review.processingEnabled -->
        <svg-icon class="f-color-danger" params="name: 'exclamation-circle', width: 16, height: 16" data-bind="tooltip, tooltipText: _t('answers', 'Анкета с обработкой')"></svg-icon>
        <!-- /ko -->
      </div>
    </td>
    <!-- /ko -->

    <!-- ID -->
    <!-- ko if: $root.table.columns.has('id') -->
    <td class="reviews-list__cell reviews-list__cell--id">
      <div class="reviews-list__cell-content">
        <div data-bind="text: review.hash">
        </div>
      </div>
    </td>
    <!-- /ko -->
    <!-- /ID -->

    <!-- Тип -->
    <!-- ko if: $root.table.columns.has('type') -->
    <td class="reviews-list__cell reviews-list__cell--type">
      <div class="reviews-list__cell-content">
        <div data-bind="attr: {
          title: review.isAuto ? _t('Автоматический') : _t('Ручной')
        }, tooltip">
          <!-- ko if: review.isAuto -->
          <i class="icon icon-automatic"></i>
          <!-- /ko -->
          <!-- ko ifnot: review.isAuto -->
          <i class="icon icon-manual"></i>
          <!-- /ko -->
        </div>

      </div>
    </td>
    <!-- /ko -->
    <!-- /Тип -->

    <!-- Название опроса -->
    <!-- ko if: $root.table.columns.has('pollName') -->
    <td class="reviews-list__cell reviews-list__cell--pollName">
      <div class="reviews-list__cell-content hyphenate" data-bind="text: review.pollName">

      </div>
    </td>
    <!-- /ko -->
    <!-- /Название опроса -->

    <!-- Пройден -->
    <!-- ko if: $root.table.columns.has('passedAt') -->
    <td class="reviews-list__cell reviews-list__cell--passedAt">
      <div class="reviews-list__cell-content" data-bind="text: review.passedAt">

      </div>
    </td>
    <!-- /ko -->
    <!-- /Пройден -->

    <!-- Статус анкеты -->
    <!-- ko if: $root.table.columns.has('anketaStatus') -->
    <td class="reviews-list__cell reviews-list__cell--anketaStatus">
      <div class="reviews-list__cell-content">
        <span data-bind="text: review.anketaStatus"></span>
      </div>
    </td>
    <!-- /ko -->
    <!-- /Статус анкеты -->
    
    <!-- Ссылка -->
    <!-- ko if: $root.table.columns.has('anketaLink') -->
    <td class="reviews-list__cell reviews-list__cell--anketaLink">
      <div class="reviews-list__cell-content">
        <span data-bind="text: review.anketaLink, css: {'semibold': !review.anketaLinkStatus, 'f-color-service': !review.anketaLinkStatus}"></span>
      </div>
    </td>
    <!-- /ko -->
    <!-- /Ссылка -->

    <!-- Квота -->
    <!-- ko if: $root.table.columns.has('anketaQuote') -->
    <td class="reviews-list__cell reviews-list__cell--anketaQuote">
      <div class="reviews-list__cell-content">
        <span data-bind="text: review.anketaQuote, css: {'semibold': !review.anketaLinkStatus, 'f-color-service': !review.anketaLinkStatus}"></span>
      </div>
    </td>
    <!-- /ko -->
    <!-- /Квота -->
    
    <!-- Филиал -->
    <!-- ko if: $root.hasFilials() && $root.table.columns.has('filial') -->
    <td class="reviews-list__cell reviews-list__cell--filial">
      <div class="reviews-list__cell-content">
        <filial-text params="filial: review.filial"></filial-text>
      </div>
    </td>
    <!-- /ko -->
    <!-- /Филиал -->

    <!-- Статус обработки -->
    <!-- ko if: $root.table.columns.has('processingStatus') -->
    <td class="reviews-list__cell reviews-list__cell--processingStatus">
      <div class="reviews-list__cell-content">
        <span
          class="processing-status"
          data-bind="
            text: review.processingStatusText(),
            css: 'processing-status--' + review.processingStatus(),
          "
        ></span>
      </div>
    </td>
    <!-- /ko -->
    <!-- /Статус обработки -->

    <!-- Срок обработки -->
    <!-- ko if: $root.table.columns.has('processingTime') -->
    <td class="reviews-list__cell reviews-list__cell--processingTime">
      <div class="reviews-list__cell-content">
        <!-- ko if: review.processToDate -->
        <span data-bind="text: review.processToDate"></span>
        <!-- /ko -->
      </div>
    </td>
    <!-- /ko -->
    <!-- /Срок обработки -->

    <!-- Канал -->
    <!-- ko if: $root.table.columns.has('channel') -->
    <td class="reviews-list__cell reviews-list__cell--channel">
      <div class="reviews-list__cell-content">
        <!-- ko template: {
          name: 'answer-channel-dropdown-template',
          data: {
            answer: $data
          }
        } -->
        <!-- /ko -->

      </div>
    </td>
    <!-- /ko -->
    <!-- /Канал -->

    <!-- Устройство -->
    <!-- ko if: $root.table.columns.has('device') -->
    <td class="reviews-list__cell reviews-list__cell--device">
      <div class="reviews-list__cell-content text-center">
        <!-- ko if: review.device -->
        <span data-bind="tooltip, tooltipText: _t('Устройство') + ': ' + _t(review.deviceName)">
          <svg-icon params="name: 'viewport-' + review.device" class="f-color-service"></svg-icon>
        </span>
        <!-- /ko -->

      </div>
    </td>
    <!-- /ko -->
    <!-- /Устройство -->

    <!-- Время заполнения анкеты -->
    <!-- ko if: $root.table.columns.has('answerTime') -->
    <td class="reviews-list__cell reviews-list__cell--answerTime">
      <div class="reviews-list__cell-content text-center" data-bind="text: review.answerTime"></div>
    </td>
    <!-- /ko -->
    <!-- /Время заполнения анкеты -->

    <!-- Язык -->
    <!-- ko if: $root.table.columns.has('lang') -->
    <td class="reviews-list__cell reviews-list__cell--lang">
      <div class="reviews-list__cell-content text-center" data-bind="text: review.langText"></div>
    </td>
    <!-- /ko -->
    <!-- /Язык -->

    <!-- Рассылка -->
    <!-- ko if: $root.table.columns.has('mailing') -->
    <td class="reviews-list__cell reviews-list__cell--mailing">
      <div class="reviews-list__cell-content" data-bind="text: review.mailing">
      </div>
    </td>
    <!-- /ko -->
    <!-- /Рассылка -->


    <!-- Виджет -->
    <!-- ko if: $root.table.columns.has('anketaWidget') -->
    <td class="reviews-list__cell reviews-list__cell--anketaWidget">
      <a 
        class="reviews-list__cell-content reviews-list__cell-content--link position-relative d-block f-color-primary" 
        data-bind="
          text: review.anketaWidget, 
          attr: { href: '/foquz/foquz-poll/widgets?id=' + review.pollId, 'target': '_blank' },
          click: function(_, event) {
            event.stopPropagation();
            event.stopImmediatePropagation();
            window.open('/foquz/foquz-poll/widgets?id=' + review.pollId, '_blank');
          }
        "
      >
      </a>
    </td>
    <!-- /ko -->
    <!-- /Виджет -->

    <!-- Сайт -->
    <!-- ko if: $root.table.columns.has('anketaSite') -->
    <td class="reviews-list__cell reviews-list__cell--anketaSite">
      <a 
        class="reviews-list__cell-content reviews-list__cell-content--link position-relative d-block f-color-primary" 
        data-bind="
          attr: { href: review.anketaWidgetProtocol + '://' + review.anketaSite },
          click: function(_, event) {
            event.stopPropagation();
            event.stopImmediatePropagation();
            window.open(review.anketaWidgetProtocol + '://' + review.anketaSite, '_blank');
          }
        "
        target="_blank">
        <span 
          class="reviews-list__cell-content-link-text" 
          data-bind="
            text: review.anketaSite,
            tooltip,
            tooltipText: review.anketaSite,
            tooltipPlacement: 'bottom'
          "></span>
      </a>
    </td>
    <!-- /ko -->
    <!-- /Сайт -->

    <!-- Источник -->
    <!-- ko if: $root.table.columns.has('anketaSource') -->
    <td class="reviews-list__cell reviews-list__cell--anketaSource">
      <a 
        class="reviews-list__cell-content reviews-list__cell-content--link position-relative d-block f-color-primary" 
        data-bind="
          attr: { href: review.anketaWidgetProtocol + '://' + review.anketaSource },
          click: function(_, event) {
            event.stopPropagation();
            event.stopImmediatePropagation();
            window.open(review.anketaWidgetProtocol + '://' + review.anketaSource, '_blank');
          }
        "
        target="_blank"
        >
        <span 
          class="reviews-list__cell-content-link-text" 
          data-bind="
            text: review.anketaSource,
            tooltip,
            tooltipText: review.anketaSource,
            tooltipPlacement: 'bottom',
            tooltipContentStyle: {
              wordBreak: 'break-all',
            },
            tooltipContentClass: 'tooltip-inner-word-break',
          "
          style="
            max-height: 2.35em; 
            overflow: hidden; 
            text-overflow: ellipsis; 
            display: -webkit-box; 
            -webkit-line-clamp: 2; 
            -webkit-box-orient: vertical;
            word-break: break-all;"
        ></span>
      </a>
    </td>
    <!-- /ko -->
    <!-- /Источник -->

    <!-- Оценки -->
    <!-- ko if: $root.table.columns.has('points') -->
    <td class="reviews-list__cell reviews-list__cell--points">

      <div class="reviews-list__cell-content">
        <!-- ko if: review.ratingAnswers().length > 0-->
        <div class="points position-relative" data-bind="click: function(_, e) {
                e.stopPropagation();
                return false;
               $root.openComments(review);
              }, css: {
               'points--with-comments': review.ratingAnswersWithComments().length > 0
             }">

          <!-- ko foreach: { data: review.sortedAnswersWithRatingAndNotSkipped().slice(0, 5), as: 'answer' } -->
            <!-- ko if: answer.rating !== -1 -->
              <rating-point params="
                value: answer.rating, 
                point: answer.ratingPoint, 
                type: answer.ratingView, 
                comment: answer.comment,
                icon: answer.icon,
                variantSkipped: answer.variantSkipped,
                clarifyingComments: answer.clarifyingComments,
              " data-bind="
                attr: {
                  title: answer.comment ? question.name + '<br>' + answer.comment : question.name
                }
              " data-html="true" data-boundary="window" data-toggle="tooltip"></rating-point>
            <!-- /ko -->
          <!-- /ko -->
        </div>
        <!-- /ko -->
      </div>

    </td>
    <!-- /ko -->
    <!-- /Оценки -->

    <!-- Комментарий -->
    <!-- ko if: $root.table.columns.has('comments') -->
    <td class="reviews-list__cell reviews-list__cell--comments">
      <div class="reviews-list__cell-content">
        <!-- ko if: review.answersWithComments().length > 0 -->

        <div class="d-flex align-items-center position-relative" data-bind="click: function(_, e) {
            e.stopPropagation();
            $root.openComments(review, true);
          }">
          <i class="icon icon-comment2 icon-comment2--hover mr-1"></i>
          <span class="color-active review__comments-count" data-bind="text: review.commentsLength"></span>
        </div>
        <!-- /ko -->
      </div>

    </td>
    <!-- /ko -->
    <!-- /Комментарий -->

    <!-- Теги анкеты -->
    <!-- ko if: $root.table.columns.has('answerTags') -->
    <td class="reviews-list__cell reviews-list__cell--answerTags">
      <div
        class="reviews-list__cell-content position-relative"
        data-bind="
            component: {
              name: 'clients-tag-input',
              params: {
                afterAddTag: function() { $root.directories.tags.load(true) },
                value: review.answerTags,
                list: $root.directories.tags.data,
                answer_id: review.id,
              }
            },
            click: function (_, event) { event.stopPropagation() }
        "
      ></div>
    </td>
    <!-- /ko -->
    <!-- /Теги анкеты -->

    <!-- Жалоба -->
    <!-- ko if: $root.table.columns.has('complaint') -->
    <td class="reviews-list__cell reviews-list__cell--complaint">
      <div class="reviews-list__cell-content">
        <!-- ko if: review.complaint -->
        <div class="review-complaint">
          <!-- ko if: review.complaint.text -->
          <div class="review-complaint__text" data-bind="text: review.complaint.text, attr: {title: review.complaint.text}, tooltip" data-boundary="viewport">

          </div>
          <!-- /ko -->
          <!-- ko if: review.complaint.photoUrls.length > 0 -->
          <div class="review-complaint__photos d-flex align-items-center mt-2" data-bind="click: function(_, event) {
                        event.stopPropagation();
                      },
                      fancybox: {
                        urls: review.complaint.photoUrls,
                        caption: function() {
                          return review.complaint.text
                        }
                      }">
            <i class="icon icon-image-bold mr-2"></i>
            <span class="color-active" data-bind="text: review.complaint.photoUrls.length + ' фото'"></span>
          </div>
          <!-- /ko -->
        </div>
        <!-- /ko -->
      </div>
    </td>
    <!-- /ko -->
    <!-- /Жалоба -->

    <!-- Баллов набрано -->
    <!-- ko if: $root.table.columns.has('pointsCollect') -->
    <td class="reviews-list__cell reviews-list__cell--pointsCollect">
      <div class="reviews-list__cell-content">

        <!-- ko if: review.withPoints() && !review.without_points -->
        <div class="mb-2">
          <?= \Yii::t('main', '{num1} из {num2}', [
            'num1' => '<span class="bold" data-bind="text: review.points().answer_points"></span>',
            'num2' => '<span class="bold" data-bind="text: review.points().points_max"></span>',
          ]) ?>,
          <span class="font-weight-normal" data-bind="text: review.points().percent"></span>%
        </div>
        <progress-line style="width: 90px" params="progress: review.points().percent"></progress-line>
        <!-- /ko -->

        <!-- ko if: !review.withPoints() && !review.without_points -->
        &mdash;
        <!-- /ko -->
        <!-- ko if: !review.withPoints() && review.without_points -->
        Нет ответов<br/>с баллами
        <!-- /ko -->

      </div>
    </td>
    <!-- /ko -->
    <!-- /Баллов набрано -->

    <!-- Клиент -->
    <!-- ko if: $root.table.columns.has('clientName') -->
    <td class="reviews-list__cell reviews-list__cell--clientName">
      <div class="reviews-list__cell-content hyphenate" data-bind="text: review.clientName">

      </div>
    </td>
    <!-- /ko -->
    <!-- /Клиент -->

    <!-- Телефон -->
    <!-- ko if: $root.table.columns.has('clientPhone') -->
    <td class="reviews-list__cell reviews-list__cell--clientPhone">
      <div class="reviews-list__cell-content" data-bind="text: review.clientPhone">

      </div>
    </td>
    <!-- /ko -->
    <!-- /Телефон -->

    <!-- Email -->
    <!-- ko if: $root.table.columns.has('clientEmail') -->
    <td class="reviews-list__cell reviews-list__cell--clientEmail">
      <div class="reviews-list__cell-content" data-bind="html: formattedEmail(review.clientEmail)">

      </div>
    </td>
    <!-- /ko -->
    <!-- /Email -->

    <!-- ko foreach: $root.clientSystemFields -->
    <!-- ko if: $root.table.columns.has($data.fullName) -->
    <td class="reviews-list__cell reviews-list__cell--clientField">
      <div class="reviews-list__cell-content" data-bind="style: {
        width: $data.width + 'px'
      }, html: review.clientFields[$data.id]">

      </div>
    </td>
    <!-- /ko -->
    <!-- /ko -->

    <!-- ko foreach: $root.clientFields -->
    <!-- ko if: $root.table.columns.has($data.fullName) -->
    <td class="reviews-list__cell reviews-list__cell--clientField">
      <div class="reviews-list__cell-content" data-bind="style: {
        width: $data.width + 'px'
      }, html: review.clientFields[$data.id]">

      </div>
    </td>
    <!-- /ko -->
    <!-- /ko -->

    <!-- Модератор -->
    <!-- ko if: $root.table.columns.has('moderator') -->
    <td class="reviews-list__cell reviews-list__cell--moderator">
      <div class="reviews-list__cell-content" data-bind="text: review.moderator() ? review.moderator().name : ''">

      </div>
    </td>
    <!-- /ko -->
    <!-- /Модератор -->

    <!-- Исполнитель -->
    <!-- ko if: $root.table.columns.has('executor') -->
    <td class="reviews-list__cell reviews-list__cell--executor">
      <div class="reviews-list__cell-content" data-bind="text: review.executor() ? review.executor().name : ''">

      </div>
    </td>
    <!-- /ko -->
    <!-- /Исполнитель -->

    <!-- Номер заказа -->
    <!-- ko if: $root.table.columns.has('orderNumber') -->
    <td class="reviews-list__cell reviews-list__cell--orderNumber">
      <div class="reviews-list__cell-content" data-bind="text: review.order.number">

      </div>
    </td>
    <!-- /ko -->
    <!-- /Номер заказа -->

    <!-- Время заказа -->
    <!-- ko if: $root.table.columns.has('orderTime')-->
    <td class="reviews-list__cell reviews-list__cell--orderTime">
      <div class="reviews-list__cell-content" data-bind="text: review.order.createdTime">

      </div>
    </td>
    <!-- /ko -->
    <!-- /Время заказа -->

    <!-- Сумма заказа -->
    <!-- ko if: $root.table.columns.has('orderSum') -->
    <td class="reviews-list__cell reviews-list__cell--orderSum">
      <div class="reviews-list__cell-content">
        <!-- ko if: review.order.sum -->
        <!-- ko text: review.order.sum -->
        <!-- /ko -->&nbsp;<i class="far fa-ruble-sign statistics__details-modal-dialog-history-orders-table-order-list-table-price-ruble-sign"></i>
        <!-- /ko -->
      </div>
    </td>
    <!-- /ko -->
    <!-- /Сумма заказа -->

    <!-- Тип доставки -->
    <!-- ko if: $root.table.columns.has('orderType') -->
    <td class="reviews-list__cell reviews-list__cell--orderType">
      <div class="reviews-list__cell-content">
        <!-- ko text: $root.getOrderType(review.order.deliveryType) -->

        <!-- /ko -->
      </div>
    </td>
    <!-- /ko -->
    <!-- /Тип доставки -->

    <!-- Способ оформления -->
    <!-- ko if: $root.table.columns.has('sourceType') -->
    <td class="reviews-list__cell reviews-list__cell--sourceType">
      <div class="reviews-list__cell-content">
        <!-- ko if: review.order.sourceType -->
        <!-- ko text: $root.getSourceType(review.order.sourceType) -->
        <!-- /ko -->
        <!-- /ko -->
      </div>
    </td>
    <!-- /ko -->
    <!-- /Способ оформления -->

    <!-- Адрес доставки -->
    <!-- ko if: $root.table.columns.has('orderAddress') -->
    <td class="reviews-list__cell reviews-list__cell--orderAddress">
      <div class="reviews-list__cell-content" data-bind="text: review.order.deliveryAddress">

      </div>
    </td>
    <!-- /ko -->
    <!-- /Адрес доставки -->
    
    <td class="outer-cell"></td>
  </tr>
</template>

<template id="review-kanban-card-template">
  <div class="review-card" data-bind="click: function() {
    $root.openDetailsModal($data)
  }">
    <!-- ko ifnot: $root.single -->
    <header class="review-card__header">
      <!-- ko if: $data.isAuto -->
      <i class="icon icon-automatic" data-bind="tooltip" data-placement="top" title="<?= \Yii::t('main', 'Автоматический') ?>"></i>

      <!-- /ko -->
      <!-- ko ifnot: $data.isAuto -->
      <i class="icon icon-manual" data-bind="tooltip" data-placement="top" title="<?= \Yii::t('main', 'Ручной') ?>"></i>
      <!-- /ko -->

      <span class="review-card__poll-name" data-bind="tooltip, attr: {'data-original-title': $data.pollName}">
        <span data-bind="text: $data.pollName"></span>
      </span>
    </header>
    <!-- /ko -->


    <div class="review-card__content" data-bind="let: {
      user: $data.processingStatus() == 4 ? $data.executor : $data.moderator
    }">
      <div class="review-card__row">
        <div class="review-card__executor review-executor d-flex align-items-center">
          <!-- ko if: user -->

          <span class="review-executor__userpic userpic mr-2">
            <img data-bind="attr: { src: user().avatar || '/assets/img/user-placeholder.png' }">
          </span>

          <span class="review-executor__name" data-bind="text: user().name"></span>
          <!-- /ko -->


          <!-- ko ifnot: user -->
          <span class="review-executor__name color-service"><?= \Yii::t('answers', 'Не назначен') ?></span>
          <!-- /ko -->
        </div>

        <div class="review-card__complaint">
          <!-- ko if: $data.complaint -->
          <i class="icon icon-complaint icon-complaint--error" title="<?= \Yii::t('answers', 'Есть жалоба') ?>" data-bind="tooltip"></i>
          <!-- /ko -->
        </div>
      </div>


      <div class="review-card__rating">
        <!-- ko if: $data.ratingAnswers && $data.ratingAnswers.length > 0 -->

        <div class="points points--row" data-bind="click: function(_, e) {
          e.preventDefault();
          e.stopPropagation();
          $root.openComments($data);
        }">
          <div class="points-wrapper" data-bind="hoverScroll">
            <!-- ko foreach: { data: $data.sortedAnswersWithRatingAndNotSkipped, as: 'answer' } -->
            <rating-point params="value: answer.rating, point: answer.ratingPoint, type: answer.ratingView, comment: answer.comment" data-bind="attr: {
              title: answer.comment ? question.name + '<br>' + answer.comment : question.name
            }" data-html="true" data-boundary="window" data-toggle="tooltip"></rating-point>
            <!-- /ko -->

          </div>
        </div>

        <!-- /ko -->
      </div>

    </div>

    <footer class="review-card__footer">
      <!-- ko if: $data.passedAt -->
      <span class="review-card__time color-active" title="<?= \Yii::t('answers', 'Дата и время прохождения опроса') ?>" data-bind="text: $data.passedAt, tooltip"></span>
      <!-- /ko -->
      <span class="review-card__answers color-service" title="<?= \Yii::t('answers', 'Получено ответов') ?>" data-bind="tooltip">
        <!-- ko text: $data.answersReceived && $data.answersReceived() -->
        <!-- /ko -->
        /
        <!-- ko text: $data.answers.length -->
        <!-- /ko -->
      </span>
      <!-- ko if: order.number -->
      <span class="review-card__order color-text" title="<?= \Yii::t('main', 'Номер заказа') ?>" data-bind="text: '#' + order.number, tooltip"></span>
      <!-- /ko -->
    </footer>
  </div>
</template>

<template id="folder-template">
  <option data-bind="value: folder.id, text: folder.name, attr: {'data-level': level, 'data-parent': folder.parentName	}">
  </option>
  <!-- ko foreach: { data: folder.items, as: 'item' } -->
  <!-- ko template: { name: 'folder-template', data: { folder: item, level: $parent.level + 1 } } -->
  <!-- /ko -->
  <!-- /ko -->
</template>

<script type="text/html" id="review-rating-modal-template">
  <div data-bind="component: { name: 'review-rating-modal-dialog', params: { data, modalElement, cancel: function () { close(); }, submit: function () { close(true); } } }" role="document"></div>
</script>

<script type="text/html" id="change-status-modal-template">
  <div data-bind="component: { name: 'change-status-modal-dialog', params: { data, modalElement, cancel: function () { close(); }, submit: function (data) { close(data); } } }" role="document"></div>
</script>

<template id="kanban-template">
  <!-- ko let: { $kanban: $component } -->
  <div class="mt-10p" data-bind="component: {name: 'scroll-container'}">

    <div class="kanban-header" data-bind="component: { name: 'scroll-container-content'}, stickyWatcher">
      <div class="kanban mt-10p">
        <div class="kanban-offset"></div>

        <div class="kanban-column kanban-column--new">
          <div class="kanban-column__header"><?= \Yii::t('answers', 'Новая') ?></div>
        </div>

        <div class="kanban-column kanban-column--in-process">
          <div class="kanban-column__header"><?= \Yii::t('answers', 'В процессе') ?></div>


        </div>

        <div class="kanban-column kanban-column--handle">
          <div class="kanban-column__header"><?= \Yii::t('answers', 'Обрабатывается исполнителем') ?></div>

        </div>

        <div class="kanban-column kanban-column--delayed">
          <div class="kanban-column__header"><?= \Yii::t('answers', 'Отложена') ?></div>

        </div>

        <div class="kanban-column kanban-column--completed">
          <div class="kanban-column__header"><?= \Yii::t('answers', 'Обработана') ?></div>


        </div>
        <div class="kanban-offset"></div>

      </div>
    </div>

    <div class="kanban-sticky">
      <div class="kanban-sticky__bg"></div>
      <div class="kanban-sticky__shadow"></div>
    </div>

    <div data-bind="component: { name: 'scroll-container-content'}">

      <div class="kanban mt-n2" data-bind="kanbanBoard, kanbanBoardMove: tryMove">
        <div class="kanban-offset"></div>


        <div class="kanban-column kanban-column--new" data-column-status="0">


          <div class="kanban-column__content">
            <!-- ko foreach: $kanban.newItems -->
            <div class="kanban-card">
              <!-- ko template: { name: $kanban.cardTemplateName, data: $data } -->
              <!-- /ko -->
            </div>
            <!-- /ko -->
          </div>
        </div>

        <div class="kanban-column kanban-column--in-process" data-column-status="1">


          <div class="kanban-column__content">
            <!-- ko foreach: $kanban.processItems -->
            <div class="kanban-card">
              <!-- ko template: { name: $kanban.cardTemplateName, data: $data } -->
              <!-- /ko -->
            </div>
            <!-- /ko -->
          </div>
        </div>

        <div class="kanban-column kanban-column--handle" data-column-status="4">

          <div class="kanban-column__content">
            <!-- ko foreach: $kanban.handleItems -->
            <div class="kanban-card">
              <!-- ko template: { name: $kanban.cardTemplateName, data: $data } -->
              <!-- /ko -->
            </div>
            <!-- /ko -->
          </div>
        </div>

        <div class="kanban-column kanban-column--delayed" data-column-status="2">

          <div class="kanban-column__content">
            <!-- ko foreach: $kanban.delayedItems -->
            <div class="kanban-card">
              <!-- ko template: { name: $kanban.cardTemplateName, data: $data } -->
              <!-- /ko -->
            </div>
            <!-- /ko -->
          </div>
        </div>

        <div class="kanban-column kanban-column--completed" data-column-status="3">


          <div class="kanban-column__content">
            <!-- ko foreach: $kanban.completedItems -->
            <div class="kanban-card">
              <!-- ko template: { name: $kanban.cardTemplateName, data: $data } -->
              <!-- /ko -->
            </div>
            <!-- /ko -->
          </div>
        </div>

        <div class="kanban-offset"></div>


      </div><!-- .kanban-board -->

    </div>
  </div>
  <!-- /ko -->
</template>

<template id="sending-list-template">
  <sending-list params="list: list"></sending-list>
</template>

<template id="answer-channel-dropdown-template">
  <!-- ko if: answer.answerChannel -->
  <!-- ko let: {
    answerChannelType: answer.getChannelType(answer.answerChannel.channel_name)
  } -->

  <!-- ko ifnot: answerChannelType -->
  <span class="cursor-default">
    <!-- ko text: answer.answerChannel.channel_name -->
    <!-- /ko -->
  </span>
  <!-- /ko -->


  <!-- ko if: answerChannelType -->
  <div class="position-relative" data-bind="stopClick">
    <div data-bind="let: { list: answer.getChannelsStats() }">

      <div class="d-inline-flex align-items-center" data-bind="dropdown: 'sending-list-template'">
        <foquz-icon class="mr-2" params="prefix: 'channel', icon: answerChannelType"></foquz-icon>
        <!-- ko text:answer.answerChannel.channel_name -->
        <!-- /ko -->
      </div>

    </div>
  </div>
  <!-- /ko -->

  <!-- /ko -->
  <!-- /ko -->
</template>

<template id="s-answers-list-mobile-template">
  <div class="reviews-mobile-table" data-bind="fScrollbar: { onlyX: true }">
    <div class="d-inline-block position-relative" style="min-width: 100%">
      <table class="fixed-table">
        <tbody>
          <!-- ko if: $root.hasProcessingOptions -->
          <tr>
            <th style="height: 40px; border-bottom-width: 2px;">

            </th>
            <!-- ko foreach: { data:  $root.reviews.reviews, as: 'review' } -->
            <td style=" border-bottom-width: 2px;">
              <!-- ko if: review.processingEnabled -->
              <svg-icon class="f-color-danger" params="name: 'exclamation-circle', width: 16, height: 16" data-bind="tooltip, tooltipText: _t('answers', 'Анкета с обработкой')"></svg-icon>
              <!-- /ko -->
            </td>
            <!-- /ko -->
          </tr>
          <!-- /ko -->

          <!-- ko if: $root.table.columns.has('type') || $root.table.columns.has('pollName') -->
          <tr>
            <th>
              <?= \Yii::t('answers', 'Тип/название опроса') ?>
            </th>


            <!-- ko foreach: { data:  $root.reviews.reviews, as: 'review' } -->
            <td data-bind="css: {
                      'reviews-list__row--highlighted': review.checked
                      },
                      click: function(_, e) {
                        $root.openDetailsModal(review);
                      }">
              <div class="d-flex align-items-center">
                <div class="mr-2" data-bind="attr: {
                  title: review.isAuto ? _t('Автоматический') : _t('Ручной')
                }, tooltip">
                  <!-- ko if: review.isAuto -->
                  <i class="icon icon-automatic"></i>
                  <!-- /ko -->
                  <!-- ko ifnot: review.isAuto -->
                  <i class="icon icon-manual"></i>
                  <!-- /ko -->
                </div>

                <div data-bind="text: review.pollName">

                </div>
              </div>

            </td>
            <!-- /ko -->
          </tr>
          <!-- /ko -->

          <!-- ko if: $root.table.columns.has('passedAt') -->
          <tr>
            <th>
              <?= \Yii::t('main', 'Пройден') ?>
            </th>


            <!-- ko foreach: { data:  $root.reviews.reviews, as: 'review' } -->
            <td data-bind="css: {
                      'reviews-list__row--highlighted': review.checked
                      },
                      click: function(_, e) {
                        $root.openDetailsModal(review);
                      }">
              <div class="reviews-list__cell-content" data-bind="text: review.passedAt">

              </div>
            </td>
            <!-- /ko -->
          </tr>
          <!-- /ko -->

          <!-- ko if: $root.table.columns.has('anketaStatus') -->
          <tr>
            <th>
              <?= \Yii::t('main', 'Статус анкеты') ?>
            </th>
            <!-- ko foreach: { data:  $root.reviews.reviews, as: 'review' } -->
            <td data-bind="css: {
                      'reviews-list__row--highlighted': review.checked
                      },
                      click: function(_, e) {
                        $root.openDetailsModal(review);
                      }">
              <div class="reviews-list__cell-content">
                <span data-bind="text: review.anketaStatus"></span>
              </div>
            </td>
            <!-- /ko -->
          </tr>
          <!-- /ko -->
          
          <!-- ko if: $root.table.columns.has('anketaLink') -->
          <tr>
            <th>
              <?= \Yii::t('main', 'Ссылка') ?>
            </th>
            <!-- ko foreach: { data:  $root.reviews.reviews, as: 'review' } -->
            <td data-bind="css: {
                      'reviews-list__row--highlighted': review.checked
                      },
                      click: function(_, e) {
                        $root.openDetailsModal(review);
                      }">
              <div class="reviews-list__cell-content">
                <span data-bind="text: review.anketaLink, css: {'semibold': !review.anketaLinkStatus, 'f-color-muted': !review.anketaLinkStatus, 'f-color-service': !review.anketaLinkStatus}"></span>
              </div>
            </td>
            <!-- /ko -->
          </tr>
          <!-- /ko -->

          <!-- ko if: $root.table.columns.has('anketaQuote') -->
          <tr>
            <th>
              <?= \Yii::t('main', 'Квота') ?>
            </th>
            <!-- ko foreach: { data:  $root.reviews.reviews, as: 'review' } -->
            <td data-bind="css: {
                      'reviews-list__row--highlighted': review.checked
                      },
                      click: function(_, e) {
                        $root.openDetailsModal(review);
                      }">
                <div class="reviews-list__cell-content">
                  <span data-bind="text: review.anketaQuote, css: {'semibold': !review.anketaLinkStatus, 'f-color-muted': !review.anketaLinkStatus, 'f-color-service': !review.anketaLinkStatus}" ></span>
                </div>
              </td>
            <!-- /ko -->
          </tr>
          <!-- /ko -->

          <!-- ko if: $root.hasFilials() && $root.table.columns.has('filial') -->
          <tr>
            <th>
              <?= \Yii::t('main', 'Филиал') ?>
            </th>

            <!-- ko foreach: { data:  $root.reviews.reviews, as: 'review' } -->
            <td data-bind="css: {
                      'reviews-list__row--highlighted': review.checked
                      },
                      click: function(_, e) {
                        $root.openDetailsModal(review);
                      }">
              <div class="reviews-list__cell-content">
                <filial-text params="filial: review.filial"></filial-text>
              </div>
            </td>
            <!-- /ko -->



          </tr>
          <!-- /ko -->

          <!-- ko if: $root.table.columns.has('processingStatus') -->
          <tr>
            <th>
              <?= \Yii::t('main', 'Статус обработки') ?>
            </th>

            <!-- ko foreach: { data:  $root.reviews.reviews, as: 'review' } -->
            <td data-bind="css: {
                      'reviews-list__row--highlighted': review.checked,

                      },
                      click: function(_, e) {

                        $root.openDetailsModal(review);
                      }">
              <div class="reviews-list__cell-content">
                <span
                  class="processing-status"
                  data-bind="
                    text: review.processingStatusText(),
                    css: 'processing-status--' + review.processingStatus(),
                  "
                ></span>
              </div>
            </td>
            <!-- /ko -->

          </tr>
          <!-- /ko -->

          <!-- ko if: $root.table.columns.has('processingTime') -->
          <tr>
            <th>
              <?= \Yii::t('main', 'Срок обработки') ?>
            </th>

            <!-- ko foreach: { data:  $root.reviews.reviews, as: 'review' } -->
            <td data-bind="css: {
                      'reviews-list__row--highlighted': review.checked,

                      },
                      click: function(_, e) {

                        $root.openDetailsModal(review);
                      }">
              <div class="reviews-list__cell-content">


                <!-- ko if: review.processToDate -->
                <span data-bind="text: review.processToDate"></span>
                <!-- /ko -->
              </div>
            </td>
            <!-- /ko -->

          </tr>
          <!-- /ko -->

          <!-- ko if: $root.table.columns.has('channel') -->
          <tr>
            <th>
              <?= \Yii::t('main', 'Канал') ?>
            </th>

            <!-- ko foreach: { data:  $root.reviews.reviews, as: 'review' } -->
            <td data-bind="css: {
                      'reviews-list__row--highlighted': review.checked
                      },">
              <div class="reviews-list__cell-content">
                <!-- ko template: {
                  name: 'answer-channel-dropdown-template',
                  data: {
                    answer: $data
                  }
                } -->
                <!-- /ko -->

              </div>
            </td>
            <!-- /ko -->



          </tr>
          <!-- /ko -->

          <!-- ko if: $root.table.columns.has('device') -->
          <tr>
            <th>
              <?= \Yii::t('main', 'Устройство') ?>
            </th>

            <!-- ko foreach: { data:  $root.reviews.reviews, as: 'review' } -->
            <td data-bind="css: {
                      'reviews-list__row--highlighted': review.checked
                      },">
              <div class="reviews-list__cell-content">
                <!-- ko if: review.device -->
                <span data-bind="tooltip, tooltipText: 'Устройство: ' + review.deviceName">
                  <svg-icon params="name: 'viewport-' + review.device" class="f-color-service"></svg-icon>
                </span>
                <!-- /ko -->

              </div>
            </td>
            <!-- /ko -->
          </tr>
          <!-- /ko -->

          <!-- ko if: $root.table.columns.has('answerTime') -->
          <tr>
            <th>
              <?= \Yii::t('main', 'Время') ?>
            </th>
            <!-- ko foreach: { data:  $root.reviews.reviews, as: 'review' } -->
            <td data-bind="css: {'reviews-list__row--highlighted': review.checked},">
              <div class="reviews-list__cell-content" data-bind="text: review.answerTime"></div>
            </td>
            <!-- /ko -->
          </tr>
          <!-- /ko -->

          <!-- ko if: $root.table.columns.has('mailing') -->
          <tr>
            <th>
              <?= \Yii::t('main', 'Рассылка') ?>
            </th>

            <!-- ko foreach: { data:  $root.reviews.reviews, as: 'review' } -->
            <td data-bind="css: {
                      'reviews-list__row--highlighted': review.checked
                      },">
              <div class="reviews-list__cell-content" data-bind="text: review.mailing">

              </div>
            </td>
            <!-- /ko -->



          </tr>
          <!-- /ko -->

          <!-- ko if: $root.table.columns.has('points') -->
          <tr>
            <th>
              <?= \Yii::t('main', 'Оценки') ?>
            </th>

            <!-- ko foreach: { data:  $root.reviews.reviews, as: 'review' } -->
            <td data-bind="css: {
                      'reviews-list__row--highlighted': review.checked
                      }," class="pt-0">
              <div class="reviews-list__cell-content">
                <!-- ko if: $data.ratingAnswers && $data.ratingAnswers.length > 0 -->
                <div class="points" data-bind="click: function() { $root.openComments($data) }, css: {
                      'points--with-comments': review.ratingAnswersWithComments().length > 0
                    }">
                  <!-- ko foreach: { data: review.sortedAnswersWithRatingAndNotSkipped().slice(0, 5), as: 'answer' } -->
                    <!-- ko if: answer.rating !== -1 -->
                      <rating-point params="value: answer.rating, point: answer.ratingPoint, type: answer.ratingView, comment: answer.comment"></rating-point>
                    <!-- /ko -->
                  <!-- /ko -->
                </div>
                <!-- /ko -->
              </div>
            </td>
            <!-- /ko -->



          </tr>
          <!-- /ko -->

          <!-- ko if: $root.table.columns.has('comments') -->
          <tr>
            <th>
              <?= \Yii::t('main', 'Комментарий') ?>
            </th>

            <!-- ko foreach: { data:  $root.reviews.reviews, as: 'review' } -->
            <td data-bind="css: {
                      'reviews-list__row--highlighted': review.checked
                      },">
              <div class="reviews-list__cell-content">
                <!-- ko if: review.answersWithComments().length > 0 -->

                <div class="d-flex align-items-center position-relative" data-bind="click: function(_, e) {
                  if (review.ratingAnswersWithComments().length > 0) {
                    e.stopPropagation();
                    return false;
                    $root.openComments(review);
                  }
                  }">
                  <i class="icon icon-comment2 icon-comment2--hover mr-1"></i>
                  <span class="color-active review__comments-count" data-bind="text: review.answersWithComments().length"></span>
                </div>
                <!-- /ko -->
              </div>
            </td>
            <!-- /ko -->
          </tr>
          <!-- /ko -->

          <!-- Теги анкеты -->
          <!-- ko if: $root.table.columns.has('answerTags') -->
          <tr>
            <th>
              <?= \Yii::t('main', 'Теги анкеты') ?>
            </th>
            <!-- ko foreach: { data:  $root.reviews.reviews, as: 'review' } -->
            <td class="reviews-list__cell reviews-list__cell--answerTags"
              data-bind="css: {
                'reviews-list__row--highlighted': review.checked
              }"
            >
              <div
                class="reviews-list__cell-content position-relative"
                data-bind="
                    component: {
                      name: 'clients-tag-input',
                      params: {
                        afterAddTag: function() { $root.directories.tags.load(true) },
                        value: review.answerTags,
                        list: $root.directories.tags.data,
                        answer_id: review.id,
                      }
                    },
                    click: function (_, event) { event.stopPropagation() }
                "
              ></div>
            </td>
            <!-- /ko -->
          </tr>
          <!-- /ko -->

          <!-- ko if: $root.table.columns.has('complaint') -->
          <tr>
            <th>
              <?= \Yii::t('main', 'Жалоба') ?>
            </th>


            <!-- ko foreach: { data:  $root.reviews.reviews, as: 'review' } -->
            <td data-bind="css: {
                      'reviews-list__row--highlighted': review.checked
                      },">
              <div class="reviews-list__cell-content">
                <!-- ko if: review.complaint -->
                <div class="review-complaint">
                  <!-- ko if: review.complaint.text -->
                  <div class="review-complaint__text" data-bind="text: review.complaint.text, attr: {title: review.complaint.text}, tooltip" data-boundary="viewport">

                  </div>
                  <!-- /ko -->
                  <!-- ko if: review.complaint.photoUrls.length > 0 -->
                  <div class="review-complaint__photos d-flex align-items-center mt-2" data-bind="click: function(_, event) {
                            event.stopPropagation();
                          },
                          fancybox: {
                            urls: review.complaint.photoUrls,
                            caption: function() {
                              return review.complaint.text
                            }
                          }">
                    <i class="icon icon-image-bold mr-2"></i>
                    <span class="color-active" data-bind="text: review.complaint.photoUrls.length + ' фото'"></span>
                  </div>
                  <!-- /ko -->
                </div>
                <!-- /ko -->
              </div>
            </td>
            <!-- /ko -->



          </tr>
          <!-- /ko -->

          <!-- ko if: $root.table.columns.has('clientName') -->
          <tr>
            <th>
              <?= \Yii::t('main', 'ФИО') ?>
            </th>

            <!-- ko foreach: { data:  $root.reviews.reviews, as: 'review' } -->
            <td data-bind="css: {
                      'reviews-list__row--highlighted': review.checked
                      },
                      click: function(_, e) {
                        $root.openDetailsModal(review);
                      }">
              <div class="reviews-list__cell-content hyphenate" data-bind="text: review.clientName">

              </div>
            </td>
            <!-- /ko -->



          </tr>
          <!-- /ko -->

          <!-- ko if: $root.table.columns.has('clientPhone') -->
          <tr>
            <th><?= \Yii::t('main', 'Телефон') ?>

            </th>

            <!-- ko foreach: { data:  $root.reviews.reviews, as: 'review' } -->
            <td data-bind="css: {
                      'reviews-list__row--highlighted': review.checked
                      },
                      click: function(_, e) {
                        $root.openDetailsModal(review);
                      }">
              <div class="reviews-list__cell-content" data-bind="text: review.clientPhone">

              </div>
            </td>
            <!-- /ko -->



          </tr>
          <!-- /ko -->

          <!-- ko if: $root.table.columns.has('clientEmail') -->
          <tr>
            <th><?= \Yii::t('main', 'Email') ?>

            </th>

            <!-- ko foreach: { data:  $root.reviews.reviews, as: 'review' } -->
            <td data-bind="css: {
                      'reviews-list__row--highlighted': review.checked
                      },
                      click: function(_, e) {

                        $root.openDetailsModal(review);
                      }">
              <div class="reviews-list__cell-content" data-bind="html: formattedEmail(review.clientEmail)">

              </div>
            </td>
            <!-- /ko -->



          </tr>
          <!-- /ko -->


          <!-- ko foreach: { data: $root.clientSystemFields, as: 'field' } -->
          <!-- ko if: $root.table.columns.has(field.fullName) -->
          <tr>
            <th data-bind="text: field.text">

            </th>

            <!-- ko foreach: { data:  $root.reviews.reviews, as: 'review' } -->
            <td data-bind="css: {
                      'reviews-list__row--highlighted': review.checked
                      },
                      click: function(_, e) {

                        $root.openDetailsModal(review);
                      }">
              <div class="reviews-list__cell-content" data-bind="html: review.clientFields[field.id]">

              </div>
            </td>
            <!-- /ko -->



          </tr>
          <!-- /ko -->
          <!-- /ko -->

          <!-- ko foreach: { data: $root.clientFields, as: 'field' } -->
          <!-- ko if: $root.table.columns.has(field.fullName) -->
          <tr>
            <th data-bind="text: field.text">

            </th>

            <!-- ko foreach: { data:  $root.reviews.reviews, as: 'review' } -->
            <td data-bind="css: {
                      'reviews-list__row--highlighted': review.checked
                      },
                      click: function(_, e) {

                        $root.openDetailsModal(review);
                      }">
              <div class="reviews-list__cell-content" data-bind="html: review.clientFields[field.id]">

              </div>
            </td>
            <!-- /ko -->



          </tr>
          <!-- /ko -->
          <!-- /ko -->


          <!-- ko if: $root.table.columns.has('moderator') -->
          <tr>
            <th>
              <?= \Yii::t('answers', 'Модератор') ?>
            </th>

            <!-- ko foreach: { data:  $root.reviews.reviews, as: 'review' } -->
            <td data-bind="css: {
                      'reviews-list__row--highlighted': review.checked
                      },
                      click: function(_, e) {
                        $root.openDetailsModal(review);
                      }">
              <div class="reviews-list__cell-content" data-bind="text: review.moderator() ? review.moderator().name : ''">

              </div>
            </td>
            <!-- /ko -->



          </tr>
          <!-- /ko -->

          <!-- ko if: $root.table.columns.has('orderNumber') -->
          <tr>
            <th>
              <?= \Yii::t('main', 'Номер заказа') ?>
            </th>

            <!-- ko foreach: { data:  $root.reviews.reviews, as: 'review' } -->
            <td data-bind="css: {
                      'reviews-list__row--highlighted': review.checked
                      },
                      click: function(_, e) {
                        $root.openDetailsModal(review);
                      }">
              <div class="reviews-list__cell-content" data-bind="text: review.order.number">

              </div>
            </td>
            <!-- /ko -->



          </tr>
          <!-- /ko -->

          <!-- ko if: $root.table.columns.has('orderTime') -->
          <tr>
            <th>
              <?= \Yii::t('main', 'Время заказа') ?>
            </th>

            <!-- ko foreach: { data:  $root.reviews.reviews, as: 'review' } -->
            <td data-bind="css: {
                      'reviews-list__row--highlighted': review.checked
                      },
                      click: function(_, e) {
                        $root.openDetailsModal(review);
                      }">
              <div class="reviews-list__cell-content" data-bind="text: review.order.createdTime">

              </div>
            </td>
            <!-- /ko -->



          </tr>
          <!-- /ko -->

          <!-- ko if: $root.table.columns.has('orderSum') -->
          <tr>
            <th>
              <?= \Yii::t('main', 'Сумма заказа') ?>
            </th>

            <!-- ko foreach: { data:  $root.reviews.reviews, as: 'review' } -->
            <td data-bind="css: {
                      'reviews-list__row--highlighted': review.checked
                      },
                      click: function(_, e) {
                        $root.openDetailsModal(review);
                      }">
              <div class="reviews-list__cell-content">
                <!-- ko if: review.order.sum -->
                <!-- ko text: review.order.sum -->
                <!-- /ko -->&nbsp;<i class="far fa-ruble-sign statistics__details-modal-dialog-history-orders-table-order-list-table-price-ruble-sign"></i>
                <!-- /ko -->
              </div>
            </td>
            <!-- /ko -->



          </tr>
          <!-- /ko -->

          <!-- ko if: $root.table.columns.has('orderType') -->
          <tr>
            <th>
              <?= \Yii::t('main', 'Тип доставки') ?>
            </th>

            <!-- ko foreach: { data:  $root.reviews.reviews, as: 'review' } -->
            <td data-bind="css: {
                      'reviews-list__row--highlighted': review.checked
                      },
                      click: function(_, e) {

                        $root.openDetailsModal(review);
                      }">
              <div class="reviews-list__cell-content">
                <!-- ko text: $root.getOrderType(review.order.deliveryType) -->

                <!-- /ko -->
              </div>
            </td>
            <!-- /ko -->
          </tr>
          <!-- /ko -->

          <!-- ko if: $root.table.columns.has('sourceType') -->
          <tr>
            <th>
              <?= \Yii::t('main', 'Способ оформления') ?>
            </th>

            <!-- ko foreach: { data:  $root.reviews.reviews, as: 'review' } -->
            <td data-bind="css: {
                      'reviews-list__row--highlighted': review.checked
                      },
                      click: function(_, e) {
                        $root.openDetailsModal(review);
                      }">
              <div class="reviews-list__cell-content">
                <!-- ko if: review.order.sourceType -->
                <!-- ko text: $root.getSourceType(review.order.sourceType) -->
                <!-- /ko -->
                <!-- /ko -->
              </div>
            </td>
            <!-- /ko -->



          </tr>
          <!-- /ko -->

          <!-- ko if: $root.table.columns.has('orderAddress') -->
          <tr>
            <th>
              <?= \Yii::t('main', 'Адрес доставки') ?>
            </th>

            <!-- ko foreach: { data:  $root.reviews.reviews, as: 'review' } -->
            <td data-bind="css: {
                      'reviews-list__row--highlighted': review.checked
                      },
                      click: function(_, e) {
                        $root.openDetailsModal(review);
                      }">
              <div class="reviews-list__cell-content" data-bind="text: review.order.deliveryAddress">

              </div>
            </td>
            <!-- /ko -->
          </tr>
          <!-- /ko -->

          <!-- Виджет -->
          <!-- ko if: $root.table.columns.has('anketaWidget') -->
          <tr>
            <th>
              <?= \Yii::t('main', 'Виджет') ?>
            </th>

            <!-- ko foreach: { data:  $root.reviews.reviews, as: 'review' } -->
            <td data-bind="css: {
                      'reviews-list__row--highlighted': review.checked
                      },
                      click: function(_, e) {
                        $root.openDetailsModal(review);
                      }">

              <!-- ko if: review.anketaWidget -->
              <a 
              class="reviews-list__cell-content reviews-list__cell-content--link" 
              data-bind="
                text: review.anketaWidget, 
                attr: { href: '/foquz/foquz-poll/widgets?id=' + review.pollId,
                target: '_blank'},
                click: function(_, event) {
                  event.stopPropagation();
                  event.stopImmediatePropagation();
                  window.open('/foquz/foquz-poll/widgets?id=' + review.pollId, '_blank');
                }"
              >

              </a>
              <!-- /ko -->
            </td>
            <!-- /ko -->
          </tr>
          <!-- /ko -->
          <!-- /Виджет -->

          <!-- Сайт -->
          <!-- ko if: $root.table.columns.has('anketaSite') -->
          <tr>
            <th>
              <?= \Yii::t('main', 'Сайт') ?>
            </th>

            <!-- ko foreach: { data:  $root.reviews.reviews, as: 'review' } -->
            <td data-bind="css: {
                      'reviews-list__row--highlighted': review.checked
                      },
                      click: function(_, e) {
                        $root.openDetailsModal(review);
                      }">

              <!-- ko if: review.anketaSite -->
              <a 
              class="reviews-list__cell-content reviews-list__cell-content--link" 
              data-bind="
                text: review.anketaSite, 
                attr: { href: review.anketaWidgetProtocol + '://' + review.anketaSite },
                click: function(_, event) {
                  event.stopPropagation();
                  event.stopImmediatePropagation();
                  window.open(review.anketaWidgetProtocol + '://' + review.anketaSite, '_blank');
                }"
              >
              </a>
              <!-- /ko -->

            </td>
            <!-- /ko -->

          </tr>
          <!-- /ko -->
          <!-- /Сайт -->

          <!-- Источник -->
          <!-- ko if: $root.table.columns.has('anketaSource') -->
          <tr>
            <th>
              <?= \Yii::t('main', 'Источник') ?>
            </th>

            <!-- ko foreach: { data:  $root.reviews.reviews, as: 'review' } -->
            <td data-bind="css: {
                      'reviews-list__row--highlighted': review.checked
                      },
                      click: function(_, e) {
                        $root.openDetailsModal(review);
                      }">
              <!-- ko if: review.anketaSource -->
              <a 
              class="reviews-list__cell-content reviews-list__cell-content--link" 
              data-bind="
                text: review.anketaSource, 
                attr: { href: review.anketaWidgetProtocol + '://' + review.anketaSource },
                click: function(_, event) {
                  event.stopPropagation();
                  event.stopImmediatePropagation();
                  window.open(review.anketaWidgetProtocol + '://' + review.anketaSource, '_blank');
                }"
              >
              </a>
              <!-- /ko -->
            </td>
            <!-- /ko -->
          </tr>
          <!-- /ko -->
          <!-- /Источник -->

        </tbody>
      </table>

      <div id="reviews-view-right" class="reviews-view-right"></div>
    </div>
  </div>
</template>