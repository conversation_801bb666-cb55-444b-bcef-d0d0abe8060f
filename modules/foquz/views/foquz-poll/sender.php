<?php

use app\modules\foquz\assets\FoquzAsset;
use yii\helpers\Url;
use yii\helpers\Json;

$this->registerJs("
    var ACCESS_TOKEN = '" . Yii::$app->user->identity->access_token . "';
    var POLL_IS_AUTO = " . (int)$model->is_auto . ";
    var POLL_NAME = '" . addcslashes($model->name, "\0..\37'\\") . "';
    var POLL_ID = " . $model->id . ";
    var POLL = " . Json::encode($model) . ";
    var SMART_CAPTCHA_CLIENT_KEY = '" . \Yii::$app->params['smart_captcha_client_key'] .  "';
    var HAS_CONTACTS = " . (int)$hasContacts . ";
", $this::POS_HEAD);

$asset = FoquzAsset::register($this);


$this->registerCSSFile('/js/poll.sender.css', ['depends' => [app\modules\foquz\assets\FoquzAsset::className()]]);

$this->registerJSFile('/js/poll.sender.js', ['depends' => [app\modules\foquz\assets\FoquzAsset::className()]]);
$this->registerJSFile('https://captcha-api.yandex.ru/captcha.js');


$this->title = $model->name;
?>

<script src="https://yastatic.net/share2/share.js"></script>

<div class="sender-page initializing" data-bind="childrenComplete: onInit, css: { 'initializing': initializing() }">

    <?= $this->render('_poll_header', ['page' => 'sender', 'model' => $model]) ?>


    <?= $this->render('_menu_quests', ['page' => 'sender', 'model' => $model]) ?>

    <div class="f-card f-card--shadow">
        <div class="f-tabs">

            <nav class="nav f-tabs__nav">
                <a class="nav-item nav-link  f-tabs__nav-item active" href="<?= Url::to(['/foquz/foquz-poll/sender', 'id' => $model->id]) ?>" aria-selected="true">
                    Ссылка на опрос
                </a>
                <a class="nav-item nav-link f-tabs__nav-item" href="<?= Url::to(['/foquz/foquz-poll/channels', 'id' => $model->id]) ?>" aria-selected="false">
                    Каналы связи
                </a>

                <?php if ($model->is_auto) : ?>
                    <a class="nav-item nav-link f-tabs__nav-item" href="<?= Url::to(['/foquz/foquz-poll/mailing-conditions', 'id' => $model->id]) ?>" aria-selected="false">
                        Фильтры рассылки
                    </a>
                <?php else : ?>
                    <?php if (!(Yii::$app->user->identity->isFilialEmployee())) : ?>
                        <a class="nav-item nav-link f-tabs__nav-item" href="<?= Url::to(['/foquz/foquz-poll/mailings', 'id' => $model->id]) ?>" aria-selected="false">
                            Приглашения к опросу
                        </a>
                    <?php endif; ?>
                <?php endif; ?>
                <?php if (!$model->is_auto && (Yii::$app->user->identity->isAdmin() || Yii::$app->user->identity->isEditor())) : ?>
                    <a class="nav-item nav-link  f-tabs__nav-item" href="<?= Url::to(['/foquz/foquz-poll/widgets', 'id' => $model->id]) ?>" aria-selected="false">
                        Виджеты
                    </a>
                <?php endif; ?>
            </nav>

            <div class="f-tabs__content">

                <div class="f-tab tab-pane show active">

                    <?php if (!$model->is_auto) : ?>
                        <!-- ko let: {
                            hasFilials: ko.observable(false),
                        } -->
                        <section class="sender-page-section sender-link mb-25p">
                            <div class="content__inner">
                                <poll-link
                                    class="sender-page-section__content"
                                    params="
                                        poll: poll,
                                        hasFilials: hasFilials,
                                        onChange: function(fId) {
                                            filialId(fId);
                                        },
                                        blocked: $root.blocked,
                                    "
                                ></poll-link>
                            </div>
                        </section>
                        <!-- /ko -->
                    <?php endif; ?>

                    <?php if (YII_ENV_DEV) : ?>
                        <hr class="mt-0 mb-25p">
                        <section class="sender-page-section sender-send mb-25p">
                            <div class="content__inner">
                                <h2>Отправка опроса</h2>

                                <div class="sender-page-section__content">
                                    <div class="row">
                                        <div class="col-12 col-md-6">

                                            <div class="sender-page__to-email" data-bind="using: toEmailModel">
                                                <div class="form-group mb-3">
                                                    <label class="d-flex align-items-center form-label" for="send-to-email-field">
                                                        <span>Отправить на почту</span>

                                                        <button type="button" class="btn-question" data-toggle="tooltip" data-placement="top" title="" data-html="true" data-original-title="<?= $model->is_auto ? 'Для прохождения автоматического опроса генерируются уникальные ссылки для каждого клиента.<br>Можно указать почту и будет сгенерирована уникальная ссылка для указанного клиента.<br>Если для опроса подключен канал связи &quot;Email&quot;, то на почту будет отправлено письмо с уникальной ссылкой на опрос' : 'Можно указать через запятую email-почты и отправить письмо с анонимной ссылкой на опрос.<br>Письмо придёт на почту только в том случае, если для опроса подключен канал связи Email. Канал связи можно подключить в настройках опроса' ?>"></button>
                                                    </label>
                                                    <?php if ($model->is_auto) : ?>
                                                        <div class="input-group">
                                                            <input id="send-to-email-field" class="form-control" data-bind="textInput: fields().emails, css: {
                                                                'is-invalid': formControlErrorStateMatcher(fields().emails)
                                                            }">
                                                        </div>
                                                    <?php else : ?>
                                                        <textarea id="send-to-email-field" class="form-control " data-bind="autosizeTextarea, textInput: fields().emails, css: { 'is-invalid': formControlErrorStateMatcher(fields().emails)}, minHeight: 48" style="padding-top: 10px; padding-bottom: 10px; resize: none!important; line-height: 1.6"></textarea>
                                                        <!-- <div id="send-to-email-field" class="form-control sender-page__content-editable" data-bind="contentEditable: fields().emails,
                                                            css: { 'is-invalid': formControlErrorStateMatcher(fields().emails)}"></div> -->
                                                    <?php endif; ?>
                                                    <!-- ko template: {
                                                            foreach: templateIf(formControlErrorStateMatcher(fields().emails)(), $data),
                                                            afterAdd: fadeAfterAddFactory(200),
                                                            beforeRemove: fadeBeforeRemoveFactory(200),
                                                        } -->
                                                    <div class="form-error" data-bind="text: fields().emails.error()"></div>
                                                    <!-- /ko -->

                                                    <?php if (!$model->is_auto) : ?>
                                                        <div class="text-muted mt-2 sender-page__hint">
                                                            Напишите адреса через запятую
                                                        </div>
                                                    <?php endif; ?>
                                                </div>

                                                <!-- <div class="form-group">
                                                    <fc-captcha params="key: SMART_CAPTCHA_CLIENT_KEY, onSuccess: function(token) {
                                                        $root.checkCaptchaToken(token);
                                                    }, invalid: $root.captchaTokenIsInvalid()"></fc-captcha>

                                                    <fc-error params="show: $root.captchaError, text: $root.captchaError"></fc-error>
                                                </div> -->

                                                <div class="sender-page__actions">

                                                    <button class="btn btn-default" type="button" data-bind="click: function() { submit(); }, enable: $root.captchaTokenIsValid()">Отправить</button>

                                                </div>

                                                <!-- ko if: errors.length -->
                                                <div class="sender-page__feedback">
                                                    <!-- ko foreach: errors -->
                                                    <div class="text-danger d-flex mt-2">
                                                        <i class="fas fa-exclamation-circle"></i>
                                                        <span data-bind="html: $data" class="ml-2"></span>
                                                    </div>
                                                    <!-- /ko -->
                                                </div>
                                                <!-- /ko -->

                                                <!-- ko if: notification  -->
                                                <div class="sender-page__feedback text-success d-flex mt-2">
                                                    <i class="fas fa-check-circle"></i>
                                                    <span class="ml-2" data-bind="html: notification"></span>
                                                </div>
                                                <!-- /ko -->

                                                <!-- ko if: data() && data().links && data().links.length -->
                                                <?php if ($model->is_auto) : ?>
                                                    <div class="mt-2">
                                                        <!-- ko foreach: data().links -->
                                                        <div class="d-flex align-items-center sender-page__link">
                                                            <a data-bind="text: $data, attr: { href: $data }" target="_blank"></a>
                                                            <button type="button" class="btn btn-ghost p-0 ml-2" title="Скопировать в буфер" data-bind="click: function() { $root.copyLink($data) },tooltip, tooltipPlacement: 'top'"><i class="icon icon-copy-text"></i></button>
                                                        </div>
                                                        <!-- /ko -->
                                                    </div>
                                                <?php else : ?>
                                                    <div class="mt-2" data-bind="using: data().links[0]">

                                                        <div class="d-flex align-items-center sender-page__link">
                                                            <a data-bind="text: $data, attr: { href: $data }" target="_blank"></a>
                                                            <button type="button" class="btn btn-ghost p-0 ml-2" title="Скопировать в буфер" data-bind="click: function() { $root.copyLink($data) },tooltip, tooltipPlacement: 'top'"><i class="icon icon-copy-text"></i></button>
                                                        </div>

                                                    </div>
                                                <?php endif; ?>
                                                <!-- /ko -->
                                            </div><!-- .sender-page__to-email -->
                                        </div>

                                        <div class="col-12 col-md-6">
                                            <div class="sender-page__to-phone" data-bind="using: toPhoneModel">
                                                <div class="form-group mb-3">
                                                    <label class="d-flex align-items-center form-label" for="send-to-phone-field">
                                                        <span>Отправить на телефон</span>
                                                        <button type="button" class="btn-question" data-toggle="tooltip" data-placement="top" title="" data-html="true" data-original-title="Можно указать телефон и отправить сообщение с ананонимной ссылкой на опрос.<br>Сообщение будет отправлено, если для опроса подключен соответствующий канал связи: Viber, Telegram, SMS. Канал связи можно подключить в настройках опроса"></button>
                                                    </label>
                                                    <div class="input-group">
                                                        <input id="send-to-phone-field" class="form-control" maxlength="25" data-bind="fbMask: 'phone', textInput: fields().phone, css: {
                                                                'is-invalid': formControlErrorStateMatcher(fields().phone)
                                                            }">
                                                    </div>
                                                    <!-- ko template: {
                                                        foreach: templateIf(formControlErrorStateMatcher(fields().phone)(), $data),
                                                        afterAdd: fadeAfterAddFactory(200),
                                                        beforeRemove: fadeBeforeRemoveFactory(200),
                                                    } -->
                                                    <div class="form-error" data-bind="text: fields().phone.error()"></div>
                                                    <!-- /ko -->
                                                </div>

                                                <div class="sender-page__actions">
                                                    <button class="btn btn-default mr-2" type="button" data-bind="click: function() { submit('viber'); }">В Viber</button>
                                                    <button class="btn btn-default mr-2" type="button" data-bind="click: function() { submit('telegram'); }">В Telegram</button>
                                                    <button class="btn btn-default" type="button" data-bind="click: function() { submit('sms'); }">В SMS</button>
                                                </div>

                                                <!-- ko if: errors.length -->
                                                <div class="sender-page__feedback">
                                                    <!-- ko foreach: errors -->
                                                    <div class="text-danger d-flex mt-2">
                                                        <i class="fas fa-exclamation-circle"></i>
                                                        <span class="ml-2" data-bind="html: $data"></span>
                                                    </div>
                                                    <!-- /ko -->
                                                </div>
                                                <!-- /ko -->

                                                <!-- ko if: notification  -->
                                                <div class="sender-page__feedback mt-2 text-success d-flex">
                                                    <i class="fas fa-check-circle"></i>
                                                    <span class="ml-2" data-bind="html: notification"></span>
                                                </div>
                                                <!-- /ko -->
                                            </div><!-- .sender-page__to-phone -->
                                        </div>
                                    </div>

                                    <!-- ko template: {
                                        foreach: templateIf(toEmailModel.isSubmitting() || toPhoneModel.isSubmitting(), $data),
                                        afterAdd: fadeAfterAddFactory(200),
                                        beforeRemove: fadeBeforeRemoveFactory(200),
                                    } -->
                                    <div class="sender-page__loader">
                                        <i class="fa fa-spinner fa-pulse fa-2x fa-fw color-active"></i>
                                    </div>
                                    <!-- /ko -->
                                </div><!-- .sender-page-section__content -->
                            </div>
                        </section>
                    <?php endif; ?>

                    <?php if (YII_DEBUG && (!str_contains(Yii::$app->request->hostInfo, 'foquz.ru') === false || str_contains(Yii::$app->request->hostInfo, 'devfoquz.ru'))) : ?>
                        <hr class="mt-0 mb-25p">
                        <section class="sender-page-section sender-generation mb-25p">
                            <div class="content__inner">
                                <h2>Генерация анкет</h2>

                                <div class="sender-page-section__content" data-bind="using: reviewsModel">
                                    <div class="row">
                                        <div class="col col-12 col-md-4 ">

                                            <div class="sender-page__reviews-count">
                                                <div class="form-group mb-3">
                                                    <label class="d-flex align-items-center form-label" for="reviews-count-field">
                                                        <span>Количество</span>
                                                        <button type="button" class="btn-question" data-toggle="tooltip" data-placement="top" title="" data-original-title="Можно сгенерировать анкеты случайеым образом, чтобы посмотреть, как будет работать функционал на реальных данных. Для этого нужно указать количество и параметры для заполненности анкет в процентном соотношении"></button>
                                                    </label>
                                                    <div class="input-group">
                                                        <input id="reviews-count-field" class="form-control" data-bind="numericField, textInput: fields().count, css: {
                                                            'is-invalid': formControlErrorStateMatcher(fields().count)
                                                        }">
                                                    </div>
                                                    <!-- ko template: {
                                                        foreach: templateIf(formControlErrorStateMatcher(fields().count)(), $data),
                                                        afterAdd: fadeAfterAddFactory(200),
                                                        beforeRemove: fadeBeforeRemoveFactory(200),
                                                    } -->
                                                    <div class="form-error" data-bind="text: fields().count.error()"></div>
                                                    <!-- /ko -->
                                                </div>
                                            </div><!-- .sender-page__reviews-count -->
                                        </div>

                                        <div class="col col-12 col-md-4 ">
                                            <div class="sender-page__reviews-completed">
                                                <div class="form-group mb-3">
                                                    <label class="d-flex align-items-center form-label" for="reviews-completed-field">
                                                        <span>Заполненных целиком, %</span>
                                                        <button type="button" class="btn-question" data-toggle="tooltip" data-placement="top" title="" data-original-title="Можно сгенерировать анкеты случайеым образом, чтобы посмотреть, как будет работать функционал на реальных данных. Для этого нужно указать количество и параметры для заполненности анкет в процентном соотношении"></button>
                                                    </label>
                                                    <div class="input-group">
                                                        <input id="reviews-completed-field" class="form-control" data-bind="numericField, textInput: fields().full, css: {
                                                            'is-invalid': formControlErrorStateMatcher(fields().full)
                                                        }">
                                                    </div>
                                                    <!-- ko template: {
                                            foreach: templateIf(formControlErrorStateMatcher(fields().full)(), $data),
                                            afterAdd: fadeAfterAddFactory(200),
                                            beforeRemove: fadeBeforeRemoveFactory(200),
                                            } -->
                                                    <div class="form-error" data-bind="text: fields().full.error()"></div>
                                                    <!-- /ko -->
                                                </div>
                                            </div><!-- .sender-page__reviews-completed -->
                                        </div>

                                        <div class="col col-12 col-md-4 ">
                                            <div class="sender-page__reviews-incompleted">
                                                <div class="form-group mb-3">
                                                    <label class="d-flex align-items-center form-label" for="reviews-incompleted-field">
                                                        <span>Заполненных частично, %</span>
                                                        <button type="button" class="btn-question" data-toggle="tooltip" data-placement="top" title="" data-original-title="Можно сгенерировать анкеты случайеым образом, чтобы посмотреть, как будет работать функционал на реальных данных. Для этого нужно указать количество и параметры для заполненности анкет в процентном соотношении"></button>
                                                    </label>
                                                    <div class="input-group">
                                                        <input id="reviews-incompleted-field" class="form-control" data-bind="numericField, textInput: fields().part, css: {
                                                            'is-invalid': formControlErrorStateMatcher(fields().part)
                                                        }">
                                                    </div>
                                                    <!-- ko template: {
                                            foreach: templateIf(formControlErrorStateMatcher(fields().part)(), $data),
                                            afterAdd: fadeAfterAddFactory(200),
                                            beforeRemove: fadeBeforeRemoveFactory(200),
                                            } -->
                                                    <div class="form-error" data-bind="text: fields().part.error()"></div>
                                                    <!-- /ko -->
                                                </div>
                                            </div><!-- .sender-page__reviews-incompleted -->
                                        </div>
                                    </div>

                                    <div class="sender-page__actions d-flex align-items-center">
                                        <button class="btn btn-success mr-5 flex-shrink-0" type="button" data-bind="click: function() { submit(); },
                                            enable: fields().count">Сгенерировать случайные ответы</button>

                                        <!-- ko if: errors.length -->
                                        <div class="sender-page__feedback">
                                            <!-- ko foreach: errors -->
                                            <div class="text-danger d-flex">
                                                <i class="fas fa-exclamation-circle"></i>
                                                <span class="ml-2" data-bind="html: $data"></span>
                                            </div>
                                            <!-- /ko -->
                                        </div>
                                        <!-- /ko -->

                                        <!-- ko if: data() && data().message  -->
                                        <div class="sender-page__feedback text-success d-flex">
                                            <i class="fas fa-check-circle"></i>
                                            <span class="ml-2" data-bind="html: data().message"></span>
                                        </div>
                                        <!-- /ko -->
                                    </div>

                                    <!-- ko template: {
                                        foreach: templateIf(isSubmitting(), $data),
                                        afterAdd: fadeAfterAddFactory(200),
                                        beforeRemove: fadeBeforeRemoveFactory(200),
                                    } -->
                                    <div class="sender-page__loader">
                                        <i class="fa fa-spinner fa-pulse fa-2x fa-fw color-active"></i>
                                    </div>
                                    <!-- /ko -->

                                </div><!-- .sender-page-section__content -->
                            </div>
                        </section>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
