<?php

/** @var $this View */

/** @var $model FoquzPoll */
/** @var $hasWidgets bool */

use app\modules\foquz\assets\FoquzAsset;
use app\modules\foquz\models\FoquzPoll;
use yii\web\View;
use yii\helpers\Json;
use yii\helpers\Url;

$this->registerJs("
    var ACCESS_TOKEN = '" . Yii::$app->user->identity->access_token . "';
    var POLL_IS_AUTO = " . (int)$model->is_auto . ";
    var POLL_NAME = '" . addcslashes($model->name, "\0..\37'\\") . "';
    var POLL_ID = " . $model->id . ";
    var POLL = " . Json::encode($model) . ";
    var SMART_CAPTCHA_CLIENT_KEY = '" . \Yii::$app->params['smart_captcha_client_key'] .  "';
", $this::POS_HEAD);

$asset = FoquzAsset::register($this);


$this->registerCSSFile('/js/poll.sender.css', ['depends' => [app\modules\foquz\assets\FoquzAsset::className()]]);

$this->registerJSFile('/js/poll.sender.js', ['depends' => [app\modules\foquz\assets\FoquzAsset::className()]]);
$this->registerJSFile('https://captcha-api.yandex.ru/captcha.js');

$this->registerJs(
    "
    var POLL = " . Json::encode($model) . ";
    var HAS_WIDGETS = " . (int)$hasWidgets . ";
    ",
    View::POS_HEAD
);

$this->title = $model->name;
?>

<div class="sender-page">

    <?= $this->render('_poll_header', ['page' => 'sender', 'model' => $model]) ?>


    <?= $this->render('_menu_quests', ['page' => 'sender', 'model' => $model]) ?>

    <div class="f-card f-card--shadow">
        <div class="f-tabs">

            <nav class="nav f-tabs__nav">
                <a class="nav-item nav-link  f-tabs__nav-item" href="<?= Url::to(['/foquz/foquz-poll/sender', 'id' => $model->id]) ?>" aria-selected="false">
                    Ссылка на опрос
                </a>
                <a class="nav-item nav-link f-tabs__nav-item" href="<?= Url::to(['/foquz/foquz-poll/channels', 'id' => $model->id]) ?>" aria-selected="false">
                    Каналы связи
                </a>

                <?php if ($model->is_auto) : ?>
                    <a class="nav-item nav-link f-tabs__nav-item" href="<?= Url::to(['/foquz/foquz-poll/mailing-conditions', 'id' => $model->id]) ?>" aria-selected="false">
                        Фильтры рассылки
                    </a>
                <?php else : ?>
                    <?php if (!(Yii::$app->user->identity->isFilialEmployee())) : ?>
                        <a class="nav-item nav-link f-tabs__nav-item" href="<?= Url::to(['/foquz/foquz-poll/mailings', 'id' => $model->id]) ?>" aria-selected="false">
                            Приглашения к опросу
                        </a>
                    <?php endif; ?>
                <?php endif; ?>
                <?php if (!$model->is_auto && (Yii::$app->user->identity->isAdmin() || Yii::$app->user->identity->isEditor())) : ?>
                    <a class="nav-item nav-link  f-tabs__nav-item active" href="<?= Url::to(['/foquz/foquz-poll/widgets', 'id' => $model->id]) ?>" aria-selected="true">
                        Виджеты
                    </a>
                <?php endif; ?>
            </nav>

            <div class="f-tabs__content">

                <div class="f-tab tab-pane show active">

                        <!-- ko let: {
                            hasFilials: ko.observable(false),
                        } -->
                        <section class="sender-page-section sender-link">
                            <div class="content__inner">
                                
                                <poll-widgets
                                    class="sender-page-section__content"
                                    params="
                                        poll: poll,
                                        hasFilials: hasFilials,
                                        onChange: function(fId) {
                                            filialId(fId);
                                        },
                                        blocked: $root.blocked,
                                    "
                                ></poll-widgets>
                            </div>
                        </section>
                        <!-- /ko -->
                </div>
            </div>
        </div>
    </div>
</div>
