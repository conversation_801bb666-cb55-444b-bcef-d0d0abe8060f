<?php

use yii\helpers\Url;


$companyId = $poll->company_id;

$this->registerJs("
        var QUESTIONS = " . \yii\helpers\Json::encode($questions) . ";
        var POLL = " . \yii\helpers\Json::encode($poll) . ";
        var POLL_NAME = '" . $poll->name . "';
        var POLL_ID = " . $poll->id . ";
        var POLL_IS_AUTO = " . $poll->is_auto . ";
        var POLL_TRIGGER = " . ($poll->trigger ?? 0) . ";
        var FIRST_ANSWER =  '" . $firstAnswer . "';
        var LAST_ANSWER = '" . $lastAnswer . "';
        var AVG_TIME_PER_ANSWER = '" . $avgTime . "';
        var STATS_IS_EMPTY = " . ($processed > 0 ? 0 : 1) . ";
        var IS_EXTERNAL = true;
        var SHOW_ANSWERS = '" . $poll->statsLink->show_answers . "';

    ", $this::POS_HEAD);


$this->registerCSSFile('/js/poll.stats.external.css', ['depends' => [app\modules\foquz\assets\FoquzAsset::className()]]);

$this->registerJSFile('/js/poll.stats.external.js', ['depends' => [app\modules\foquz\assets\FoquzAsset::className()]]);
$asset = \app\modules\foquz\assets\FoquzExternalAsset::register($this);

$this->title = $poll->name;

$this->registerMetaTag([
    'name' => 'description',
    'content' => $poll->goal_text,
]);

$showAnswers = $poll->statsLink->show_answers ? 'show-answers_show' : 'show-answers_hide';
?>

<div class="question-statistics__content question-statistics__content--initializing" data-bind="descendantsComplete: onInit, css: { 'question-statistics__content--initializing': initializing() }">

    <header class="s-app__header">
        <div class="container-fluid s-app__container">
            <div class="d-flex justify-content-between align-items-center">
                <a class="s-app__logo mr-50p" href="<?= Url::to(['/']) ?>">
                    <img src="<?= Url::to(['/img/logo-ru.svg']) ?>" alt="" width="94">
                    <?php /* <img src="<?= Url::to(['/img/logo/logo-ny.svg']) ?>" alt="" width="94">  */ ?>
                </a>

                <div class="d-md-flex justify-content-between flex-grow-1 align-items-center d-none">

                    <h1 class="f-h1 pb-0 mb-0"><?= $poll->name ?></h1>
                    <div class="no-print">
                        <button
                            class="f-btn"
                            data-bind="
                                click: function() { $root.printPage() },
                                attr: { disabled: $data.deleted() == 1 },
                            "
                        >
                            <foquz-icon params="icon: 'print'" class="f-btn-prepend"></foquz-icon>
                            Распечатать
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <div>
        <div class="container-fluid s-app__container">
            <div class="f-card f-card--lg f-card--min-height f-card--shadow question-statistics__card <?= $showAnswers?>">
                <div class="f-tabs">
                    <!-- ko if: window.POLL.statsLink.show_answers && !deleted() -->
                    <nav class="nav f-tabs__nav no-print">
                        <a class="nav-item nav-link f-tabs__nav-item active" href="#" aria-selected="true">
                            Статистика
                        </a>
                        <a
                            class="nav-item nav-link f-tabs__nav-item"
                            aria-selected="false"
                            data-bind="
                                attr: {
                                    href: `/answers/${statsLinkId}?id=${poll.id}`,
                                },
                            "
                        >
                            Ответы
                        </a>
                    </nav>
                    <!-- /ko -->
                    <!-- ko let: { opened: ko.observable(true) } -->


                    <!-- Фильтры (md, lg) - не для печати -->
                    <!-- ko ifnot: deleted -->
                    <div class="d-none d-md-flex f-card__section f-card__divider py-10p no-print" data-bind="let: { opened: ko.observable(true) }">
                        <div class="d-flex flex-wrap w-100">
                            <div class="form-group dense-form-group mr-30p py-5p">
                                <label class="form-label">Период</label>
                                <period-picker params="value: periodFilter, autosize: true, allowClear: true" data-bind="css: {
                                        'is-invalid':(!periodErrorStateMatcher() && isSubmittedFilters()) || havePeriodError() && isSubmittedFilters(),
                                        'is-valid':!havePeriodError() && isSubmittedFilters()
                                    }"></period-picker>
                                <!-- ko if: (!periodErrorStateMatcher(periodFilter) && isSubmittedFilters()) || havePeriodError() && isSubmittedFilters() -->
                                <div class="form-error" data-bind="text: getPeriodError()"></div>
                                <!-- /ko -->
                            </div>

                            <div class="form-group dense-form-group mr-30p py-5p">
                                <label class="form-label"><?= \Yii::t('main', 'Статус анкеты') ?></label>

                                <select multiple data-bind="
                                    selectedOptions: questionnaires,
                                    valueAllowUnset: true,
                                    allowUnset: true,
                                    lazySelect2: {
                                        wrapperCssClass: 'select2-container--form-control',
                                        dropdownCssClass: 'dense-form-group__dropdown',
                                        containerCss: { 'min-width': '90px' },
                                    }
                                " data-placeholder="<?= \Yii::t('main', 'Все анкеты') ?>">
                                    <option></option>
                                    <option value="done"><?= \Yii::t('answers', 'Заполнена') ?></option>
                                    <option value="in-progress"><?= \Yii::t('answers', 'В процессе') ?></option>
                                    <option value="quote-full"><?= \Yii::t('answers', 'Квотафул') ?></option>
                                    <option value="screen-out"><?= \Yii::t('answers', 'Скринаут') ?></option>
                                </select>
                            </div>

                            <tags-filter params="tags: tags,
                                    operation: tagsOperation, formGroupClass: 'py-5p', companyId: '<?= $companyId ?>'" class="mr-30p"></tags-filter>

                            <div class="form-group dense-form-group mr-30p py-5p">
                                <label class="form-label">Устройство</label>
                                <select data-bind="
                                    selectedOptions: devices,
                                    valueAllowUnset: true,
                                    select2: {
                                        wrapperCssClass: 'select2-container--form-control foquz-select2',
                                            dropdownCssClass: 'dense-form-group__dropdown',
                                            containerCss: { 'min-width': '80px' },
                                    }" data-placeholder="Все" multiple>

                                    <option value="desktop">Десктоп</option>
                                    <option value="tablet">Планшет</option>
                                    <option value="mobile">Смартфон</option>
                                </select>
                            </div>

                            <!-- ko if: hasFilials -->
                            <div class="form-group dense-form-group mr-30p py-5p">
                                <label class="form-label">Филиал</label>

                                <div>
                                    <fc-select class="categorized" params="inline: true,
                                        options: filialsList,
                                        value: filials,
                                        placeholder: 'Все филиалы',
                                        multiple: true,
                                        blockSelectedGroup: true"></fc-select>
                                </div>
                            </div>
                            <!-- /ko -->

                            <!-- ko if: hasLinksWithQuotas -->
                            <div class="form-group dense-form-group mr-30p py-5p">
                                <label class="form-label">Ссылки</label>

                                <div>
                                    <fc-select class="categorized" params="inline: true, 
                                        options: linksList, 
                                        value: links, 
                                        placeholder: 'Все ссылки', 
                                        multiple: true, 
                                        blockSelectedGroup: true"></fc-select>
                                </div>
                            </div>
                            <!-- /ko -->

                            <div class="ml-auto no-print py-5p pl-30p">
                                <button class="f-btn f-btn-link" type="button" data-bind="
                                    click: function() {
                                        resetFilters();
                                    }">Сбросить</button>
                                <button class="f-btn f-btn-success" type="button" data-bind="
                                    disable: !periodErrorStateMatcher() || isAjaxSended() || (havePeriodError() && isSubmittedFilters),
                                    css: {
                                        'f-btn--loading': isAjaxSended()
                                    },
                                    click:  function(_,event) {
                                        event.preventDefault();
                                        submitFilters();
                                    }">
                                    <foquz-icon class="f-btn-prepend" params="icon: 'check'"></foquz-icon>
                                    Применить
                                </button>
                            </div>
                        </div>
                    </div>
                    <!-- /ko -->
                    <!-- /Фильтры (md, lg) - не для печати -->

                    <!-- Сохраненные фильтры (для печати) -->
                    <!-- ko ifnot: deleted -->
                    <!-- ko if: hasSavedFilters -->
                    <div class="f-card__divider only-print" style="display: none">
                        <div class="mx-20p">
                            <stats-filters-value class="py-15p" params="filters: $root.getSavedSearchParams()"></stats-filters-value>
                        </div>
                    </div>
                    <!-- /ko -->
                    <!-- /ko -->
                    <!-- /Сохраненные фильтры (для печати) -->

                    <!-- Статистика (md, lg) -->
                    <div class="f-card__divider d-none d-md-block">
                        <div class="d-md-flex">
                            <foquz-stats-item class="adaptive">
                                <div class="value f-color-primary">
                                    <span data-bind="text: $parent.counts.sended()"></span>

                                    <small>
                                        <!-- ko if: $parent.hasDeleted -->/0
                                        <!-- /ko -->
                                        <!-- ko ifnot: $parent.hasDeleted -->
                                        /<?= $sended ?>
                                        <!--/ko-->
                                    </small>
                                </div>
                                <div class="label">Отправлено</div>
                            </foquz-stats-item>
                            <foquz-stats-item class="adaptive">
                                <div class="value f-color-violet">
                                    <span data-bind="text: $parent.counts.opened()"></span>

                                    <small>
                                        <!-- ko if: $parent.hasDeleted -->/0
                                        <!-- /ko -->
                                        <!-- ko ifnot: $parent.hasDeleted -->
                                        /<?= $opened ?>
                                        <!--/ko-->
                                    </small>
                                </div>
                                <div class="label">Открыто</div>
                            </foquz-stats-item>
                            <foquz-stats-item class="adaptive">
                                <div class="value f-color-blue">
                                    <span data-bind="text: $parent.counts.processed()"></span>

                                    <small>
                                        <!-- ko if: $parent.hasDeleted -->/0
                                        <!-- /ko -->
                                        <!-- ko ifnot: $parent.hasDeleted -->
                                        /<?= $processed ?>
                                        <!--/ko-->
                                    </small>
                                </div>
                                <div class="label">В процессе</div>
                            </foquz-stats-item>
                            <foquz-stats-item class="adaptive">
                                <div class="value f-color-mint">
                                    <span data-bind="text: $parent.counts.done()"></span>

                                    <small>
                                        <!-- ko if: $parent.hasDeleted -->/0
                                        <!-- /ko -->
                                        <!-- ko ifnot: $parent.hasDeleted -->
                                        /<?= $done ?>
                                        <!--/ko-->
                                    </small>
                                </div>
                                <div class="label">Заполнено</div>
                            </foquz-stats-item>

                            <?php if ($goal) : ?>
                                <!-- ko ifnot: deleted -->
                                <foquz-stats-item class="d-none d-md-block adaptive">
                                    <foquz-icon params="icon: 'target'"></foquz-icon>
                                    <div class="label">Цель достигнута</div>
                                </foquz-stats-item>
                                <!-- /ko -->
                            <?php endif; ?>

                            <!-- ko if: firstAnswerDate || lastAnswerDate || averageTime  -->
                            <div class="question-statistics__time f-color-service d-none d-md-flex" data-bind="css: {
                                    'ml-auto': deleted
                                    }">
                                <!-- ko if: !deleted() && (firstAnswerDate || lastAnswerDate) -->
                                <div class="question-statistics__time-block">
                                    <div class="question-statistics__time-icon">
                                        <span class="f-icon f-icon-danger f-icon-calendar">
                                            <svg id="calendar-icon" width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M14 1V5" stroke="red" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                                <path d="M6 1V5" stroke="red" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                                <path d="M1 8H19" stroke="red" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                                <path d="M17 3H3C1.895 3 1 3.895 1 5V18C1 19.105 1.895 20 3 20H17C18.105 20 19 19.105 19 18V5C19 3.895 18.105 3 17 3Z" stroke="red" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                                <path d="M4.99601 11C4.44402 11 3.99603 11.448 4.00003 12C4.00003 12.552 4.44802 13 5.00001 13C5.55201 13 6 12.552 6 12C6 11.448 5.55201 11 4.99601 11Z" fill="red"></path>
                                                <path d="M9.99601 11C9.44402 11 8.99603 11.448 9.00003 12C9.00003 12.552 9.44802 13 10 13C10.552 13 11 12.552 11 12C11 11.448 10.552 11 9.99601 11Z" fill="red"></path>
                                                <path d="M14.996 11C14.444 11 13.996 11.448 14 12C14 12.552 14.448 13 15 13C15.552 13 16 12.552 16 12C16 11.448 15.552 11 14.996 11Z" fill="red"></path>
                                                <path d="M4.99601 15C4.44402 15 3.99603 15.448 4.00003 16C4.00003 16.552 4.44802 17 5.00001 17C5.55201 17 6 16.552 6 16C6 15.448 5.55201 15 4.99601 15Z" fill="red"></path>
                                                <path d="M9.99601 15C9.44402 15 8.99603 15.448 9.00003 16C9.00003 16.552 9.44802 17 10 17C10.552 17 11 16.552 11 16C11 15.448 10.552 15 9.99601 15Z" fill="red"></path>
                                            </svg>
                                        </span>
                                    </div>
                                    <div class="f-fs-1">
                                        <div class="cursor-pointer" data-bind="if: firstAnswerDate, tooltip" title="Дата и время получения первого ответа">
                                            <span class="bold" data-bind="text: firstAnswerDate"></span>
                                            <span data-bind="text: firstAnswerTime"></span>
                                        </div>
                                        <div class="cursor-pointer" data-bind="if: lastAnswerDate, tooltip" title="Дата и время получения последнего ответа">
                                            <span class="bold" data-bind="text: lastAnswerDate"></span>
                                            <span data-bind="text: lastAnswerTime"></span>
                                        </div>
                                    </div>

                                </div>
                                <!-- /ko -->

                                <!-- ko if: !deleted() && averageTime -->
                                <div class="question-statistics__time-block cursor-pointer">
                                    <div class="d-flex align-items-center" title="Среднее время заполнения анкеты" data-bind="tooltip">
                                        <div class="question-statistics__time-icon">
                                            <span class="f-icon f-icon-danger f-icon-time">
                                                <svg width="24" height="24" xmlns="http://www.w3.org/2000/svg">
                                                    <use href="#time-icon"></use>
                                                </svg>
                                            </span>
                                        </div>
                                        <div data-bind="text: averageTime" class="questions-statistics__average-time f-fs-2 bold"></div>
                                    </div>
                                </div>
                                <!-- /ko -->
                            </div>
                            <!-- /ko -->
                        </div>
                    </div>
                    <!-- /Статистика -->

                    <!-- Баллы (md, lg) - не для печати -->
                    <!-- ko ifnot: deleted -->
                    <!-- ko if: pointsModel.hasPoints -->
                    <div class="f-card__divider no-print d-none d-md-block">
                        <div class="d-md-flex" data-bind="using: pointsModel">
                            <foquz-stats-item class="adaptive">
                                <div class="value" data-bind="text: $parent.maxPoints"></div>
                                <div class="label">Max кол-во баллов</div>
                            </foquz-stats-item>
                            <foquz-stats-item class="adaptive">
                                <div class="value">
                                    <span data-bind="text: $parent.avgPoints"></span>
                                    /
                                    <span data-bind="text: window.POLL.avg_percent + '%'"></span>
                                </div>
                                <div class="label">Среднее кол-во баллов</div>
                            </foquz-stats-item>
                            <div class="d-none d-md-flex ml-auto pl-4 pr-20p align-items-center">
                                <a href="javascript:void(0)" class="no-print f-fs-1-5 font-weight-500" data-bind="click: function() { $root.openPointsModal() }">Распределение баллов</a>

                            </div>
                        </div>
                    </div>
                    <!-- /ko -->
                    <!-- /ko -->
                    <!-- /Баллы -->


                    <!-- Сворачивающаяся секция (mobile) -->
                    <!-- ko template: {
                        foreach: templateIf(opened(), $data),
                        afterAdd: slideAfterAddFactory(400),
                        beforeRemove: slideBeforeRemoveFactory(400)
                    } -->
                    <div>
                        <!-- Статистика (mobile) -->
                        <div class="f-card__divider d-md-none">
                            <div class="d-md-flex">
                                <foquz-stats-item class="adaptive">
                                    <div class="value f-color-primary">
                                        <span data-bind="text: $parent.deleted() ? '0' : $parent.counts.sended()"></span>

                                        <small>
                                            <!-- ko if: $parent.deleted -->/0
                                            <!-- /ko -->
                                            <!-- ko ifnot: $parent.deleted -->
                                            /<?= $sended ?>
                                            <!--/ko-->
                                        </small>
                                    </div>
                                    <div class="label">Отправлено</div>
                                </foquz-stats-item>
                                <foquz-stats-item class="adaptive">
                                    <div class="value f-color-violet">
                                        <span data-bind="text: $parent.deleted() ? '0' : $parent.counts.opened()"></span>

                                        <small>
                                            <!-- ko if: $parent.deleted -->/0
                                            <!-- /ko -->
                                            <!-- ko ifnot: $parent.deleted -->
                                            /<?= $opened ?>
                                            <!--/ko-->
                                        </small>
                                    </div>
                                    <div class="label">Открыто</div>
                                </foquz-stats-item>
                                <foquz-stats-item class="adaptive">
                                    <div class="value f-color-blue">
                                        <span data-bind="text: $parent.deleted() ? '0' : $parent.counts.processed()"></span>

                                        <small>
                                            <!-- ko if: $parent.deleted -->/0
                                            <!-- /ko -->
                                            <!-- ko ifnot: $parent.deleted -->
                                            /<?= $processed ?>
                                            <!--/ko-->
                                        </small>
                                    </div>
                                    <div class="label">В процессе</div>
                                </foquz-stats-item>
                                <foquz-stats-item class="adaptive">
                                    <div class="value f-color-mint">
                                        <span data-bind="text: $parent.deleted() ? '0' : $parent.counts.done()"></span>

                                        <small>
                                            <!-- ko if: $parent.deleted -->/0
                                            <!-- /ko -->
                                            <!-- ko ifnot: $parent.deleted -->
                                            /<?= $done ?>
                                            <!--/ko-->
                                        </small>
                                    </div>
                                    <div class="label">Заполнено</div>
                                </foquz-stats-item>

                                <?php if ($goal) : ?>
                                    <!-- ko ifnot: deleted -->
                                    <foquz-stats-item class="d-none d-md-block adaptive">
                                        <foquz-icon params="icon: 'target'"></foquz-icon>
                                        <div class="label">Цель достигнута</div>
                                    </foquz-stats-item>
                                    <!-- /ko -->
                                <?php endif; ?>

                                <!-- ko if: firstAnswerDate || lastAnswerDate || averageTime  -->
                                <div class="question-statistics__time f-color-service d-none d-md-flex" data-bind="css: {
                                    'ml-auto': deleted
                                    }">
                                    <!-- ko if: !deleted() && (firstAnswerDate || lastAnswerDate) -->
                                    <div class="question-statistics__time-block">
                                        <div class="question-statistics__time-icon">
                                            <span class="f-icon f-icon-danger f-icon-calendar">
                                                <svg id="calendar-icon" width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M14 1V5" stroke="red" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                                    <path d="M6 1V5" stroke="red" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                                    <path d="M1 8H19" stroke="red" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                                    <path d="M17 3H3C1.895 3 1 3.895 1 5V18C1 19.105 1.895 20 3 20H17C18.105 20 19 19.105 19 18V5C19 3.895 18.105 3 17 3Z" stroke="red" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                                    <path d="M4.99601 11C4.44402 11 3.99603 11.448 4.00003 12C4.00003 12.552 4.44802 13 5.00001 13C5.55201 13 6 12.552 6 12C6 11.448 5.55201 11 4.99601 11Z" fill="red"></path>
                                                    <path d="M9.99601 11C9.44402 11 8.99603 11.448 9.00003 12C9.00003 12.552 9.44802 13 10 13C10.552 13 11 12.552 11 12C11 11.448 10.552 11 9.99601 11Z" fill="red"></path>
                                                    <path d="M14.996 11C14.444 11 13.996 11.448 14 12C14 12.552 14.448 13 15 13C15.552 13 16 12.552 16 12C16 11.448 15.552 11 14.996 11Z" fill="red"></path>
                                                    <path d="M4.99601 15C4.44402 15 3.99603 15.448 4.00003 16C4.00003 16.552 4.44802 17 5.00001 17C5.55201 17 6 16.552 6 16C6 15.448 5.55201 15 4.99601 15Z" fill="red"></path>
                                                    <path d="M9.99601 15C9.44402 15 8.99603 15.448 9.00003 16C9.00003 16.552 9.44802 17 10 17C10.552 17 11 16.552 11 16C11 15.448 10.552 15 9.99601 15Z" fill="red"></path>
                                                </svg>
                                            </span>
                                        </div>
                                        <div class="f-fs-1">
                                            <div class="cursor-pointer" data-bind="if: firstAnswerDate, tooltip" title="Дата и время получения первого ответа">
                                                <span class="bold" data-bind="text: firstAnswerDate"></span>
                                                <span data-bind="text: firstAnswerTime"></span>
                                            </div>
                                            <div class="cursor-pointer" data-bind="if: lastAnswerDate, tooltip" title="Дата и время получения последнего ответа">
                                                <span class="bold" data-bind="text: lastAnswerDate"></span>
                                                <span data-bind="text: lastAnswerTime"></span>
                                            </div>
                                        </div>

                                    </div>
                                    <!-- /ko -->

                                    <!-- ko if: !deleted() && averageTime -->
                                    <div class="question-statistics__time-block cursor-pointer">
                                        <div class="d-flex align-items-center" title="Среднее время заполнения анкеты" data-bind="tooltip">
                                            <div class="question-statistics__time-icon">
                                                <span class="f-icon f-icon-danger f-icon-time">
                                                    <svg width="24" height="24" xmlns="http://www.w3.org/2000/svg">
                                                        <use href="#time-icon"></use>
                                                    </svg>
                                                </span>
                                            </div>
                                            <div data-bind="text: averageTime" class="questions-statistics__average-time f-fs-2 bold"></div>
                                        </div>
                                    </div>
                                    <!-- /ko -->
                                </div>
                                <!-- /ko -->
                            </div>
                        </div>
                        <!-- /Статистика -->

                        <!-- Баллы (mobile) не для печати  -->
                        <!-- ko ifnot: deleted -->
                        <!-- ko if: pointsModel.hasPoints -->
                        <div class="f-card__divider no-print d-md-none">
                            <div class="d-md-flex" data-bind="using: pointsModel">
                                <foquz-stats-item class="adaptive">
                                    <div class="value" data-bind="text: $parent.maxPoints"></div>
                                    <div class="label">Max кол-во баллов</div>
                                </foquz-stats-item>
                                <foquz-stats-item class="adaptive">
                                    <div class="value">
                                        <span data-bind="text: $parent.avgPoints"></span>
                                        /
                                        <span data-bind="text: window.POLL.avg_percent + '%'"></span>
                                    </div>
                                    <div class="label">Среднее кол-во баллов</div>
                                </foquz-stats-item>
                                <div class="d-none d-md-flex ml-auto pl-4 pr-20p align-items-center">
                                    <a href="javascript:void(0)" class="no-print f-fs-1-5 font-weight-500" data-bind="click: function() { $root.openPointsModal() }">Распределение баллов</a>

                                </div>
                            </div>
                        </div>
                        <!-- /ko -->
                        <!-- /ko -->
                        <!-- /Баллы -->
                    </div>
                    <!-- /ko -->
                    <div class="d-flex align-items-center  d-md-none no-print border-bottom" style="height: 45px;">
                        <div class=" flex-grow-1 px-15p">
                            <!-- ko ifnot: deleted -->
                            <!-- ko if: pointsModel.hasPoints -->
                            <a href="javascript:void(0)" class=" f-fs-1-5 font-weight-500" data-bind="click: function() { $root.openPointsModal() }">Распределение баллов</a>
                            <!-- /ko -->
                            <!-- /ko -->
                        </div>


                        <div class="d-flex align-items-center border-left px-15p" style="height: 45px;">
                            <button class="f-icon-button " data-bind="click: function() {
                                opened(!opened());
                            }, css: {
                                'f-color-primary': opened,
                                'f-color-service': !opened(),
                            }">
                                <svg width="17" height="20" viewBox="0 0 17 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M16 1V19M6 5L6 19M11 9V19M1 11L1 19" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                                </svg>
                            </button>

                        </div>
                    </div>
                    <!-- /ko -->
                    <!-- /Сворачивающаяся секция (mobile) -->

                    <!-- Фильтры (mobile) -->
                    <!-- ko ifnot: deleted -->
                    <div class="d-md-none f-card__section f-card__divider py-10p no-print" data-bind="let: { opened: ko.observable(true) }">


                        <!-- ko template: {
                            foreach: templateIf(opened(), $data),
                            afterAdd: slideAfterAddFactory(400),
                            beforeRemove: slideBeforeRemoveFactory(400)
                        } -->
                        <div>
                            <div class="form-group dense-form-group mb-3 ">
                                <label class="form-label">Период</label>
                                <period-picker params="value: periodFilter, autosize: true, allowClear: true" data-bind="css: {
                                    'is-invalid':(!periodErrorStateMatcher() && isSubmittedFilters()) || havePeriodError() && isSubmittedFilters(),
                                    'is-valid':!havePeriodError() && isSubmittedFilters()
                                }"></period-picker>
                                <!-- ko if: (!periodErrorStateMatcher(periodFilter) && isSubmittedFilters()) || havePeriodError() && isSubmittedFilters() -->
                                <div class="form-error" data-bind="text: getPeriodError()"></div>
                                <!-- /ko -->
                            </div>

                            <div class="form-group dense-form-group mb-3">
                                <label class="form-label"><?= \Yii::t('main', 'Статус анкеты') ?></label>

                                <select multiple data-bind="
                                    selectedOptions: questionnaires,
                                    valueAllowUnset: true,
                                    lazySelect2: {
                                        wrapperCssClass: 'select2-container--form-control',
                                        dropdownCssClass: 'dense-form-group__dropdown',
                                        containerCss: { 'min-width': '80px' },
                                    }
                                    " data-placeholder="<?= \Yii::t('main', 'Все анкеты') ?>">
                                    <option value="done"><?= \Yii::t('answers', 'Заполнена') ?></option>
                                    <option value="in-progress"><?= \Yii::t('answers', 'В процессе') ?></option>
                                    <option value="quote-full"><?= \Yii::t('answers', 'Квотафул') ?></option>
                                    <option value="screen-out"><?= \Yii::t('answers', 'Скринаут') ?></option>
                                </select>
                            </div>

                            <tags-filter params="tags: tags,
                            operation: tagsOperation, formGroupClass: 'py-5p', companyId: '<?= $companyId ?>'" class="mb-3"></tags-filter>

                            <div class="form-group dense-form-group mb-3">
                                <label class="form-label">Устройство</label>
                                <select data-bind="
                                selectedOptions: devices,

                                valueAllowUnset: true,
                                    select2: {
                                        wrapperCssClass: 'select2-container--form-control foquz-select2',
                                            dropdownCssClass: 'dense-form-group__dropdown',
                                            containerCss: { 'min-width': '80px' },
                                    }" data-placeholder="Все" multiple>

                                    <option value="desktop">Десктоп</option>
                                    <option value="tablet">Планшет</option>
                                    <option value="mobile">Смартфон</option>
                                </select>
                            </div>

                            <!-- ko if: hasFilials -->
                            <div class="form-group dense-form-group mb-3">
                                <label class="form-label">Филиал</label>
                                <div>
                                    <fc-select class="categorized" params="inline: true,
                                        options: filialsList,
                                        value: filials,
                                        placeholder: 'Все филиалы',
                                        multiple: true,
                                        blockSelectedGroup: true"></fc-select>
                                </div>
                            </div>
                            <!-- /ko -->

                            <!-- ko if: hasLinksWithQuotas -->
                            <div class="form-group dense-form-group">
                                <label class="form-label">Ссылки</label>

                                <div>
                                    <fc-select class="categorized" params="inline: true, 
                                        options: linksList, 
                                        value: links, 
                                        placeholder: 'Все ссылки', 
                                        multiple: true, 
                                        blockSelectedGroup: true"></fc-select>
                                </div>
                            </div>
                            <!-- /ko -->

                            <div class="d-flex d-md-none no-print mb-15p mt-15p">
                                <button class="flex-grow-1 f-btn" type="button" data-bind="
                                    click: function() {
                                        resetFilters();
                                    }">
                                    <foquz-icon class="f-btn-prepend" params="icon: 'bin'"></foquz-icon>
                                    Сбросить
                                </button>
                                <button class="flex-grow-1 f-btn f-btn-success" type="button" data-bind="
                                    disable: !periodErrorStateMatcher() || isAjaxSended() || (havePeriodError() && isSubmittedFilters),
                                    css: {
                                        'f-btn--loading': isAjaxSended()
                                    },
                                    click:  function(_,event) {
                                        event.preventDefault();
                                        submitFilters();
                                    }">
                                    <foquz-icon class="f-btn-prepend" params="icon: 'check'"></foquz-icon>
                                    Применить
                                </button>
                            </div>
                        </div>
                        <!-- /ko -->

                        <div class="cursor-pointer d-flex align-items-center justify-content-center f-color-primary f-fs-1-5 font-weight-500" data-bind="click: function() {
                            opened(!opened())
                        }" style="height: 20px; margin-bottom: 4px;">
                            <!-- ko if: opened -->
                            Свернуть фильтры
                            <!-- /ko -->

                            <!-- ko ifnot: opened -->
                            Показать фильтры
                            <!-- /ko -->
                        </div>

                    </div>
                    <!-- /ko -->
                    <!-- /Фильтры -->

                    <!-- ko ifnot: deleted -->
                    <!-- ko if: pointsModel.hasPoints  -->
                    <div class="f-card__divider only-print" style="display: none">
                        <div class="d-flex justify-content-between mr-n4">

                            <div class="d-flex" data-bind="using: pointsModel">
                                <foquz-stats-item class="adaptive">
                                    <div class="value" data-bind="text: $parent.maxPoints"></div>
                                    <div class="label">Max кол-во баллов</div>
                                </foquz-stats-item>
                                <foquz-stats-item class="adaptive">
                                    <div class="value">
                                        <span data-bind="text: $parent.avgPoints"></span>
                                        <span>/</span>
                                        <span data-bind="text: window.POLL.avg_percent + '%'"></span>
                                    </div>
                                    <div class="label">Среднее кол-во баллов</div>
                                </foquz-stats-item>
                                <div class="ml-auto pl-4 pr-20p d-flex align-items-center">
                                    <a class="no-print f-fs-1-5 font-weight-500 mr-4" href="/foquz/foquz-poll/answer?id=<?= $poll->id ?>">Все ответы</a>
                                    <a href="javascript:void(0)" class="no-print f-fs-1-5 font-weight-500" data-bind="click: function() { $root.openPointsModal() }">Распределение баллов</a>

                                </div>
                            </div>

                        </div>
                    </div>
                    <!-- /ko -->




                    <!-- ko template: {
                        foreach: templateIf(!isFilterApplied(), $data),
                        afterAdd: fadeAfterAddFactory(650, 200),
                        beforeRemove: fadeBeforeRemoveFactory(200)
                    } -->

                    <div class="question-statistics__questions">
                        <div>
                            <!-- ko foreach: questions -->
                            <div class="question-statistics__question" data-bind="css: {
                            'question-statistics__question--system': isSystem,
                            'question-statistics__question--disabled': disabled
                            }, attr: {
                                'data-id': question_id
                            }">
                                <div class="question-statistics__question-header">
                                    <span class="question-statistics__question-number" data-bind="text: ($index() + 1) + '.'"></span>
                                    <span class="question-statistics__question-short-name d-flex align-items-center" data-bind="attr: { title: isSystem ? 'Системная точка контакта' : '' }">
                                        <!-- ko if: isSystem -->
                                        <span style="color: #9BB0FB" class="d-inline-block mr-2 mb-1">
                                            <svg width="12" height="12">
                                                <use href="#star-icon"></use>
                                            </svg>
                                        </span>
                                        <!-- /ko -->
                                        <!-- ko if: shortName != '' -->
                                        <span class="mr-20p" data-bind="text: shortName"></span>
                                        <!-- /ko -->

                                        <!-- ko if: $data.donorId -->
                                        <span class="recipient-label f-color-danger f-fs-1 font-weight-normal">
                                            Реципиент вопроса <span data-bind="text: $root.getDonorIndex(donorId)"></span>
                                            <span class="cursor-pointer" style="margin-left: 9px;" data-bind="click: function() {
                                                    $root.scrollToQuestion(donorId);
                                                }">
                                                <svg style="margin-top: -3px; " width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M4 10.5L4 1.5M4 1.5L1 4.49994M4 1.5L7 4.5" stroke="#F96261" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                                </svg>
                                            </span>

                                        </span>
                                        <!-- /ko -->
                                    </span>
                                    <div class="spacer"></div>
                                    <!-- ko if: disabled -->

                                    <span class="question-statistics__question-deletion-indicator">Вопрос удален</span>
                                    <!-- /ko -->
                                    <span class="f-color-service">Ответов: <span data-bind="text: ($data.answersCount || 0).toLocaleString()" class="bold"></span></span>

                                </div>
                                <div class="question-statistics__question-body">
                                    <div class="question-statistics__question-name">
                                        <!-- ko text: name -->
                                        <!-- /ko -->
                                        <!-- ko if: $data.linkWithClientField -->
                                        <small class="question-statistics__service-text" data-bind="text: '(связан с параметром ' + $data.linkedClientField + ' контакта)'"></small>
                                        <!-- /ko -->
                                    </div>
                                    <?= $this->render('../../../../ko/pages/poll/stats/template.php'); ?>
                                </div>
                            </div>
                            <!-- /ko -->
                        </div>

                        <print-footer></print-footer>
                    </div>

                    <!-- /ko -->

                    <!-- ko template: {
                        foreach: templateIf(isFilterApplied(), $data),
                        afterAdd: fadeAfterAddFactory(650, 200),
                        beforeRemove: fadeBeforeRemoveFactory(200)
                    } -->
                    <spinner></spinner>
                    <!-- /ko -->

                    <!-- /ko -->

                    <!-- ko if: deleted -->
                    <div class="question-statistics__empty">
                        <div class="text">
                            <div class="question-statistics__no-data-container">
                                <img src="<?= Url::to(['/img/stats-empty.svg']) ?>" alt="" width="220">
                                По опросу пока не получено ни одного ответа
                            </div>
                        </div>
                    </div>
                    <!-- /ko -->
                </div>
            </div>

            <!-- ko component: { name: 'modal-container', params: { opens: modalOpens } } -->
            <!-- /ko -->

            <foquz-modals-container params="modals: modals"></foquz-modals-container>
        </div>
    </div>

</div>
