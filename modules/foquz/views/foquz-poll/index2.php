<?php

/* @var $this yii\web\View */
/* @var $dataProvider yii\data\ActiveDataProvider */
/* @var $dataProviderArchive yii\data\ActiveDataProvider */

use app\models\company\CompanyStaff;
use app\models\User;
use app\modules\foquz\models\FoquzPoll;
use yii\helpers\Json;

$this->title = \Yii::t('main', 'Опросы');
$this->params['breadcrumbs'][] = $this->title;

$hasData = (count($folders) > 0 || $allPolls > 0) ? 'true' : 'false';

$isTemplateCompany =  \Yii::$app->user->identity->company->id === \Yii::$app->params['template_company_id'] ? 1 : 0;

$this->registerJs("
    var FOLDERS = " . Json::encode(FoquzPoll::getFolderRecursive(null, 1)) . ";
    var SELECTED_CATEGORY = '" . Yii::$app->getRequest()->get('id', 0) . "';
    var HAS_POLLS_DATA = " . $hasData . ";
    
    window.pageData = {
        isTemplateCompany: " . $isTemplateCompany . "
    }

", $this::POS_HEAD);
$this->registerJSFile('/js/poll.list.js', ['depends' => [app\modules\foquz\assets\FoquzAsset::className()]]);
$this->registerCSSFile('/js/poll.list.css', ['depends' => [app\modules\foquz\assets\FoquzAsset::className()]]);
?>


<div class="polls-list survey-list__content" data-app-container data-bind="childrenComplete: onInit">


    <div class="f-card f-card--sm f-card--shadow polls-list__controls">
        <div class="polls-list-controls">
            <div class="polls-list-controls__links">
                <div class="polls-list-controls__link">
                    <a href="/foquz/foquz-poll/index?id=allPolls" class="no-link">
                        <span class="info-row__label mr-1"><?= \Yii::t('main', 'Все') ?></span>
                        <span class="f-label <?= (!isset($_GET['id']) || $_GET['id'] === 'allPolls') ? 'f-label-primary' : '' ?>" data-bind="text: totalLength"></span>
                    </a>
                </div>
                <?php if (!Yii::$app->user->identity->isFilialEmployee()) : ?>
                    <div class="polls-list-controls__link">
                        <a href="/foquz/foquz-poll/index?id=archive" class="no-link">
                            <span class="info-row__label"><?= \Yii::t('main', 'Архив') ?></span>
                            <span class="f-label <?= (isset($_GET['id']) && $_GET['id'] === 'archive') ? 'f-label-primary' : '' ?>" data-bind="text: archivedLength"></span>
                        </a>
                    </div>
                <?php endif; ?>
            </div>
            <!-- ko ifnot: $root.isEmpty -->
            <div class="polls-list-controls__view">
                <div class="polls-list-controls__sort">
                    <div class="form-group dense-form-group">
                        <div class="form-label">
                            <?= \Yii::t('main', 'Сортировать') ?>:
                        </div>
                        <select data-bind="
                                    value: filterSort,
                                    select2: {
                                        wrapperCssClass: 'select2-container--form-control',
                                        dropdownCssClass: 'dense-form-group__dropdown',
                                        containerCss: { 'min-width': '80px' }
                                    }
                                ">
                            <option value="created_at"><?= \Yii::t('polls', 'По дате') ?></option>
                            <option value="name"><?= \Yii::t('polls', 'По названию') ?></option>
                            <option value="view"><?= \Yii::t('polls', 'По просмотрам') ?></option>
                            <option value="answer"><?= \Yii::t('polls', 'По ответам') ?></option>
                        </select>
                    </div>
                </div>
                <div class="polls-list-controls__modes">
                    <button type="button" class="button-ghost" data-bind="click: function() {
                                viewMode('cards');
                            }">
                        <svg-icon params="name: 'view-mode-cards'" data-bind="css: {
                                'f-color-light': viewMode() != 'cards',
                                'f-color-primary': viewMode() == 'cards',
                            }" class="svg-icon--lg"></svg-icon>
                    </button>
                    <button type="button" class="button-ghost" data-bind="click: function() {
                                viewMode('table');
                            }">
                        <svg-icon params="name: 'view-mode-list'" data-bind="css: {
                                'f-color-light': viewMode() != 'table',
                                'f-color-primary': viewMode() == 'table',
                            }" class="svg-icon--lg"></svg-icon>
                    </button>
                </div>
            </div>
            <!-- /ko -->
        </div>
    </div>


    <div class="survey-list__card f-card f-card--shadow f-card--lg" data-bind="descendantsComplete: function() {
        initializing(false);
    }, css: {
        'survey-list__empty--watcher': window.CURRENT_USER.watcher && $root.isEmpty
            }">
        <div class="f-card__inner" style="visibility: hidden" data-bind="style: {
            'visibility': initializing() ? 'hidden' : ''
        }">

            <div class="f-card__divider">
                <div class="polls-list__meta">
                    <div class="polls-list__stats">
                        <foquz-stats-item class="adaptive">
                            <div class="value f-color-primary" data-bind="let: { value: <?= $sended ?> }">
                                <span data-bind="text: value.toLocaleString()"></span>
                            </div>
                            <div class="label text-nowrap"><?= \Yii::t('polls', 'Отправлено') ?></div>
                        </foquz-stats-item>
                        <foquz-stats-item class="adaptive">
                            <div class="value f-color-violet" data-bind="let: { value: <?= $opened ?> }">
                                <span data-bind="text: value.toLocaleString()"></span>
                            </div>
                            <div class="label text-nowrap"><?= \Yii::t('polls', 'Открыто') ?></div>
                        </foquz-stats-item>
                        <foquz-stats-item class="adaptive">
                            <div class="value f-color-blue" data-bind="let: { value: <?= $processed ?> }">
                                <span data-bind="text: value.toLocaleString()"></span>
                            </div>
                            <div class="label text-nowrap"><?= \Yii::t('polls', 'В процессе') ?></div>
                        </foquz-stats-item>
                        <foquz-stats-item class="adaptive">
                            <div class="value f-color-mint" data-bind="let: { value: <?= $filled ?> }">
                                <span data-bind="text: value.toLocaleString()"></span>
                            </div>
                            <div class="label text-nowrap"><?= \Yii::t('polls', 'Заполнено') ?></div>
                        </foquz-stats-item>
                        <foquz-stats-item class="adaptive">
                            <div class="value f-color-gold" data-bind="let: { value: <?= $goals ?> }">
                                <span data-bind="text: value.toLocaleString()"></span>
                            </div>
                            <div class="label text-nowrap"><?= \Yii::t('polls', 'Достигнуто целей') ?></div>
                        </foquz-stats-item>
                    </div>
                    <div class="polls-list__search">
                        <div class="polls-list-search" data-bind="component: { name: 'survey-list__search' }"></div>
                        <button type="button" class="polls-list__stats-toggler button-ghost f-color-primary" data-bind="click: function(_, e) {
                                let block = document.querySelector('.polls-list__stats');
                                if (block.hasAttribute('data-closed')) {
                                    $(block).slideDown(400);
                                    block.removeAttribute('data-closed');
                                    $(e.target).removeClass('f-color-light').addClass('f-color-primary');
                                } else {
                                    $(block).slideUp(400);
                                    block.setAttribute('data-closed', true);
                                    $(e.target).addClass('f-color-light').removeClass('f-color-primary');
                                }
                            }">
                            <svg-icon params="name: 'chart-column'" class="svg-icon--lg"></svg-icon>
                        </button>
                    </div>
                </div>
            </div>

            <!-- ko ifnot: $root.isEmpty -->
            <div class="f-card__section f-card__divider polls-list__filters">
                <div class="polls-list-filters">

                    <?php if (Yii::$app->user->identity->superadmin) : ?>
                        <div class="polls-list-filters__item">
                            <div class="form-group dense-form-group">
                                <label class="form-label"><?= \Yii::t('main', 'Компания') ?></label>
                                <select id="company_filter" data-bind="
                                    value: filterCompany,
                                    select2: {
                                        wrapperCssClass: 'select2-container--form-control',
                                        dropdownCssClass: 'dense-form-group__dropdown',
                                        minimumResultsForSearch: 0,
                                        allowClear: true,
                                        templateResult: folderTemplateResult
                                    }" data-placeholder="<?= \Yii::t('main', 'Все компании') ?>">
                                    <option></option>
                                    <?php
                                    $companies = \app\models\company\Company::find()->orderBy('name')->all();
                                    foreach ($companies as $company) { ?>
                                        <option value="<?= $company->id ?>"><?= $company->name ?></option>
                                    <?php } ?>
                                </select>
                            </div>
                        </div>
                    <?php endif; ?>


                    <div class="polls-list-filters__item">
                        <div class="form-group dense-form-group">
                            <label class="form-label"><?= \Yii::t('main', 'Папка') ?></label>
                            <select id="folder_filter" data-bind="
                                value: filterFolder,
                                lazySelect2: {
                                    minimumResultsForSearch: 0,
                                    wrapperCssClass: 'select2-container--form-control',
                                    dropdownCssClass: 'dense-form-group__dropdown',
                                    allowClear: true,
                                    templateResult: locationTemplateResult,
                                }" data-placeholder="<?= \Yii::t('main', 'Все папки') ?>">

                                <option></option>
                                <!-- ko foreach: foldersList -->
                                <!-- ko if: level == 1 -->
                                <option data-bind="value: id, text: name"></option>
                                <!-- /ko -->
                                <!-- /ko -->
                            </select>
                        </div>
                    </div>


                    <div class="polls-list-filters__item">
                        <div class="form-group dense-form-group">
                            <label class="form-label"><?= \Yii::t('polls', 'Автор') ?></label>
                            <select id="author_filter" data-bind="
                                value: filterAuthor,
                                select2: {
                                    wrapperCssClass: 'select2-container--form-control',
                                    dropdownCssClass: 'dense-form-group__dropdown',
                                    minimumResultsForSearch: 0,
                                    allowClear: true,
                                    templateResult: authorTemplateResult
                                }" data-placeholder="<?= \Yii::t('polls', 'Все авторы') ?>">
                                <option></option>
                                <?php
                                $companyIds = [];
                                $companies = CompanyStaff::find()->select('company_id')->where(['user_id' => Yii::$app->getUser()->id])->asArray()->all();
                                foreach ($companies as $company) {
                                    $companyIds[] = $company['company_id'];
                                }
                                foreach (User::find()
                                    ->select('user.*')
                                    ->addSelect(new \yii\db\Expression('IF(`user`.`name`= "" or `user`.`name` is null, `user`.`username`, `user`.`name`) as ifName'))
                                    ->join('LEFT JOIN', '{{company_staff}}', 'company_staff.user_id = user.id')
                                    ->where(['in', 'company_id', $companyIds])
                                    ->andWhere(['superadmin' => 0])
                                    ->orderBy(['ifName' => SORT_ASC])
                                    ->all() as $user) {
                                    if (!$user->isExecutor()) { ?>
                                        <option data-avatar-url="<?= $user->getThumbUploadUrl('avatar', 'preview') ?>" value="<?= $user->id ?>"><?= $user->name ? $user->name : $user->username ?></option>
                                <?php }
                                } ?>
                            </select>
                        </div>
                    </div>

                    <div class="polls-list-filters__item">

                        <input-checkbox params="checked: filterWithAnswer"><?= \Yii::t('polls', 'Только с ответами') ?></input-checkbox>
                    </div>
                </div>

                <div class="spacer"></div>

                <div data-bind="visible: !window.CURRENT_USER.watcher">
                    <?php if (!Yii::$app->user->identity->isFilialEmployee()) : ?>
                        <!-- ko if: editorWithoutFolders -->
                        <button class="f-btn f-btn-primary f-btn-lg polls-list__create-folder disabled" type="button" data-bind="tooltip, tooltipText: 'Нет доступных папок для создания новой папки. Обратитесь к администратору'"><?= \Yii::t('polls', 'Создать папку') ?>
                        </button>
                        <!-- /ko -->
                        <!-- ko ifnot: editorWithoutFolders -->
                        <button class="f-btn f-btn-primary f-btn-lg polls-list__create-folder" type="button" data-bind="click: function() { openNewFolderModal(); }"><?= \Yii::t('polls', 'Создать папку') ?>
                        </button>
                        <!-- /ko -->
                    <?php endif; ?>
                </div>
            </div>
            <!-- /ko -->


            <!-- ko if: checkingEditorFolders -->
            <fc-spinner class="f-color-primary"></fc-spinner>
            <!-- /ko -->

            <!-- ko ifnot: checkingEditorFolders -->
            <!-- ko if: editorWithoutFolders -->
            <div class="p-4 text-center f-color-service">Нет доступных папок для создания опросов и новых папок. Обратитесь к администратору</div>
            <!-- /ko -->

            <!-- ko ifnot: editorWithoutFolders -->
            <!-- ko if: $root.showEmptySection -->
            <div class="survey-list__empty f-card__section f-card__grow" data-bind="css: {
                'survey-list__empty--watcher': window.CURRENT_USER.watcher && $root.isEmpty
            }">
                <div class="d-flex justify-content-center flex-wrap" data-bind="css: {'align-items-center': window.CURRENT_USER.watcher, 'align-items-start': !window.CURRENT_USER.watcher}">
                    <svg width="220" height="220" viewBox="0 0 220 220" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="110" cy="110" r="110" fill="#EFF2FA" />
                        <path d="M43 79.832C43 69.432 51.431 61 61.832 61h87.097c10.401 0 18.832 8.431 18.832 18.832v54.142c0 10.4-8.431 18.831-18.832 18.831H61.832c-10.4 0-18.832-8.431-18.832-18.831V79.832z" fill="#255EB2" />
                        <path d="M113.031 170.46h8.239l-7.062-25.305-18.832 7.65 17.655 17.655z" fill="#255EB2" />
                        <path d="M51.239 79.832C51.239 69.432 59.67 61 70.07 61h87.097C167.569 61 176 69.431 176 79.832v54.142c0 10.4-8.431 18.831-18.832 18.831H70.071c-10.4 0-18.832-8.431-18.832-18.831V79.832zM121.27 170.46v-17.655h-17.655l17.655 17.655z" fill="#2D99FF" />
                        <path d="M174.646 95.31V78.574c0-8.958-7.268-16.22-16.233-16.22h-39.862M104.203 62.354H90.079" stroke="#AFD8FF" stroke-width="3" stroke-linecap="round" />
                        <rect x="96" y="87" width="50" height="5" rx="2.5" fill="#2876D1" />
                        <rect x="96" y="106" width="50" height="5" rx="2.5" fill="#2876D1" />
                        <rect x="96" y="125" width="50" height="5" rx="2.5" fill="#2876D1" />
                        <path d="M75.955 86.921l4.106 4.106 7.664-7.664M75.955 105.753l4.106 4.106 7.664-7.664M75.955 124.585l4.106 4.106 7.664-7.664" stroke="#6EB9FF" stroke-width="3" stroke-linecap="round" />
                    </svg>

                    <!-- ko if: !window.CURRENT_USER.watcher -->
                    <div class="text">
                        <p class="first-item">
                            <?= \Yii::t('polls', ' В разделе {section} можно создавать опросы вашей
                            компании.', [
                                'section' => "<strong>«" . \Yii::t('main', 'Опросы') . "»</strong>"
                            ]) ?>
                            <br />
                            <?= \Yii::t('polls', 'Для удобства просмотра они разделены на категории:') ?>
                        </p>
                        <ul>
                            <li><?= \Yii::t('main', 'опросы с достигнутыми целями') ?>;</li>
                            <li><?= \Yii::t('main', 'последние опросы') ?>;</li>
                            <li><?= \Yii::t('main', 'папки') ?>;</li>
                            <li><?= \Yii::t('main', 'все опросы') ?>;</li>
                            <li><?= \Yii::t('main', 'архив') ?>.</li>
                        </ul>
                        <p>
                            <?= \Yii::t('polls', 'Для создания нового опроса нажмите кнопку {button} и выберите тип опроса: ручной или автоматический.', [
                                'button' => "«<strong>" . \Yii::t('main', 'Создать опрос') . "</strong>»"
                            ]) ?>

                        </p>
                        <p>
                            <?= \Yii::t('polls', 'Для ручных опросов можно сформировать рассылку по базе клиентов.') ?>
                        </p>
                        <p>
                            <?= \Yii::t('polls', 'Автоматический опрос будет отправляться клиенту, если выполнен определённый триггер.') ?>
                        </p>
                    </div>
                    <!-- /ko -->

                    <!-- ko if: window.CURRENT_USER.watcher -->
                    <div class="text">
                        Нет доступных папок для отображения опросов. Обратитесь к администратору.
                    </div>
                    <!-- /ko -->
                </div>
            </div>
            <!-- /ko -->

            <!-- ko ifnot: $root.showEmptySection -->
            <div id="main-content" class="f-card__inner position-relative" data-bind="css: {
                'category-mode': openCategoryMode,
                'categories-mode': !openCategoryMode(),
             }">
                <group-actions class="group-actions" params="controller: groupActions"></group-actions>

                <group-actions class="group-actions" params="controller: archivedGroupActions"></group-actions>

                <!-- ko if: openCategoryMode -->
                <div class="f-card__grow">
                    <div class="f-card__section polls-list__content">

                        <div class="breadcrumbs survey-list__breadcrumbs">
                            <!-- ko foreach: breads -->
                            <!-- ko if: $index() < $parent.breads().length - 1 -->
                            <a class="breadcrumbs__item" href="#" data-bind="click: function () {$root.openCategory([], '', $data.id)}, text:$data.label"></a>
                            <!-- /ko -->
                            <!-- ko ifnot: $index() < $parent.breads().length - 1 -->
                            <a class="breadcrumbs__item cursor-default" href="#" data-bind="text:$data.label"></a>
                            <!-- /ko -->
                            <!-- /ko -->
                        </div>


                        <!-- ko if: viewMode() === 'cards' -->

                        <div class="survey-list__survey-cards" data-bind="">
                            <div class="survey-list__survey-cards-category">
                                <div class="survey-list__survey-cards-category-header" data-bind="visible: !loading()">
                                    <div class="survey-list__survey-cards-category-title">
                                        <div class="survey-list__survey-cards-category-counter" data-bind="text: openedLength"></div>
                                        <div class="survey-list__survey-cards-category-name" data-bind="text: categoryName"></div>
                                    </div>
                                </div>

                                <div class="survey-list__survey-cards-category-content">
                                    <div class="survey-list__survey-cards-category-list">
                                        <!-- ko template: { name: 'survey-list-survey-cards-category-list-content-template', data: { elements: openedElements } } -->
                                        <!-- /ko -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="pages-loader" data-bind="visible: loading() || ajaxLoading()" title="<?= \Yii::t('main', 'Пожалуйста, подождите...') ?>">
                            <i class="fa fa-spinner fa-pulse fa-2x fa-fw"></i>
                        </div>

                        <!-- /ko -->

                        <!-- ko if: viewMode() === 'table' -->
                        <div class="survey-list__survey-table-category-header" data-bind="visible: !loading()">
                            <div class="survey-list__survey-table-category-title">
                                <div class="survey-list__survey-table-category-counter" data-bind="text: openedLength"></div>
                                <div class="survey-list__survey-table-category-name" data-bind="text: categoryName"></div>
                            </div>
                        </div>



                        <complex-table params="scroll: true" class="mt-15p">
                            <div class="survey-list__survey-table survey-list__survey-single-category-table mt-0">
                                <!-- ko using: $root -->
                                <div class="survey-list__survey-table-head">
                                    <div class="survey-list__survey-table-head-cell survey-list__survey-table-checked-head-cell"></div>

                                    <div class="survey-list__survey-table-head-cell survey-list__survey-table-name-head-cell">
                                        <?= \Yii::t('main', 'Название') ?>
                                    </div>
                                    <div class="survey-list__survey-table-head-cell survey-list__survey-table-owner-name-head-cell">
                                        <?= \Yii::t('polls', 'Владелец') ?>
                                    </div>
                                    <div class="survey-list__survey-table-head-cell survey-list__survey-table-created-at-head-cell">
                                        <?= \Yii::t('main', 'Дата создания') ?>
                                    </div>
                                    <div class="survey-list__survey-table-head-cell survey-list__survey-table-send-head-cell">
                                        <?= \Yii::t('polls', 'Отправлено') ?>
                                    </div>
                                    <div class="survey-list__survey-table-head-cell survey-list__survey-table-open-head-cell">
                                        <?= \Yii::t('polls', 'Открыто') ?>
                                    </div>
                                    <div class="survey-list__survey-table-head-cell survey-list__survey-table-process-head-cell">
                                        <?= \Yii::t('polls', 'В процессе') ?>
                                    </div>
                                    <div class="survey-list__survey-table-head-cell survey-list__survey-table-filled-head-cell">
                                        <?= \Yii::t('polls', 'Заполнено') ?>
                                    </div>
                                    <div class="survey-list__survey-table-head-cell survey-list__survey-table-target-head-cell">
                                        <?= \Yii::t('polls', 'Достигнуто целей') ?>
                                    </div>
                                    <div class="survey-list__survey-table-head-cell survey-list__survey-table-status-head-cell">
                                        <?= \Yii::t('main', 'Статус') ?>
                                    </div>
                                    <div class="survey-list__survey-table-head-cell survey-list__survey-table-favour-head-cell"></div>
                                    <div class="survey-list__survey-table-head-cell survey-list__survey-table-actions-head-cell"></div>
                                </div>
                                <div class="survey-list__survey-table-body">
                                    <!-- ko template: { name: 'survey-list-survey-table-category-content-template', data: { elements: openedElements } } -->
                                    <!-- /ko -->
                                </div>
                                <!-- /ko -->
                            </div>
                        </complex-table>


                        <div class="pages-loader" data-bind="visible: loading() || ajaxLoading()" title="<?= \Yii::t('main', 'Пожалуйста, подождите...') ?>">
                            <i class="fa fa-spinner fa-pulse fa-2x fa-fw"></i>
                        </div>

                        <!-- /ko -->


                    </div>
                </div>
                <div class="f-card__footer">
                    <a data-bind="click: closeCategory" class="btn btn-link report__back-button">
                        <?= \Yii::t('main', 'Назад') ?>
                    </a>
                </div>
                <!-- /ko -->

                <!-- ko ifnot: openCategoryMode -->
                <div class="f-card__grow">
                    <!-- ko if: !loading() && allLength() == 0 && folderLength() == 0 -->
                    <div class="d-flex justify-content-center p-4 color-service"><?= \Yii::t('main', 'Ничего не найдено') ?></div>
                    <!-- /ko -->

                    <!-- ko ifnot: updating -->

                    <!-- ko if: viewMode() === 'cards' -->
                    <div class="f-card__section  polls-list__content">
                        <div class="survey-list__survey-cards">
                            <!-- ko let: { open: ko.observable(true) } -->
                            <div class="survey-list__survey-cards-category" data-bind="css: { 'survey-list__survey-cards-category--open': open() }, visible: (showTitle && favourLength() > 0)">
                                <div class="survey-list__survey-cards-category-header">
                                    <div class="survey-list__survey-cards-category-title" data-bind="click: function () { open(!open()); }">
                                        <div class="survey-list__survey-cards-category-counter" data-bind="text: favourLength"></div>
                                        <div class="survey-list__survey-cards-category-name"><?= \Yii::t('main', 'Избранное') ?></div>
                                        <i class="survey-list__survey-cards-category-arrow"></i>
                                    </div>
                                </div>

                                <div class="survey-list__survey-cards-category-content" data-bind="slide: open()">
                                    <div class="survey-list__survey-cards-category-list">
                                        <!-- ko template: { name: 'survey-list-survey-cards-category-list-content-template', data: { elements: favourElements } } -->
                                        <!-- /ko -->

                                    </div>

                                    <a href="#" data-bind="click: function() { openCategory(favourElements, _t('Избранное'), 'favorite'); }, visible: (favourLength() > 4 ? true : false)" class="btn btn-default survey-list__survey-cards-category-show-more-button"><?= \Yii::t('main', 'Показать еще') ?></a>
                                </div>
                            </div>
                            <!-- /ko -->

                            <!-- ko let: { open: ko.observable(true) } -->
                            <div class="survey-list__survey-cards-category" data-bind="css: { 'survey-list__survey-cards-category--open': open() }, visible: showTitle && targetLength() > 0">
                                <div class="survey-list__survey-cards-category-header">
                                    <div class="survey-list__survey-cards-category-title" data-bind="click: function () { open(!open()); }">
                                        <div class="survey-list__survey-cards-category-counter" data-bind="text: targetLength"></div>
                                        <div class="survey-list__survey-cards-category-name"><?= \Yii::t('main', 'Опросы с достигнутыми целями') ?></div>
                                        <i class="survey-list__survey-cards-category-arrow"></i>
                                    </div>
                                </div>

                                <div class="survey-list__survey-cards-category-content" data-bind="slide: open()">
                                    <div class="survey-list__survey-cards-category-list">
                                        <!-- ko template: { name: 'survey-list-survey-cards-category-list-content-template', data: { elements: targetElements } } -->
                                        <!-- /ko -->
                                    </div>

                                    <a href="#" data-bind="visible: targetLength() > 4, click: function() { openCategory(targetElements, '<?= \Yii::t('main', 'Опросы с достигнутыми целями') ?>', 'goalPolls'); }" class="btn btn-default survey-list__survey-cards-category-show-more-button">
                                        <?= \Yii::t('main', 'Показать ещё') ?></a>
                                </div>
                            </div>
                            <!-- /ko -->

                            <!-- ko let: { open: ko.observable(true) } -->
                            <div class="survey-list__survey-cards-category" data-bind="css: { 'survey-list__survey-cards-category--open': open() }, visible: showTitle && lastLength() > 0">
                                <div class="survey-list__survey-cards-category-header">
                                    <div class="survey-list__survey-cards-category-title" data-bind="click: function () { open(!open()); }">
                                        <div class="survey-list__survey-cards-category-counter" data-bind="text:lastLength"></div>
                                        <div class="survey-list__survey-cards-category-name"><?= \Yii::t('main', 'Последние опросы') ?></div>
                                        <i class="survey-list__survey-cards-category-arrow"></i>
                                    </div>
                                </div>

                                <div class="survey-list__survey-cards-category-content" data-bind="slide: open()">
                                    <div class="survey-list__survey-cards-category-list">
                                        <!-- ko template: { name: 'survey-list-survey-cards-category-list-content-template', data: { elements: lastElements.slice(0,4) } } -->
                                        <!-- /ko -->
                                    </div>

                                    <a href="#" data-bind="click: function() { openCategory(lastElements, _t('Последние опросы'), 'lastPolls'); }, visible: lastLength() > 4" class="btn btn-default survey-list__survey-cards-category-show-more-button"><?= \Yii::t('main', 'Показать ещё') ?></a>
                                </div>
                            </div>
                            <!-- /ko -->

                            <!-- ko let: { open: ko.observable(true) } -->
                            <div class="survey-list__survey-cards-category" data-bind="css: { 'survey-list__survey-cards-category--open': open() }, visible: showTitle && folderLength() > 0">
                                <div class="survey-list__survey-cards-category-header">
                                    <div class="survey-list__survey-cards-category-title" data-bind="click: function () { open(!open()); }">
                                        <div class="survey-list__survey-cards-category-counter" data-bind="text:folderLength"></div>
                                        <div class="survey-list__survey-cards-category-name"><?= \Yii::t('main', 'Папки') ?></div>
                                        <i class="survey-list__survey-cards-category-arrow"></i>
                                    </div>
                                </div>

                                <div class="survey-list__survey-cards-category-content" data-bind="slide: open()">
                                    <div class="survey-list__survey-cards-category-list">
                                        <!-- ko template: { name: 'survey-list-survey-cards-category-list-content-template', data: { elements: folderElements } } -->
                                        <!-- /ko -->
                                    </div>

                                    <a href="#" data-bind="click: function() { openCategory(folderElements, _t('Папки'), 'folders'); }, visible: folderLength() > 4" class="btn btn-default survey-list__survey-cards-category-show-more-button"><?= \Yii::t('main', 'Показать ещё') ?></a>
                                </div>
                            </div>
                            <!-- /ko -->

                            <!-- ko let: { open: ko.observable(true) } -->
                            <div class="survey-list__survey-cards-category" data-bind="css: { 'survey-list__survey-cards-category--open': open() }, visible: showTitle && allLength() > 0">
                                <div class="survey-list__survey-cards-category-header">
                                    <div class="survey-list__survey-cards-category-title" data-bind="click: function () { open(!open()); }">
                                        <div class="survey-list__survey-cards-category-counter" data-bind="text:allLength"></div>
                                        <div class="survey-list__survey-cards-category-name"><?= \Yii::t('main', 'Все опросы') ?></div>
                                        <i class="survey-list__survey-cards-category-arrow"></i>
                                    </div>
                                </div>

                                <div class="survey-list__survey-cards-category-content" data-bind="slide: open()">
                                    <div class="survey-list__survey-cards-category-list">
                                        <!-- ko template: { name: 'survey-list-survey-cards-category-list-content-template', data: { elements: allElements } } -->
                                        <!-- /ko -->
                                    </div>

                                    <a href="#" data-bind="click: function() { openCategory(allElements, _t('Все опросы'), 'allPolls'); }, visible: allLength() > 4" class="btn btn-default survey-list__survey-cards-category-show-more-button"><?= \Yii::t('main', 'Показать ещё') ?></a>
                                </div>
                            </div>
                            <!-- /ko -->

                            <?php if (!Yii::$app->user->identity->isFilialEmployee()) : ?>
                                <!-- ko let: { open: ko.observable(true) } -->
                                <div class="survey-list__survey-cards-category" data-bind="css: { 'survey-list__survey-cards-category--open': open() }, visible: showTitle && archivedLength() > 0">
                                    <div class="survey-list__survey-cards-category-header">
                                        <div class="survey-list__survey-cards-category-title" data-bind="click: function () { open(!open()); }">
                                            <div class="survey-list__survey-cards-category-counter" data-bind="text:archivedLength"></div>
                                            <div class="survey-list__survey-cards-category-name"><?= \Yii::t('main', 'В архиве') ?></div>
                                            <i class="survey-list__survey-cards-category-arrow"></i>
                                        </div>
                                    </div>

                                    <div class="survey-list__survey-cards-category-content" data-bind="slide: open()">
                                        <div class="survey-list__survey-cards-category-list">
                                            <!-- ko template: { name: 'survey-list-survey-cards-category-list-content-template', data: { elements: archivedElements } } -->
                                            <!-- /ko -->
                                        </div>

                                        <a href="#" data-bind="click: function() { openCategory(archivedElements, _t('В архиве'), 'archive'); }, visible: archivedLength() > 4" class="btn btn-default survey-list__survey-cards-category-show-more-button">
                                            <?= \Yii::t('main', 'Показать ещё') ?>
                                        </a>
                                    </div>
                                </div>
                                <!-- /ko -->
                            <?php endif; ?>
                        </div>
                    </div>
                    <!-- /ko -->

                    <!-- ko if: viewMode() === 'table' -->
                    <div class="f-card__section">
                        <complex-table params="scroll: true">
                            <div class="survey-list__survey-table" data-bind="using: $root">
                                <div class="survey-list__survey-table-head">
                                    <div class="survey-list__survey-table-head-cell survey-list__survey-table-checked-head-cell"></div>

                                    <div class="survey-list__survey-table-head-cell survey-list__survey-table-name-head-cell"><?= \Yii::t('main', 'Название') ?>
                                    </div>
                                    <div class="survey-list__survey-table-head-cell survey-list__survey-table-owner-name-head-cell">
                                        <?= \Yii::t('polls', 'Владелец') ?>
                                    </div>
                                    <div class="survey-list__survey-table-head-cell survey-list__survey-table-created-at-head-cell"><?= \Yii::t('main', 'Дата создания') ?>
                                    </div>
                                    <div class="survey-list__survey-table-head-cell survey-list__survey-table-send-head-cell"><?= \Yii::t('polls', 'Отправлено') ?>
                                    </div>
                                    <div class="survey-list__survey-table-head-cell survey-list__survey-table-open-head-cell"><?= \Yii::t('polls', 'Открыто') ?>
                                    </div>
                                    <div class="survey-list__survey-table-head-cell survey-list__survey-table-process-head-cell">
                                        <?= \Yii::t('polls', 'В процессе') ?>
                                    </div>
                                    <div class="survey-list__survey-table-head-cell survey-list__survey-table-filled-head-cell">
                                        <?= \Yii::t('polls', 'Заполнено') ?>
                                    </div>
                                    <div class="survey-list__survey-table-head-cell survey-list__survey-table-target-head-cell">
                                        <?= \Yii::t('polls', 'Достигнуто целей') ?>
                                    </div>
                                    <div class="survey-list__survey-table-head-cell survey-list__survey-table-status-head-cell"><?= \Yii::t('main', 'Статус') ?>
                                    </div>
                                    <div class="survey-list__survey-table-head-cell survey-list__survey-table-favour-head-cell"></div>
                                    <div class="survey-list__survey-table-head-cell survey-list__survey-table-actions-head-cell"></div>
                                </div>

                                <div class="survey-list__survey-table-body">
                                    <!-- ko let: { open: ko.observable(true) } -->
                                    <div class="survey-list__survey-table-category" data-bind="css: { 'survey-list__survey-table-category--open': open() }, visible: (showTitle && favourLength() > 0)">
                                        <div class="survey-list__survey-table-category-header">
                                            <div class="survey-list__survey-table-category-header-wrapper">
                                                <div class="survey-list__survey-table-category-title" data-bind="click: function () { open(!open()); }">
                                                    <div class="survey-list__survey-table-category-counter" data-bind="text: favourLength"></div>
                                                    <div class="survey-list__survey-table-category-name"><?= \Yii::t('main', 'Избранное') ?></div>
                                                    <i class="survey-list__survey-table-category-arrow"></i>
                                                </div>
                                                <div class="spacer"></div>
                                                <div class="show-more">
                                                    <a data-bind="click: function() { openCategory(favourElements, _t('Избранное'),'favorite'); }, visible: (favourLength() > 4 ? true : false)" class="btn btn-link survey-list__survey-table-category-show-more-button" href="#"><?= \Yii::t('main', 'Показать все') ?></a>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="survey-list__survey-table-category-content" data-bind="slide: open()">
                                            <!-- ko template: { name: 'survey-list-survey-table-category-content-template', data: { elements: favourElements } } -->
                                            <!-- /ko -->
                                        </div>
                                    </div>
                                    <!-- /ko -->
                                    <!-- ko let: { open: ko.observable(true) } -->
                                    <div class="survey-list__survey-table-category" data-bind="css: { 'survey-list__survey-table-category--open': open() }, visible: showTitle && targetLength() > 0">
                                        <div class="survey-list__survey-table-category-header">
                                            <div class="survey-list__survey-table-category-header-wrapper">
                                                <div class="survey-list__survey-table-category-title" data-bind="click: function () { open(!open()); }">
                                                    <div class="survey-list__survey-table-category-counter" data-bind="text: targetLength"></div>
                                                    <div class="survey-list__survey-table-category-name"><?= \Yii::t('main', 'Опросы с достигнутыми целями') ?></div>
                                                    <i class="survey-list__survey-table-category-arrow"></i>
                                                </div>
                                                <div class="spacer"></div>
                                                <div class="show-more">
                                                    <a data-bind="visible: targetLength() > 4,click: function() { openCategory(targetElements, _t('main', 'Опросы с достигнутыми целями'),'goalPolls'); }" class="btn btn-link survey-list__survey-table-category-show-more-button" href="#"><?= \Yii::t('main', 'Показать все') ?></a>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="survey-list__survey-table-category-content" data-bind="slide: open()">
                                            <!-- ko template: { name: 'survey-list-survey-table-category-content-template', data: { elements: targetElements } } -->
                                            <!-- /ko -->
                                        </div>
                                    </div>
                                    <!-- /ko -->

                                    <!-- ko let: { open: ko.observable(true) } -->
                                    <div class="survey-list__survey-table-category" data-bind="visible: lastLength, css: { 'survey-list__survey-table-category--open': open() }, visible: showTitle && lastLength() > 0">
                                        <div class="survey-list__survey-table-category-header">
                                            <div class="survey-list__survey-table-category-header-wrapper">
                                                <div class="survey-list__survey-table-category-title" data-bind="click: function () { open(!open()); }">
                                                    <div class="survey-list__survey-table-category-counter" data-bind="text:lastLength"></div>
                                                    <div class="survey-list__survey-table-category-name"><?= \Yii::t('main', 'Последние опросы') ?></div>
                                                    <i class="survey-list__survey-table-category-arrow"></i>
                                                </div>
                                                <div class="spacer"></div>
                                                <div class="show-more">
                                                    <a data-bind="click: function() { openCategory(lastElements, _t('main', 'Последние опросы'),'lastPolls'); }, visible: lastLength() > 4" class="btn btn-link survey-list__survey-table-category-show-more-button" href="#"><?= \Yii::t('main', 'Показать все') ?></a>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="survey-list__survey-table-category-content" data-bind="slide: open()">
                                            <!-- ko template: { name: 'survey-list-survey-table-category-content-template', data: { elements: lastElements.slice(0,4) } } -->
                                            <!-- /ko -->
                                        </div>
                                    </div>
                                    <!-- /ko -->

                                    <!-- ko let: { open: ko.observable(true) } -->
                                    <div class="survey-list__survey-table-category" data-bind="css: { 'survey-list__survey-table-category--open': open() }, visible: showTitle && folderLength() > 0">
                                        <div class="survey-list__survey-table-category-header">
                                            <div class="survey-list__survey-table-category-header-wrapper">
                                                <div class="survey-list__survey-table-category-title" data-bind="click: function () { open(!open()); }">
                                                    <div class="survey-list__survey-table-category-counter" data-bind="text:folderLength"></div>
                                                    <div class="survey-list__survey-table-category-name"><?= \Yii::t('main', 'Папки') ?></div>
                                                    <i class="survey-list__survey-table-category-arrow"></i>
                                                </div>
                                                <div class="spacer"></div>
                                                <div class="show-more">
                                                    <a data-bind="click: function() { openCategory(folderElements, _t('Папки'), 'folders'); }, visible: folderLength() > 4" class="btn btn-link survey-list__survey-table-category-show-more-button" href="#"><?= \Yii::t('main', 'Показать все') ?></a>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="survey-list__survey-table-category-content" data-bind="slide: open()">
                                            <!-- ko template: { name: 'survey-list-survey-table-category-content-template', data: { elements: folderElements } } -->
                                            <!-- /ko -->
                                        </div>
                                    </div>
                                    <!-- /ko -->

                                    <!-- ko let: { open: ko.observable(true) } -->
                                    <div class="survey-list__survey-table-category" data-bind="css: { 'survey-list__survey-table-category--open': open() }, visible: showTitle && allLength() > 0">
                                        <div class="survey-list__survey-table-category-header">
                                            <div class="survey-list__survey-table-category-header-wrapper">
                                                <div class="survey-list__survey-table-category-title" data-bind="click: function () { open(!open()); }">
                                                    <div class="survey-list__survey-table-category-counter" data-bind="text:allLength">
                                                    </div>
                                                    <div class="survey-list__survey-table-category-name"><?= \Yii::t('main', 'Все опросы') ?></div>
                                                    <i class="survey-list__survey-table-category-arrow"></i>
                                                </div>
                                                <div class="spacer"></div>
                                                <div class="show-more">
                                                    <a data-bind="click: function() { openCategory(allElements, _t('Все опросы'), 'allPolls'); }, visible: allLength() > 4" class="btn btn-link survey-list__survey-table-category-show-more-button" href="#"><?= \Yii::t('main', 'Показать все') ?></a>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="survey-list__survey-table-category-content" data-bind="slide: open()">
                                            <!-- ko template: { name: 'survey-list-survey-table-category-content-template', data: { elements: allElements } } -->
                                            <!-- /ko -->
                                        </div>
                                    </div>
                                    <!-- /ko -->

                                    <?php if (!Yii::$app->user->identity->isFilialEmployee()) : ?>
                                        <!-- ko let: { open: ko.observable(true) } -->
                                        <div class="survey-list__survey-table-category" data-bind="css: { 'survey-list__survey-table-category--open': open() }, visible: showTitle && archivedLength() > 0">
                                            <div class="survey-list__survey-table-category-header">
                                                <div class="survey-list__survey-table-category-header-wrapper">
                                                    <div class="survey-list__survey-table-category-title" data-bind="click: function () { open(!open()); }">
                                                        <div class="survey-list__survey-table-category-counter" data-bind="text:archivedLength"></div>
                                                        <div class="survey-list__survey-table-category-name"><?= \Yii::t('main', 'В архиве') ?></div>
                                                        <i class="survey-list__survey-table-category-arrow"></i>
                                                    </div>
                                                    <div class="spacer"></div>
                                                    <div class="show-more">
                                                        <a data-bind="click: function() { openCategory(archivedElements, _t('В архиве'), 'archive'); }, visible: archivedLength() > 4" class="btn btn-link survey-list__survey-table-category-show-more-button" href="#"><?= \Yii::t('main', 'Показать все') ?></a>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="survey-list__survey-table-category-content" data-bind="slide: open()">
                                                <!-- ko template: { name: 'survey-list-survey-table-category-content-template', data: { elements: archivedElements } } -->
                                                <!-- /ko -->
                                            </div>
                                        </div>
                                        <!-- /ko -->
                                    <?php endif; ?>
                                </div>
                            </div>
                        </complex-table>
                    </div>
                    <!-- /ko -->

                    <!-- /ko -->

                    <div class="pages-loader mb-4" data-bind="visible: loading, tooltip, tooltipText: _t('Пожалуйста, подождите...')">
                        <i class="fa fa-spinner fa-pulse fa-2x fa-fw"></i>
                    </div>
                </div>

                <!-- /ko -->
            </div>
            <!-- /ko -->
            <!-- /ko -->
            <!-- /ko -->
        </div>
    </div>

    <success-message
        params="show: successToast, text: successMessage, showCross: true, delay: 3000, centered: true"></success-message>
    <success-message
        class="foquz-success-message--error"
        params="show: errorToast, text: errorMessage, showCross: true, delay: 3000, centered: true"></success-message>
</div>

<script type="text/html" id="survey-list-survey-cards-category-list-content-template">
    <!-- ko foreach: { data: elements } -->
<!-- ko if: type === 0 -->
<div data-bind="component: {
                        name: 'survey-list__folder-card',
                        params: {
                            model: viewModel,
                            enableEdit: window.pageData.isTemplateCompany,
                            deleteClick: function () { $root.openDeleteFolderModal(viewModel); },
                            moveClick: function () { $root.openMoveModal(0, viewModel); },
                            renameClick: function () { $root.renameFolder(viewModel); },
                            editClick: function() { $root.editFolder(viewModel) },
                            openFolderClick: function () { $root.openFolder(0, viewModel.id); },
                            addToFavorite: function () { $root.addToFavor(viewModel.id); }
                        }
                    }">
</div>
<!-- /ko -->

<!-- ko if: type === 1 -->
<poll-card params="model: viewModel,
                    duplicateClick: function () { $root.openDuplicateSurveyModal(viewModel.id); },
                    moveClick: function () { $root.openMoveModal(1, viewModel); },
                    renameClick: function () { $root.renamePoll(viewModel); },
                    archiveClick: function () { $root.openArchiveSurveyModal(viewModel.id); },
                    deleteClick: function () { $root.openDeleteSurveyModal(viewModel.id, viewModel); },
                    restoreClick: function () { $root.openRestoreSurveyModal(viewModel.id, viewModel); },
                    openPoll: function () { $root.openRestoreSurveyModal(viewModel.link); },
                    addToFavorite: function () { $root.addToFavor(viewModel.id); }"></poll-card>
<!-- /ko -->
<!-- /ko -->
</script>
<script type="text/html" id="survey-list-survey-table-category-content-template">
    <!-- ko foreach: { data: elements, beforeRemove: $root.beforeElementRemove } -->
<!-- ko if: type === 0 -->
<div data-bind="component: {
                name: 'survey-list__survey-table-folder-row',
                params: {
                    ...viewModel,
                    enableEdit: window.pageData.isTemplateCompany,
                    deleteClick: function () { $root.openDeleteFolderModal(viewModel); },
                    renameClick: function () { $root.renameFolder(viewModel); },
                    moveClick: function () { $root.openMoveModal(0, viewModel); },
                    editClick: function() { $root.editFolder(viewModel) },
                    openFolderClick: function () { $root.openFolder(0, viewModel.id); },
                    addToFavorite: function () { $root.addToFavor(viewModel.id); }
                }
            }">
</div>
<!-- /ko -->

<!-- ko if: type === 1 -->
<div data-bind="component: {
                name: 'survey-list__survey-table-survey-row',
                params: {
                    model: viewModel,
                    duplicateClick: function () { $root.openDuplicateSurveyModal(viewModel.id); },
                    moveClick: function () { $root.openMoveModal(1, viewModel); },
                    renameClick: function () { $root.renamePoll(viewModel); },
                    archiveClick: function () { $root.openArchiveSurveyModal(viewModel.id); },
                    deleteClick: function () { $root.openDeleteSurveyModal(viewModel.id); },
                    restoreClick: function () { $root.openRestoreSurveyModal(viewModel.id); },
                    openPoll: function () { $root.openRestoreSurveyModal(viewModel.link); },
                    addToFavorite: function () { $root.addToFavor(viewModel.id); }
                }
            }">
</div>
<!-- /ko -->
<!-- /ko -->
</script>
<script type="text/html" id="survey-list-element-card-statistics-template">
    <table class="survey-list__element-card-statistics">
        <tbody>
            <tr class="survey-list__element-card-statistics-item" data-bind="attr: { title: _t('polls', 'Отправлено') + ': ' + sendCount }, tooltip, visible: sendCount!='0'">
                <td class="survey-list__element-card-statistics-item-value" data-bind="text: sendCount"></td>
                <td class="survey-list__element-card-statistics-item-graph survey-list__element-card-statistics-item-send-graph">
                    <div class="survey-list__element-card-statistics-item-graph-line" data-bind="style: { width: calcWidth(sendCount) + '%' }"></div>
                </td>
                <td class="survey-list__element-card-statistics-item-value">100%</td>
            </tr>

            <tr class="survey-list__element-card-statistics-item" data-bind="attr: { title: _t('polls', 'Открыто') + ': ' + openCount }, tooltip, visible: openCount!='0'">
                <td class="survey-list__element-card-statistics-item-value" data-bind="text: openCount"></td>
                <td class="survey-list__element-card-statistics-item-graph survey-list__element-card-statistics-item-open-graph">
                    <div class="survey-list__element-card-statistics-item-graph-line" data-bind="style: { width: calcWidth(openCount) + '%' }"></div>
                </td>
                <td class="survey-list__element-card-statistics-item-value">
                    <!-- ko text: calcWidth(openCount) + '%' -->
                    <!-- /ko -->
                </td>
            </tr>

            <tr class="survey-list__element-card-statistics-item" data-bind="attr: { title: _t('polls', 'В процессе') + ': ' + processCount }, tooltip, visible: processCount!='0'">
                <td class="survey-list__element-card-statistics-item-value" data-bind="text: processCount"></td>
                <td class="survey-list__element-card-statistics-item-graph survey-list__element-card-statistics-item-process-graph">
                    <div class="survey-list__element-card-statistics-item-graph-line" data-bind="style: { width: calcWidth(processCount) + '%' }"></div>
                </td>
                <td class="survey-list__element-card-statistics-item-value">
                    <!-- ko text: calcWidth(processCount) + '%' -->
                    <!-- /ko -->
                </td>
            </tr>

            <tr class="survey-list__element-card-statistics-item" data-bind="attr: { title: _t('polls', 'Заполнено') + ': ' + filledCount }, tooltip, visible: filledCount!='0'">
                <td class="survey-list__element-card-statistics-item-value" data-bind="text: filledCount"></td>
                <td class="survey-list__element-card-statistics-item-graph survey-list__element-card-statistics-item-filled-graph">
                    <div class="survey-list__element-card-statistics-item-graph-line" data-bind="style: { width: calcWidth(filledCount) + '%' }"></div>
                </td>
                <td class="survey-list__element-card-statistics-item-value">
                    <!-- ko text: calcWidth(filledCount) + '%' -->
                    <!-- /ko -->
                </td>
            </tr>


            <tr class="survey-list__element-card-statistics-item" data-bind="attr: {
                        title: _t('polls', 'Достигнуто целей') + ': ' + _t('main', '{num1} из {num2}', {
                            num1: currentTarget,
                            num2: targetCount
                        })
                }, tooltip, visible: currentTarget > 0 && targetCount > 0">
                <td class="survey-list__element-card-statistics-item-value" data-bind="text: currentTarget"></td>
                <td class="survey-list__element-card-statistics-item-graph survey-list__element-card-statistics-item-target-graph">
                    <div class="survey-list__element-card-statistics-item-graph-line" data-bind="style: { width: calcGoalWidth(currentTarget) + '%' }"></div>
                </td>
                <td class="survey-list__element-card-statistics-item-value">
                    <!-- ko text: calcGoalWidth(currentTarget) + '%' -->
                    <!-- /ko -->
                </td>
            </tr>

        </tbody>
    </table>
</script>


<?= $this->render('../../../../ko/legacy/models/table/group-actions-template.php'); ?>