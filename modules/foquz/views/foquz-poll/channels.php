<?php

use app\modules\foquz\assets\FoquzAsset;
use yii\helpers\Url;
use yii\helpers\Json;

if (!isset($senders['Email-names'])) {
  $senders['Email-names'] = [];
}

$asset = FoquzAsset::register($this);

$this->registerJs(
  "
  var POLL = " . Json::encode($poll) . "; 
  var EMAIL_SENDER_NAMES = " . Json::encode($senders['Email-names']) . ";
  var CHANNELS = " . Json::encode($channels) . ";
  var ICONS = " . Json::encode($icons) . ";
  var SENDER_NAMES = " . Json::encode($sender_names) . ";
  var SENDERS = " . Json::encode($senders) . ";",
  \yii\web\View::POS_HEAD
);

$this->registerJSFile("//editor.unlayer.com/embed.js");
$this->registerCSSFile('/js/poll.channels.css', ['depends' => [app\modules\foquz\assets\FoquzAsset::className()]]);

$this->registerJSFile('/js/poll.channels.js', ['depends' => [app\modules\foquz\assets\FoquzAsset::className()]]);

$this->title = $poll->name;
?>
<div class="poll-channels">
  <?= $this->render('_poll_header', ['page' => 'channels', 'model' => $poll]) ?>
  <?= $this->render('_menu_quests', ['page' => 'sender', 'model' => $poll]) ?>
  <div class="f-card f-card--shadow">
    <div class="f-tabs">
      <nav class="nav f-tabs__nav">
        <a class="nav-item nav-link  f-tabs__nav-item" href="<?= Url::to(['/foquz/foquz-poll/sender', 'id' => $poll->id]) ?>" aria-selected="true">
          Ссылка на опрос
        </a>
        <a class="nav-item nav-link f-tabs__nav-item active" href="<?= Url::to(['/foquz/foquz-poll/channels', 'id' => $poll->id]) ?>" aria-selected="true">
          Каналы связи
        </a>
        <?php if ($poll->is_auto) : ?>
          <a class="nav-item nav-link f-tabs__nav-item" href="<?= Url::to(['/foquz/foquz-poll/mailing-conditions', 'id' => $poll->id]) ?>" aria-selected="false">
            Фильтры рассылки
          </a>
        <?php else : ?>
          <?php if (!(Yii::$app->user->identity->isFilialEmployee())) : ?>
            <a class="nav-item nav-link f-tabs__nav-item" href="<?= Url::to(['/foquz/foquz-poll/mailings', 'id' => $poll->id]) ?>" aria-selected="false">
              Приглашения к опросу
            </a>
          <?php endif; ?>
        <?php endif; ?>
      <?php if (!$poll->is_auto && (Yii::$app->user->identity->isAdmin() || Yii::$app->user->identity->isEditor())) : ?>
          <a class="nav-item nav-link  f-tabs__nav-item" href="<?= Url::to(['/foquz/foquz-poll/widgets', 'id' => $poll->id]) ?>" aria-selected="false">
              Виджеты
          </a>
      <?php endif; ?>
      </nav>
      <div class="f-tabs__content" style="min-height: 530px">
        <div class="f-tab tab-pane show active">
          <!-- ko if: initializing -->
          <div class="f-card__inner align-items-center justify-content-center">
            <spinner></spinner>
          </div>
          <!-- /ko -->
          <!-- ko ifnot: initializing -->
          <div class="f-card__inner" style="display: none" data-bind="
              style: {
                display: initializing() ? 'none' : ''
              },
            ">
            <div class="f-card__section f-card__grow">
              <!-- ko if: !window.CURRENT_USER.watcher -->
              <div class="mb-25p f-color-service f-fs-1">
                Очерёдность использования каналов определяется их порядком в таблице.
                <!-- ko ifnot: blocked -->
                Используйте перетаскивание.
                <!-- /ko -->
              </div>
              <!-- /ko -->
              <div class="channels-by-invites" data-bind="let: {open: ko.observable(true)}">
                <h2 class="f-h2 pb-0 d-flex align-items-center cursor-pointer">
                  <span data-bind="
                      click: function() {
                        open(!open());
                      },
                    ">
                    Каналы отправки опроса по приглашениям
                  </span>
                  <fc-expander class="ml-10p" params="open: open"></fc-expander>
                </h2>
                <!-- ko template: {
                  foreach: templateIf(open(), $data),
                  afterAdd: slideAfterAddFactory(400),
                  beforeRemove: slideBeforeRemoveFactory(400),
                } -->
                <div class="channels-by-invites__mailings">
                  <fc-select params="
                      disabled: mailings().length === 0,
                      optionsForSearch: 0,
                      clearable: true,
                      options: mailings(),
                      value: mailingsValue,
                      placeholder: 'Настройки каналов по умолчанию',
                      onReachEnd: onReachEnd,
                      onSearch: onSearch,
                    "></fc-select>
                  <!-- ko template: {
                    foreach: templateIf(mailingsValue(), $data),
                    afterAdd: slideAfterAddFactory(400),
                    beforeRemove: slideBeforeRemoveFactory(400),
                  } -->
                  <fc-button params="
                      label: 'Просмотр приглашения',
                      color: 'primary',
                      mode: 'text',
                      linkMode: true,
                      linkAttrs: {
                        href: `./mailings?id=${poll.id}&mailing_id=${mailingsValue()}`,
                      },
                    " class="fc-btn"></fc-button>
                  <!-- /ko -->
                </div>
                <!-- ko if: currentMailing() -->
                <!-- ko ifnot: currentMailing().customChannelSettings() -->
                <div class="f-color-service f-fs-1 channels-by-invites__disclaimer" data-bind="
                    html: `
                      Для приглашения «<strong>${currentMailing().text}</strong>» используются настройки каналов связи по умолчанию.<br>Вы можете применить индивидуальные настройки, внеся изменения в таблицу ниже.
                    `,
                  "></div>
                <!-- /ko -->
                <!-- ko if: currentMailing().customChannelSettings() && !window.CURRENT_USER.watcher -->
                <fc-button params="
                    label: 'Сбросить до настроек по умолчанию',
                    color: 'primary',
                    mode: 'text',
                    icon: 'times',
                    click: function() {
                      clearCurrentMailingSettings();
                    },
                  " class="fc-btn custom-channel-settings__reset-button"></fc-button>
                <!-- /ko -->
                <!-- /ko -->
                <div class="mt-25p">
                  <div class="flex-table settings__conn-channels-table" data-bind="settingsConnChannelsTable: { type: 'before', onSort: onSortMailing }, disabled: blocked">
                    <div class="flex-table__head">
                      <div class="flex-table__head-cell settings__conn-channels-table-name-head-cell">Каналы</div>
                      <div class="flex-table__head-cell settings__conn-channels-table-delay-head-cell">Задержка</div>
                      <div class="flex-table__head-cell settings__conn-channels-table-repeat-head-cell">Повторы</div>
                      <div class="flex-table__head-cell settings__conn-channels-table-text-head-cell">Текст сообщения</div>
                      <div class="flex-table__head-cell settings__conn-channels-table-active-head-cell">Активность</div>
                    </div>
                    <div class="flex-table__body settings__conn-channels-table-body" data-bind="log">
                      <!-- ko foreach: orderedChannels -->
                      <div class="flex-table__row settings__conn-channels-table-row" data-bind="
                          attr: {
                            id: 'channel_id' + channel.channelType,
                            'data-channel-id': channel.channel.id,
                            'data-channel-name': channel.channel.name,
                          },
                          css: {
                            'settings__conn-channels-table-row--inactive': !channel.isActive(),
                            'settings__conn-channels-table-row--no-template': !channel.hasTemplate(),
                          },
                          click: function() {
                            if ($root.blocked && !channel.hasTemplate()) return false;
                            $parent.openChannel(channel);
                          },
                        ">
                        <div class="flex-table__cell settings__conn-channels-table-name-cell">
                          <span class="settings__conn-channels-table-name">
                            <svg-icon params="name: 'channel-' + channel.channelType" class="mr-2 svg-icon--lg" data-bind="class: 'f-color-channel-' + channel.channelType"></svg-icon>
                            <!-- ko text: channel.title -->
                            <!-- /ko -->
                          </span>
                        </div>
                        <div class="flex-table__cell settings__conn-channels-table-delay-cell">
                          <!-- ko text: channel.channel.getDelayText() || '—' -->
                          <!-- /ko -->
                        </div>
                        <div class="flex-table__cell settings__conn-channels-table-repeat-cell">
                          <span class="settings__conn-channels-table-repeat" data-bind="
                              css: {
                                'settings__conn-channels-table-repeat--zero': channel.channel.repeats().length == 0,
                              },
                            ">
                            <i class="icon icon-repeat mr-3" data-bind="
                                css: {
                                  'icon--inactive': !channel.isActive(),
                                },
                              "></i>
                            <span data-bind="
                                text: channel.channel.repeats().length,
                                css: {
                                  'font-weight-normal': channel.channel.repeats().length == 0,
                                },
                              "></span>
                          </span>
                        </div>
                        <div class="flex-table__cell settings__conn-channels-table-text-cell">
                          <!-- ko ifnot: channel.hasTemplate -->
                          <span class="bold">—</span>
                          <!-- /ko -->
                          <!-- ko if: channel.hasTemplate -->
                          <div class="text-ellipsis w-100" data-bind="text: channel.channel.getMessage()"></div>
                          <!-- /ko -->
                        </div>
                        <div class="flex-table__cell settings__conn-channels-table-active-cell">
                          <!-- ko if: channel.hasTemplate -->
                          <div class="mr-20p form-check settings__conn-channels-table-checkbox" data-bind="
                              click: function(_, e) {
                                e.stopPropagation();
                                return true;
                              },
                            ">
                            <input type="checkbox" class="form-check-input" id="isEmailActive" data-bind="
                                checked: channel.isActive,
                                attr: {
                                  disabled: channel.isDisabled,
                                  id: 'isActive-' + channel.channelType,
                                },
                              ">
                            <label class="form-check-label" data-bind="
                                attr: {
                                  'for': 'isActive-' + channel.channelType,
                                },
                                click: function() {
                                  if ($root.blocked) return false;
                                  $parent.checkActivate(channel);
                                },
                                disabled: $parent.blocked
                              "></label>
                          </div>
                          <!-- /ko -->
                          <!-- ko ifnot: channel.hasTemplate && $root.blocked -->
                          <button class="f-btn f-btn-text f-btn-primary" data-bind="disable: $root.blocked">
                            Настроить&nbsp;коннектор
                          </button>
                          <!-- /ko -->
                        </div>
                      </div>
                      <!-- /ko -->
                    </div>
                  </div>
                </div>
                <!-- /ko -->
              </div>
              <div class="mt-40p mb-40p" data-bind="let: {open: ko.observable(false)}">
                <h2 class="f-h2 pb-0 d-flex align-items-center cursor-pointer">
                  <span data-bind="
                      click: function() {
                        open(!open());
                      },
                    ">
                    Рассылка после опроса
                  </span>
                  <fc-expander class="ml-10p" params="open: open"></fc-expander>
                </h2>
                <!-- ko template: {
                  foreach: templateIf(open(), $data),
                  afterAdd: slideAfterAddFactory(400),
                  beforeRemove: slideBeforeRemoveFactory(400),
                } -->
                <div class="mt-25p">
                  <div class="row">
                    <div class="col col-6">
                      <div class="form-group">
                        <fc-label params="text: 'Триггер', hint: 'Триггер'"></fc-label>
                        <fc-select params="
                            options: passedMailing.triggers,
                            value: passedMailing.trigger,
                            clearable: true,
                            placeholder: 'Выберите триггер',
                            disabled: blocked
                          "></fc-select>
                      </div>
                    </div>
                    <!-- ko template: {
                      foreach: templateIf(passedMailing.trigger(), $data),
                    } -->
                    <div class="col col-6">
                      <fc-label params="
                          text: 'Отправить сообщение через',
                          hint: 'Отправить сообщение через',
                        "></fc-label>
                      <div class="d-flex">
                        <!-- ko component: {
                          name: 'delay-group',
                          params: {
                            days: passedMailing.delayDays,
                            time: passedMailing.delayTime,
                            errorMatcher: passedMailing.errorMatcher,
                            successMatcher: passedMailing.successMatcher,
                            short: true,
                            disabled: blocked
                          }
                        } -->
                        <!-- /ko -->
                        <!-- ko if: !window.CURRENT_USER.watcher -->
                        <fc-button class="ml-30p" params="
                            click: function() {
                              passedMailing.submit();
                            },
                            icon: {
                              name: 'check',
                            },
                            color: 'success',
                            size: 'xl',
                            pending: passedMailing.pending,
                            disabled: blocked
                          "></fc-button>
                        <!-- /ko -->
                      </div>
                    </div>
                    <!-- /ko -->
                    <!-- ko template: {
                      foreach: templateIf(!passedMailing.trigger(), $data),
                    } -->
                      <!-- ko if: !window.CURRENT_USER.watcher -->
                      <div class="col col-6">
                        <fc-label></fc-label>
                        <fc-button params="
                            click: function() {
                              passedMailing.submit();
                            },
                            icon: {
                              name: 'check',
                            },
                            pending: passedMailing.pending,
                            color: 'success',
                            size: 'xl',
                            disabled: blocked
                          "></fc-button>
                      </div>
                      <!-- /ko -->
                    <!-- /ko -->
                  </div>
                  <div class="flex-table settings__conn-channels-table mt-1" data-bind="settingsConnChannelsTable, disabled: blocked">
                    <div class="flex-table__head">
                      <div class="flex-table__head-cell settings__conn-channels-table-name-head-cell">Каналы</div>
                      <div class="flex-table__head-cell settings__conn-channels-table-text-head-cell">Текст сообщения</div>
                      <div class="flex-table__head-cell settings__conn-channels-table-active-head-cell">Активность</div>
                    </div>
                    <div class="flex-table__body settings__conn-channels-table-body">
                      <!-- ko foreach: passedMailing.orderedChannels -->
                      <div class="flex-table__row settings__conn-channels-table-row" data-bind="
                          attr: {
                            id: 'channel_id' + channel.channelType, 
                            'data-channel-id': channel.channel.id,
                          },
                          css: {
                            'settings__conn-channels-table-row--inactive': !channel.isActive(),
                            'settings__conn-channels-table-row--no-template': !channel.hasTemplate(),
                          },
                          click: function() {
                            if ($root.blocked && !channel.hasTemplate()) {return false;}
                            $parent.passedMailing.openChannel(channel);
                          },
                        ">
                        <div class="flex-table__cell settings__conn-channels-table-name-cell">
                          <span class="settings__conn-channels-table-name">
                            <svg-icon params="name: 'channel-' + channel.channelType" class="mr-2 svg-icon--lg" data-bind="class: 'f-color-channel-' + channel.channelType"></svg-icon>
                            <!-- ko text: channel.title -->
                            <!-- /ko -->
                          </span>
                        </div>
                        <div class="flex-table__cell settings__conn-channels-table-text-cell">
                          <!-- ko ifnot: channel.hasTemplate -->
                          <span class="bold">—</span>
                          <!-- /ko -->
                          <!-- ko if: channel.hasTemplate -->
                          <div class="text-ellipsis w-100" data-bind="text: channel.channel.getMessage()"></div>
                          <!-- /ko -->
                        </div>
                        <div class="flex-table__cell settings__conn-channels-table-active-cell">
                          <!-- ko if: channel.hasTemplate -->
                          <div class="mr-20p form-check settings__conn-channels-table-checkbox" data-bind="
                              click: function(_, e) {
                                e.stopPropagation();
                                return true;
                              },
                            ">
                            <input type="checkbox" class="form-check-input" id="isEmailActive" data-bind="
                                checked: channel.isActive,
                                attr: {
                                  disabled: channel.isDisabled,
                                  id: 'isActive-' + channel.channelType,
                                },
                              ">
                            <label class="form-check-label" data-bind="
                                attr: {
                                  'for': 'isActive-' + channel.channelType,
                                },
                                click: function() {
                                  if ($root.blocked) return false;
                                  $parent.passedMailing.checkActivate(channel);
                                },
                              "></label>
                          </div>
                          <!-- /ko -->
                          <!-- ko ifnot: channel.hasTemplate && $root.blocked -->
                          <button class="f-btn f-btn-text f-btn-primary" data-bind="disable: $root.blocked">
                            Настроить&nbsp;коннектор
                          </button>
                          <!-- /ko -->
                        </div>
                      </div>
                      <!-- /ko -->
                    </div>
                  </div>
                </div>
                <!-- /ko -->
              </div>
            </div>
            <div class="f-card__footer <?= isset($toQuestionsSettingsActive) && $toQuestionsSettingsActive ? '' : 'd-none' ?>">
              <div class="settings__actions">
                <div class="spacer"></div>
                <a id="to-questions-settings" class="btn btn-default btn-with-icon settings__next-button" href="<?= Url::to($poll->getViewUrl()) ?>" style="color: #73808d;text-decoration: none;">
                  Настроить вопросы
                </a>
              </div>
            </div>
            <div class="success-message-wrapper">
              <fc-success params="
                  show: passedMailing.showSuccessMessage,
                  text: 'Настройки рассылки после опроса успешно сохранены',
                "></fc-success>
            </div>
          </div>
          <!-- /ko -->
        </div>
      </div>
    </div>
  </div>
  <!-- ko component: { name: 'modal-container', params: { opens: modalOpens } } -->
  <!-- /ko -->
  <dialogs-container params="ref: sidesheets"></dialogs-container>
  <?= $this->render('@app/ko/legacy/models/channels/template.php'); ?>
</div>
