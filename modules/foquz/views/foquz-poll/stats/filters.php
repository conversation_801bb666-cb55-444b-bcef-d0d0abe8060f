<!-- Фильтры -->
<!-- ko ifnot: deleted -->
<div class="poll-stats__filters f-card__section f-card__divider no-print">
  <div class="poll-stats-filters">
    <div class="form-group dense-form-group">
      <label class="form-label">Период</label>
      <period-picker params="value: periodFilter, autosize: true, allowClear: true" data-bind="css: {
                        'is-invalid':(!periodErrorStateMatcher() && isSubmittedFilters()) || havePeriodError() && isSubmittedFilters(),
                        }"></period-picker>
      <!-- ko if: (!periodErrorStateMatcher(periodFilter) && isSubmittedFilters()) || havePeriodError() && isSubmittedFilters() -->
      <div class="form-error" data-bind="text: getPeriodError()"></div>
      <!-- /ko -->
    </div>

    <div class="form-group dense-form-group">
      <label class="form-label"><?= \Yii::t('main', 'Статус анкеты') ?></label>

      <select multiple data-bind="
        selectedOptions: questionnaires,
        valueAllowUnset: true,
        lazySelect2: {
            wrapperCssClass: 'select2-container--form-control',
            dropdownCssClass: 'dense-form-group__dropdown',
            containerCss: { 'min-width': '80px' },
        }
      " data-placeholder="<?= \Yii::t('main', 'Все анкеты') ?>">
          <option value="done"><?= \Yii::t('answers', 'Заполнена') ?></option>
          <option value="in-progress"><?= \Yii::t('answers', 'В процессе') ?></option>
          <option value="quote-full"><?= \Yii::t('answers', 'Квотафул') ?></option>
          <option value="screen-out"><?= \Yii::t('answers', 'Скринаут') ?></option>
      </select>
    </div>

    <tags-filter params="tags: tags, operation: tagsOperation, disabled: !isContactsEnabled"></tags-filter>

    <div class="form-group dense-form-group">
      <label class="form-label">Устройство</label>
      <select data-bind="
                        selectedOptions: devices,

                        valueAllowUnset: true,
                        select2: {
                            wrapperCssClass: 'select2-container--form-control foquz-select2',
                            dropdownCssClass: 'dense-form-group__dropdown',
                            containerCss: { 'min-width': '80px' },
                        }" data-placeholder="Все" multiple>

        <option value="desktop">Десктоп</option>
        <option value="tablet">Планшет</option>
        <option value="mobile">Смартфон</option>
      </select>
    </div>

    <!-- ko if: hasFilials -->
    <div class="form-group dense-form-group">
      <label class="form-label">Филиал</label>

      <div>
        <fc-select class="categorized" params="inline: true, 
        options: filialsList, 
        value: filials, 
        placeholder: 'Все филиалы', 
        multiple: true, 
        blockSelectedGroup: true"></fc-select>
      </div>
    </div>
    <!-- /ko -->

    <!-- ko if: hasLinksWithQuotas -->
    <div class="form-group dense-form-group">
      <label class="form-label">Ссылки</label>

      <div>
        <fc-select class="categorized" params="inline: true, 
        options: linksList, 
        value: links, 
        placeholder: 'Все ссылки', 
        multiple: true, 
        blockSelectedGroup: true"></fc-select>
      </div>
    </div>
    <!-- /ko -->

    <!-- ko if: !questions().some(item => item.type == 1) -->
    <div class="no-print poll-stats-filters__actions ml-auto">

      <button class="f-btn f-btn-link d-none d-md-flex mr-3" type="button" data-bind="
                            click: function() {
                                resetFilters();
                            }">
          Сбросить
        </button>

      <button class="f-btn d-md-none" type="button" data-bind="
        click: function() {
            resetFilters();
        }">
        <span class="f-btn-prepend">
          <svg-icon params="name: 'bin'"></svg-icon>
        </span>
        Сбросить
      </button>
      <button class="f-btn f-btn-success" type="button" data-bind="
                            disable: !periodErrorStateMatcher() || isAjaxSended() || (havePeriodError() && isSubmittedFilters),
                            css: {
                                'f-btn--loading': isAjaxSended()
                            },
                            click:  function(_,event) {
                                event.preventDefault();
                                submitFilters();
                            }">
        <foquz-icon class="f-btn-prepend" params="icon: 'check'"></foquz-icon>
        Применить
      </button>
    </div>
    <!-- /ko -->
  </div>
  <!-- ko if: questions().some(item => item.type == 1) -->
  <!-- ko if: variantsFilters().length -->
  <div class="mt-2">
    <!-- ko foreach: variantsFilters() -->
    <div class="d-flex align-items-top mb-2">
      <fc-check class="variants-filter-question-check" params="checked: $data.checked, disabled: false"></fc-check>
      <div class="d-flex variants-filter-question-additional" data-bind="style: {opacity: $data.checked() ? 1 : 0.5}">
        <div data-bind="text: $data.questions.find(i => i.id == $data.value()).position + '.&nbsp;'" class="variants-filter-question-description"></div>
        <div data-bind="text: $data.questions.find(i => i.id == $data.value()).description, tooltip, tooltipPlacement: 'top', tooltipText: $data.questions.find(i => i.id == $data.value()).description.length > 24 ? $data.questions.find(i => i.id == $data.value()).description : ''" class="variants-filter-question-description"></div>
        <div class="variants-filter-question-description">&nbsp;/&nbsp;</div>
        
        <div data-bind="text: $parent.variantFilterActions[$data.action() - 1], style: {color: ($data.action() == 1 || $data.action() == 2) ? '#73808D' : '#F96261'}"></div>
        <!-- ko if: $data.action() == 1 || $data.action() == 2 -->
          <div>:&nbsp;&nbsp;</div>
          <!-- ko foreach: $data.answers() -->
          <div data-bind="text: $parent.variants().find(v => v.id == $data)?.text() || $parent.questions.find(q => q.id == $parent.value()).self_variant_text || $parent.questions.find(i => i.id == $parent.value())?.detail_answers.find(i => i.question_detail_id == $data)?.question || 'Свой вариант', tooltip, tooltipPlacement: 'top', tooltipText: ($parent.variants().find(v => v.id == $data)?.text() || $parent.questions.find(q => q.id == $parent.value()).self_variant_text || $parent.questions.find(i => i.id == $parent.value())?.detail_answers.find(i => i.question_detail_id == $data)?.question || []).length > 24 ? $parent.variants().find(v => v.id == $data)?.text() || $parent.questions.find(q => q.id == $parent.value()).self_variant_text || $parent.questions.find(i => i.id == $parent.value())?.detail_answers.find(i => i.question_detail_id == $data)?.question : ''" class="variants-filter-question-answer"></div>
          <!-- ko if: $parent.answers().length !== $index() + 1 -->
          <div>/&nbsp;</div>
          <!-- /ko -->
          <!-- /ko -->
        <!-- /ko -->
      </div>

    </div>
    <!-- /ko -->
  </div>
    
  <!-- /ko -->
  <div class="d-flex align-items-center mt-4">
      <button class="f-btn f-btn-link d-none d-md-flex align-items-center mr-10p" type="button" data-bind="
                            click: function() {
                                $root.openVariantsFilter()
                            }">
        Настроить условия по ответам
        <span>
          <button class="btn-question ml-0" data-bind="tooltip, tooltipPlacement: 'top', tooltipText: 'Условия работают через «И», выбранные варианты ответов внутри условия — через «ИЛИ».'" type="button" tabindex="10">
          </button>
        </span>
        
      </button>

    <div class="no-print poll-stats-filters__actions ml-auto">

      <button class="f-btn f-btn-link d-none d-md-flex mr-3" type="button" data-bind="
                            click: function() {
                                resetFilters();
                            }">
          Сбросить
        </button>

      <button class="f-btn d-md-none" type="button" data-bind="
        click: function() {
            resetFilters();
        }">
        <span class="f-btn-prepend">
          <svg-icon params="name: 'bin'"></svg-icon>
        </span>
        Сбросить
      </button>
      <button class="f-btn f-btn-success" type="button" data-bind="
                            disable: !periodErrorStateMatcher() || isAjaxSended() || (havePeriodError() && isSubmittedFilters),
                            css: {
                                'f-btn--loading': isAjaxSended()
                            },
                            click:  function(_,event) {
                                event.preventDefault();
                                submitFilters();
                            }">
        <foquz-icon class="f-btn-prepend" params="icon: 'check'"></foquz-icon>
        Применить
      </button>
    </div>
  </div>
  <!-- /ko -->

  <div class="poll-stats-filters-toggler">
    <button class="f-btn f-btn-link" type="button" data-bind="click: function() {
          $('.poll-stats-filters').slideUp(300).attr('data-closed', true);
        }" data-close>Свернуть фильтры</button>
    <button class="f-btn f-btn-link" type="button" data-bind="click: function() {
          $('.poll-stats-filters').slideDown(300).removeAttr('data-closed');
        }" data-open>Развернуть фильтры</button>
  </div>
</div>
<!-- /ko -->
<!-- /Фильтры -->

<!-- Сохраненные фильтры (для печати) -->
<!-- ko ifnot: deleted -->
<!-- ko if: hasSavedFilters -->
<div class="poll-stats__filters f-card__divider only-print" style="display: none">
  <div class="mx-20p">
    <stats-filters-value class="py-15p" params="filters: $root.getSavedSearchParams()"></stats-filters-value>
  </div>
</div>
<!-- /ko -->
<!-- /ko -->
<!-- /Сохраненные фильтры (для печати) -->
<!-- Фильтры по вариантам для (для печати) -->
<!-- ko if: variantsFilters().some(i => i.checked()) -->
<div class="poll-stats__filters f-card__divider only-print" style="display: none">
  <div class="mx-20p">
    <!-- ko foreach: variantsFilters().filter(i => i.checked()) -->
    <div class="d-flex align-items-top mb-10p">
      <div class="d-flex variants-filter-question-additional" data-bind="style: {opacity: $data.checked() ? 1 : 0.5}">
        <div data-bind="text: $data.questions.find(i => i.id == $data.value()).position + '.&nbsp;'" class="variants-filter-question-description"></div>
        <div data-bind="text: $data.questions.find(i => i.id == $data.value()).description, tooltip, tooltipPlacement: 'top', tooltipText: $data.questions.find(i => i.id == $data.value()).description.length > 24 ? $data.questions.find(i => i.id == $data.value()).description : ''" class="variants-filter-question-description"></div>
        <div class="variants-filter-question-description">&nbsp;/&nbsp;</div>
        
        <div data-bind="text: $parent.variantFilterActions[$data.action() - 1], style: {color: ($data.action() == 1 || $data.action() == 2) ? '#73808D' : '#F96261'}"></div>
        <!-- ko if: $data.action() == 1 || $data.action() == 2 -->
          <div>:&nbsp;&nbsp;</div>
          <!-- ko foreach: $data.answers() -->
          <div data-bind="text: $parent.variants().find(v => v.id == $data)?.text() || $parent.questions.find(q => q.id == $parent.value()).self_variant_text || $parent.questions.find(i => i.id == $parent.value())?.detail_answers.find(i => i.question_detail_id == $data)?.question || 'Свой вариант', tooltip, tooltipPlacement: 'top', tooltipText: ($parent.variants().find(v => v.id == $data)?.text() || $parent.questions.find(q => q.id == $parent.value()).self_variant_text || $parent.questions.find(i => i.id == $parent.value())?.detail_answers.find(i => i.question_detail_id == $data)?.question || []).length > 24 ? $parent.variants().find(v => v.id == $data)?.text() || $parent.questions.find(q => q.id == $parent.value()).self_variant_text || $parent.questions.find(i => i.id == $parent.value())?.detail_answers.find(i => i.question_detail_id == $data)?.question : ''" class="variants-filter-question-answer"></div>
          <!-- ko if: $parent.answers().length !== $index() + 1 -->
          <div>/&nbsp;</div>
          <!-- /ko -->
          <!-- /ko -->
        <!-- /ko -->
      </div>

    </div>
    <!-- /ko -->
  </div>
</div>
<!-- /ko -->
<!-- Фильтры по вариантам для (для печати) -->