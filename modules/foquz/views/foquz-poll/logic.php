<?php

use yii\helpers\Url;
use app\modules\foquz\assets\FoquzAsset;

$asset = FoquzAsset::register($this);

$this->title = \Yii::t('questions', 'Логика опроса');
$logicLink = Url::to(['/foquz/foquz-poll/logic', 'id' => $_GET['id']], 'https');

$this->registerJs("
    var QUESTIONS = " . \yii\helpers\Json::encode($questions) . ";
    var POLL = " . \yii\helpers\Json::encode($poll) . ";
    var DESIGN = " . \yii\helpers\Json::encode($poll->design) . ";
", $this::POS_HEAD);

$this->registerCSSFile('/js/poll.logic.css', ['depends' => [app\modules\foquz\assets\FoquzAsset::className()]]);

$this->registerJSFile('/js/poll.logic.js', ['depends' => [app\modules\foquz\assets\FoquzAsset::className()]]);

?>

<div class="logic initializing" data-bind="childrenComplete: $root.onInit.bind($root), css: { 'initializing': initializing(), 'logic--blocked': blocked }">

    <?= $this->render('_poll_header', ['page' => 'logic', 'model' => $poll]) ?>

    <?= $this->render('_menu_quests', ['page' => 'questions', 'model' => $poll]) ?>

    <div class="d-flex justify-content-between align-items-start app__inner-content" style="margin-bottom: 15px; margin-top: -12px;">



        <question-form-paginator
            params="
                poll: poll,
                questions: questions,
                isAuto: poll.isAuto,
                hasLogic: hasLogic,
                randomOrder: randomOrder,
                hasEndLogic: hasEndLogic,
                activateQuestion: function(id) {
                    window.location = '/foquz/foquz-question/update?id=' + id + '&pollId=' + poll.id
                },
                blinked: blinked,
                pagesMode: pagesMode,
                pages: pages,
                isSortableEnabled: false,
            "
        >

            <?php if (!$poll->is_auto && !(Yii::$app->user->identity->isFilialEmployee())) : ?>
                <!-- ko if: pagesMode() && pages().length > 1 -->
                <fc-button params="shape: 'circle', color: 'primary', icon: 'plus', size: 'sm'" id="add-question-button"></fc-button>

                <fc-popper params="target: 'add-question-button'">
                    <div class="fc-dropdown-list">
                        <!-- ko foreach: $root.pages -->
                        <a class="fc-dropdown-list__item" href="javascript:void(0)" data-bind="click: function () {
                                $root.addQuestion(id);
                            }, text: name() || $root.translator.t('Название страницы {number}', {
                                number: $index() + 1
                            })">

                        </a>
                        <!-- /ko -->
                    </div>
                </fc-popper>
                <!-- /ko -->

                <!-- ko if: !window.CURRENT_USER.watcher && (!pagesMode() || pages().length <=1)  -->
                <fc-button params="shape: 'circle',
                            size: 'sm',
                            color: 'primary',
                            icon: { name: 'plus', size: 12 },
                            click: function () { addQuestion(); }" id="add-question-button" data-bind="fbTooltip: 'Добавить вопрос'">
                </fc-button>
                <!-- /ko -->


            <?php endif; ?>
        </question-form-paginator>



        <div class="d-flex align-items-center ml-4 flex-shrink-0">
            <span
                data-bind="
                    tooltip,
                    tooltipText: logicButtonTooltipText
                "
                class="f-btn f-btn--square p-0 disabled flex-shrink-0 mr-10p"
            >
                <svg-icon params="name: 'logic'" class="f-color-service svg-icon--lg" style="opacity: 0.5"></svg-icon>

                <!-- ko if: hasAnyLogic -->
                    <div class="f-badge f-badge--success">
                        <svg-icon params="name: 'check-bold'" class="f-color-white svg-icon--xs"></svg-icon>
                    </div>
                <!-- /ko -->
            </span>

            <?php if (!$poll->is_auto && $poll->point_system) : ?>
                <fc-button
                    params="
                        shape: 'square',
                        icon: 'ellipsis',
                    "
                    id="sg-dropdown-button"
                ></fc-button>
                <fc-popper
                    params="
                        target: 'sg-dropdown-button',
                        options: { placement: 'bottom-start' }
                    "
                >
                    <div class="fc-dropdown-list">
                        <?php if (!$poll->is_auto) : ?>
                            <a href="javascript:void(0)" data-bind="click: function() { copyQuestions() }" class="fc-dropdown-list__item">
                                <fc-icon params="name: 'poll-copy', color: 'secondary'" class="mr-3"></fc-icon>

                                <?= \Yii::t('questions', 'Копировать вопросы из других опросов') ?>
                            </a>
                        <?php endif; ?>
                        <?php if ($poll->point_system) : ?>

                            <a href="javascript:void(0)" data-bind="click: function() { configPoints() }" class="fc-dropdown-list__item">
                                <fc-icon params="name: 'poll-points', color: 'secondary'" class="mr-3"></fc-icon>

                                <?= \Yii::t('questions', 'Интерпретация баллов') ?>

                            </a>
                        <?php endif; ?>
                    </div>
                </fc-popper>
            <?php endif; ?>
        </div>
    </div>

    <div class="f-card f-card--shadow f-card--lg f-card--min-height">

        <!-- ko ifnot: questions().length -->
            <div class="f-card__inner align-items-center justify-content-center pa-40p">
                <div class="text-center f-color-service" style="max-width: 600px">
                    <?= \Yii::t('questions', 'В разделе «Логика» вы можете изменить порядок вопросов, распределить по страницам и перенаправить респондентов на разные вопросы в зависимости от выбранного ответа.') ?>
                    <br>
                    <?= \Yii::t('questions', 'Для настройки логики опроса необходимо добавить хотя бы один вопрос.') ?>
                    <br>
                    <a href="/foquz/foquz-question/update?pollId=<?= $poll->id ?>&id=<?= $questions[0]['id'] ?>" class="f-btn mt-4">
                        <?= \Yii::t('questions', 'Вернуться к добавлению вопросов') ?>
                    </a>
                </div>
            </div>
        <!-- /ko -->

        <!-- ko if: questions().length -->
        <div class="f-card__section">
            <div class="f-fs-1 f-color-service">

                <?= \Yii::t('questions', 'В разделе «Логика» вы можете изменить порядок вопросов, распределить по страницам и перенаправить респондентов на разные вопросы в зависимости от выбранного ответа.') ?>
                <div class="f-color-danger">
                    <?= \Yii::t('questions', 'Если для вопросов настроена логика или отображение, то порядок вопросов менять нельзя. Если включена опция «Случайный порядок страниц», логику настроить нельзя.') ?>
                </div>
            </div>
        </div>
        <div class="f-card__grow pb-4">
            <div class="f-card__section pt-5p pb-0">
                <div class="d-flex flex-wrap justify-content-between  align-items-end">
                    <div class=" pb-10p mr-4">
                        <label class="form-label"><?= \Yii::t('questions', 'Отображение вопросов') ?></label>

                        <div class="d-flex" data-bind="click: function() {
                            if ($root.hasRecipients) {
                                $root.showRecipientsModal('display');
                                return false;
                            }
                            return true;
                        }">
                            <select2 params="value: displayType, allowUnset: true, disabled: $root.blocked || $root.hasRecipients" style="width: 477px">
                                <option value="1"><?= \Yii::t('questions', 'Каждый вопрос на отдельной странице') ?></option>
                                <option value="2"><?= \Yii::t('questions', 'Разделить вопросы по страницам вручную') ?></option>
                            </select2>
                            <!-- ko ifnot: window.CURRENT_USER.watcher -->
                            <button class="f-btn f-btn-success f-btn-check ml-15p" data-bind="click: function() {
                                saveDisplaySettings()
                            }, disable: displayType() == savedDisplay() || $root.blocked || $root.tmpBlocked() || $root.hasRecipients">
                                <svg-icon params="name: 'check'"></svg-icon>
                            </button>
                            <!-- /ko -->
                        </div>
                    </div>
                    <div class="d-flex align-items-center  pb-10p">
                        <div
                            data-bind="
                                tooltip,
                                tooltipText: !randomOrder() && hasLogic()
                                    ? $root.translator.t('Если к вопросу из списка добавлено хотя бы одно логическое условие, то случайный порядок страниц для опроса настроить нельзя.')
                                    : '',
                                click: function() {
                                    if ($root.hasRecipients) {
                                        $root.showRecipientsModal('random');
                                        return false;
                                    }
                                    return true;
                                }
                            "
                        >
                            <switch
                                class="mb-2"
                                params="
                                    checked: randomOrder,
                                    onChange: function() {
                                        return toggleRandomOrder();
                                    },
                                    disabled: (!randomOrder() && hasLogic()) || $root.blocked || $root.hasRecipients,
                                "
                            ><?= \Yii::t('questions', 'Случайный порядок страниц') ?></switch>
                        </div>

                        <!-- ko if: pagesMode() && !$root.blocked -->
                        <button class="f-btn f-btn-primary ml-30p" data-bind="click: function() {
                            addPage();

                        }"><?= \Yii::t('questions', 'Добавить страницу') ?></button>
                        <!-- /ko -->
                    </div>
                </div>
                <div class="pb-30p f-fs-1 f-color-service">
                    <span data-bind="text: $root.translator.t('Запрет на случайный порядок установлен для страниц: '), visible: $root.randomOrder() && lockedEntities() !==0"></span>
                    <span class="font-weight-bold" data-bind="text: lockedEntities(), visible: $root.randomOrder() && lockedEntities() !==0"></span>
                </div>
            </div>


            <div
                class="logic-list"
                data-bind="css: {
                    'logic-list--mode_pages': pagesMode,
                    'logic-list--has-logic': hasLogic,
                    'logic-list--blocked': blocked,
                }"
            >

                <!-- ko if: startQuestions().length -->
                    <div
                        class="logic-sublist"
                        data-bind="css: { 'logic-sublist--pages': pagesMode }"
                    >
                        <!-- ko foreach: { data: startQuestions, as: 'question' } -->
                            <!-- ko template: {
                                name: 'question-template'
                            } -->
                            <!-- /ko -->
                        <!-- /ko -->
                    </div>
                <!-- /ko -->

                <!-- ko if: pagesMode -->
                    <div
                        class="logic-sublist"
                        style="margin-top: -1px;"
                        data-bind="
                            foquzSortable: {
                                data: pages,
                                as: 'page',
                                connectClass: false,
                                afterMove: function() { resortPages() },
                                options: { 'handle': '.logic-page__drag' }
                            }
                        "
                    >


                        <section
                            class="logic-page"
                            data-bind="
                                element: page.element,
                                css: { 'logic-page--empty': page.questions().length == 0 }
                            "
                        >
                            <header
                                class="logic-page__header"
                                data-bind="let: { editing: ko.observable(false) }"
                            >
                                <!-- ko if: !$root.hasLogic() && !$root.blocked -->
                                    <div
                                        class="logic-page__drag"
                                        draggable="true"
                                        data-bind="attr: { disabled: $root.pages().length < 2 }"
                                    >
                                        <i class="icon icon-drag-arrow--light"></i>
                                    </div>
                                <!-- /ko -->
                                <!-- ko if: $root.hasLogic() || $root.blocked -->
                                    <div class="logic-page__drag"></div>
                                <!-- /ko -->

                                <div class="logic-page__index" data-bind="text: ($index() + 1) + '.'"></div>

                                <div class="flex-grow-1 overflow-hidden pr-4">
                                    <!-- ko if: editing -->
                                        <edit-form
                                            params="
                                                value: page.name,
                                                placeholder: $root.translator.t('Название страницы'),
                                                allowEmpty: true,
                                                onSave: function(newName) {
                                                    page.updateName(newName);
                                                    editing(false);
                                                },
                                                onCancel: function() { editing(false) },
                                            "
                                        ></edit-form>
                                    <!-- /ko -->

                                    <!-- ko ifnot: editing -->
                                        <div
                                            class="logic-page__name"
                                            data-bind="
                                                text: page.name()
                                                    || $root.translator.t('Название страницы {number}', { number: $index() + 1 }),
                                                style: {
                                                    'font-style': page.name() ? 'normal' : 'italic',
                                                    'font-weight': page.name() ? 500 : 400,
                                                }
                                            "
                                        ></div>
                                    <!-- /ko -->
                                </div>

                                <div class="d-flex f-color-service hide-on-dragging f-fs-0">
                                    <button
                                        class="button-ghost mr-4 lock-shuffle"
                                        data-bind="
                                            click: function() {
                                                if (!$root.blocked) page.toggleLocked()
                                            },
                                            disable: page.hasLogic() && page.locked(),
                                            visible: $root.randomOrder(),
                                            css: { locked: page.locked() },
                                            tooltip,
                                            tooltipText: !page.locked()
                                                ? $root.translator.t('Зафиксировать расположение страницы')
                                                : page.hasLogic()
                                                    ? $root.translator.t('Если для вопроса на странице добавлено хотя бы одно логическое условие, то отключить фиксацию этой страницы нельзя')
                                                    : $root.translator.t('Страница зафиксирована'),
                                        "
                                        type="button"
                                    >
                                        <fc-icon params="name: 'lock-shuffle', width: 16, height: 20"></fc-icon>
                                    </button>

                                    <button
                                        class="button-ghost f-color-service"
                                        data-bind="
                                            click: function() {editing(true)},
                                            disable: editing() || $root.blocked,
                                            tooltip,
                                            tooltipText: $root.translator.t('Редактировать название')
                                        "
                                        title=""
                                    >
                                        <svg-icon params="name: 'pencil'"></svg-icon>
                                    </button>

                                    <button
                                        class="ml-4 button-ghost f-color-service"
                                        data-bind="
                                            click: function() { page.toggleRandomOrder() },
                                            enable: page.questions().length > 1
                                                && !$root.blocked
                                                && !page.hasLogic(),
                                            css: {
                                                'f-color-primary': page.randomOrder(),
                                                'f-color-service': !page.randomOrder()
                                            },
                                            tooltip,
                                            tooltipText: page.randomOrder()
                                                ? $root.translator.t('Выключить случайный порядок вопросов на странице')
                                                : page.hasLogic()
                                                    ? $root.translator.t('Если для вопроса на странице добавлено хотя бы одно логическое условие, то случайный порядок вопросов для этой страницы настроить нельзя')
                                                    : $root.translator.t('Включить случайный порядок вопросов на странице'),
                                        "
                                    >
                                        <svg-icon params="name: 'shuffle'"></svg-icon>
                                    </button>
                                    <button
                                        class="ml-4 button-ghost f-color-service"
                                        data-bind="
                                            click: function() { $root.removePage(page) },
                                            tooltip,
                                            tooltipText: $root.translator.t('Удалить страницу'),
                                            enable: $root.pagesWithQuestions().length > 1
                                                && !$root.blocked,
                                        "
                                    >
                                        <svg-icon params="name: 'bin'"></svg-icon>
                                    </button>
                                </div>
                            </header>
                            
                            <!-- ko let: { currentPage: page } -->
                                <div class="logic-page__questions hide-on-dragging ">
                                    <div
                                        class="logic-page__list logic-sublist"
                                        data-bind="
                                            foquzSortable: {
                                                data: page.questions,
                                                as: 'pageQuestion',
                                                connectClass: 'logic-page__list',

                                                afterMove: function(q, e) {
                                                    $root.moveQuestionToPage(q.item, page.id);
                                                },

                                                options: {
                                                    'handle': '.logic-item__drag',
                                                    forceHelperSize: true,
                                                    forcePlaceholderSize: true,
                                                    scrollSensitivity: 10,
                                                    tolerance: 'pointer'
                                                },
                                            },
                                            event: {
                                                'sortover': function(page, event, ui) {            
                                                    $root.onSortOver(page, ui.item, ui.helper)
                                                    return true;
                                                }
                                            }
                                        "
                                    >
                                        <question-logic params="
                                            blocked: $root.blocked,
                                            isWatcher: $root.isWatcher,
                                            canMove: !$root.blocked,
                                            randomOrder: $root.randomOrder,
                                            pageRandomOrder: currentPage.randomOrder,
                                            pageLocked: currentPage.locked,
                                            question: $root.getQuestion(pageQuestion.id),
                                            pagesMode: $root.pagesMode,
                                            formControlErrorStateMatcher: $root.formControlErrorStateMatcher,
                                        "></question-logic>
                                    </div>
                                </div>
                            <!-- /ko -->
                    </div>
                <!-- /ko -->



                <!-- ko ifnot: pagesMode -->
                <!-- ko if: defaultQuestions().length -->
                <div data-bind="css: {
                            'sort-error': $root.donorOrderError
                        }">
                    <div class="logic-sublist" data-bind="foquzSortable: {
                        data: defaultQuestions,
                        as: 'question',
                        connectClass: false,
                        template: 'question-template',
                        options: {
                            'handle': '.logic-item__drag',
                            'change': function(event, ui) {
                                $root.checkDonors(event, ui);
                            },
                            'stop': function(event, ui) {
                                $root.onStopSorting(event, ui);
                            }
                        } },
                        ">


                    </div>
                </div>
                <!-- /ko -->
                <!-- /ko -->



                <!-- ko if: endQuestions().length -->
                <div class="logic-sublist" data-bind="foquzSortable: {
                    data: endQuestions,
                    as: 'question',
                    connectClass: false,
                    template: 'question-template',
                    afterMove: function() {
                        resort();
                    },
                    options: {
                        'handle': '.logic-item__drag'
                    } },
                    css: {
                        'logic-sublist--pages': pagesMode
                    }">


                </div>
                <!-- /ko -->
            </div>




        </div>
        <!-- ko ifnot: blocked -->
        <footer class="f-card__footer" data-bind="stickyFooter">
            <!-- ko component: { name: 'saved-message', params: {
                show: isSavedMessageShown
            } } -->
            <!-- /ko -->
            <div class="d-flex justify-content-between">
                <div>
                    <fc-button params="label: _t('Очистить логику'), color: 'danger', icon: { 
                        name: 'times',
                        size: 12
                    }, pending: clearLogicPenging, click: function() { clearLogic() }" />
                </div>

                <div class="d-flex">
                    <button class="f-btn" type="button" data-bind="click: reset">
                        <span class="f-btn-prepend">
                            <svg-icon params="name: 'bin'"></svg-icon>
                        </span>
                        <?= \Yii::t('main', 'Отменить') ?>
                    </button>
                    <button class="f-btn f-btn-success" type="button" data-bind="click: save">
                        <span class="f-btn-prepend">
                            <svg-icon params="name: 'save'"></svg-icon>
                        </span>
                        <?= \Yii::t('main', 'Сохранить') ?>
                    </button>
                </div>
            </div>
        </footer>
        <!-- /ko -->
        <!-- /ko -->
    </div>

    <!-- ko template: {
       foreach: templateIf($root.showDonorOrderWarning(), $data),
       afterAdd: fadeAfterAddFactory(400),
       beforeRemove: fadeBeforeRemoveFactory(400)
    } -->
    <div class="donor-order-warning">
        <button class="donor-order-warning__close" type="button" data-bind="click: function() {
            $root.hideDonorOrderWarning();
        }">
            <fc-icon params="name: 'times', size: 8"></fc-icon>
        </button>
        Вопрос-донор должен быть расположен выше вопросов-реципиентов, с которыми он связан
    </div>
    <!-- /ko -->
</div>

<template id="question-template">
    <question-logic params="
        blocked: $root.blocked,
        isWatcher: $root.isWatcher,
        canMove: !$root.blocked && $root.canResort(),
        randomOrder: $root.randomOrder,
        locked: page.locked,
        question: question,
        formControlErrorStateMatcher: $root.formControlErrorStateMatcher,
    "></question-logic>
</template>