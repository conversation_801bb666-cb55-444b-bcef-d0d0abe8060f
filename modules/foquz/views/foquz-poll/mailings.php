<?php

/** @var $this View */

/** @var $answers FoquzPollAnswer[] */

use app\modules\foquz\assets\FoquzAsset;
use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\FoquzCompanyTag;
use app\modules\foquz\models\FoquzPollMailingList;
use app\modules\foquz\models\FoquzPollMailingListSearch;
use yii\bootstrap4\ActiveForm;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\View;
use yii\helpers\Json;

$asset = FoquzAsset::register($this);
$allTags = FoquzCompanyTag::find()->where(['company_id' => $poll->company_id])->asArray()->all();
if (!$allTags) $allTags = [];

$statuses = FoquzPollMailingList::getStatuses();

$hasChannels = false;
foreach ($channels as $channel) {
    if ($channel['active']) {
        $hasChannels = true;
        break;
    }
}

$this->registerJs(
    "
    var POLL = " . Json::encode($poll) . ";
    var MAILINGS = " . json_encode($mailings) . ";
    var MAILINGS_STATUSES = " . json_encode($statuses) . ";
    var USERS = " . json_encode($users) . ";
    var CURRENT_USER = " . json_encode(Yii::$app->user->identity->toArray()) . ";
    var ALL_TAGS = " . json_encode(ArrayHelper::getColumn($allTags, 'tag')) . ";
    var POLL_ID = " . $poll->id  . ";
    var POLL_UPDATED = '" . !$poll->is_tmp . "';
    var POLL_NAME = '" . $poll->name . "';
    var CLIENTS_COUNT = " . $clientsCount,
    View::POS_HEAD
);

$this->registerCSSFile('/js/poll.mailings.css?t=' . time(), ['depends' => [app\modules\foquz\assets\FoquzAsset::className()]]);

$this->registerJSFile('/js/poll.mailings.js?t=' . time(), ['depends' => [app\modules\foquz\assets\FoquzAsset::className()]]);

/** @var $poll FoquzPoll */
$this->title = $poll->name;
?>

<div class="mailings__content mailings__content--initializing" data-bind="childrenComplete: onInit, css: { 'mailings__content--initializing': initializing() }">

    <?= $this->render('_poll_header', ['page' => 'mailings', 'model' => $poll]) ?>


    <?= $this->render('_menu_quests', [
        'model' => $poll,
        'page' => 'sender'
    ]) ?>



    <div class="f-card f-card--shadow f-card--min-height">
        <div class="f-tabs">

            <nav class="nav f-tabs__nav">
                <a class="nav-item nav-link  f-tabs__nav-item " href="<?= Url::to(['/foquz/foquz-poll/sender', 'id' => $poll->id]) ?>" aria-selected="false">
                    Ссылка на опрос
                </a>
                <a class="nav-item nav-link f-tabs__nav-item" href="<?= Url::to(['/foquz/foquz-poll/channels', 'id' => $poll->id]) ?>" aria-selected="false">
                    Каналы связи
                </a>

                <?php if ($poll->is_auto) : ?>
                    <a class="nav-item nav-link f-tabs__nav-item active" href="<?= Url::to(['/foquz/foquz-poll/mailing-conditions', 'id' => $poll->id]) ?>" aria-selected="true">
                        Фильтры рассылки
                    </a>
                <?php else : ?>
                    <a class="nav-item nav-link f-tabs__nav-item active" href="<?= Url::to(['/foquz/foquz-poll/mailings', 'id' => $poll->id]) ?>" aria-selected="true">
                        Приглашения к опросу
                    </a>
                <?php endif; ?>
                <?php if (!$poll->is_auto && (Yii::$app->user->identity->isAdmin() || Yii::$app->user->identity->isEditor())) : ?>
                    <a class="nav-item nav-link  f-tabs__nav-item" href="<?= Url::to(['/foquz/foquz-poll/widgets', 'id' => $poll->id]) ?>" aria-selected="false">
                        Виджеты
                    </a>
                <?php endif; ?>
            </nav>

            <div class="f-tabs__content">

                <div class="f-tab tab-pane show active">

                    <?php if ($hasChannels) : ?>
                        <div class="mailings__card f-card__inner">
                            <!-- ko if: !window.CURRENT_USER.watcher -->
                            <div class="mailings__card-actions mt-0" data-bind="css: {'mailings__card-actions_empty' : !isUpdated || clientsCount == 0 || !hasMailings()}">
                                <button class="btn button-add mailings__new-contact-button" type="button" data-bind="click: function () { create(); }">
                                    Создать приглашения
                                </button>
                            </div>
                            <!-- /ko -->
                            <!-- ko if: !isUpdated -->
                            <div class="mailings__empty mailings__empty_large">
                                <div class="text">
                                    <div>
                                        <div class="title"> Опрос не создан</div>
                                        Для создания рассылки необходимо настроить опрос
                                    </div>
                                </div>
                            </div>
                            <!-- /ko -->
                            <!-- ko if: isUpdated && clientsCount == 0 -->
                            <div class="mailings__empty">
                                <div class="text">
                                    <div>
                                        Для создания рассылки добавьте контакты в раздел <a href="/foquz/foquz-contact"><strong class="strong">Контакты</strong></a>
                                    </div>
                                </div>
                            </div>
                            <!-- /ko -->
                            <!-- ko if: clientsCount > 0 && isUpdated -->
                            <!-- ko if: hasMailings -->
                            <div class="mailings__card-filters">
                                <div class="mailings__card-filters-content">
                                    <div class="mailings__card-filter">
                                        <div class="form-group dense-form-group">
                                            <label class="form-label">Название</label>
                                            <input class="form-control mailings__name-filter" placeholder="Любые" data-bind="value: nameFilter, autosizeInput">
                                        </div>
                                    </div>
                                    <div class="mailings__card-filter">
                                        <div class="form-group dense-form-group">
                                            <label class="form-label">Период</label>
                                            <period-picker params="value: periodFilter, autosize: true, allowClear: true, dense: true, ranges: true"></period-picker>

                                        </div>
                                    </div>
                                    <div class="mailings__card-filter">
                                        <div class="form-group dense-form-group">
                                            <label class="form-label">Автор</label>
                                            <select data-bind="
                                    value: authorFilter,
                                    select2: {
                                        wrapperCssClass: 'select2-container--form-control',
                                        dropdownCssClass: 'dense-form-group__dropdown',
                                        minimumResultsForSearch: 0,
                                        allowClear: true,
                                        templateResult: authorTemplateResult
                                    }
                                " data-placeholder="Все авторы">
                                                <option></option>
                                                <!-- ko foreach: { data: company_users } -->
                                                <option data-bind="
                                                                value: id,
                                                                text: (name ? name : username),
                                                                attr: { 'data-avatar-url': avatar }
                                                                ">
                                                </option>
                                                <!-- /ko -->
                                            </select>
                                        </div>
                                    </div>
                                    <div class="mailings__card-filter">
                                        <div class="form-group dense-form-group">
                                            <label class="form-label">Статус</label>
                                            <select multiple data-bind="
                                element: statusFilterElement,
                                selectedOptions: statusFilter,
                                select2: {
                                    wrapperCssClass: 'select2-container--form-control',
                                    dropdownCssClass: 'dense-form-group__dropdown',
                                    containerCss: { 'min-width': '27px' }
                                }
                            " data-placeholder="Все статусы">
                                                <option value="0">Новый</option>
                                                <option value="1">Запущен</option>
                                                <option value="2">Остановлен</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="spacer mailings__card-filters-actions-spacer"></div>
                                    <div class="mailings__card-filters-actions">
                                        <button type="submit" class="btn btn-link mailings__card-filters-reset-button" data-bind="click: resetFilters">
                                            Сбросить
                                        </button>
                                        <button type="submit" class="btn btn-success btn-with-icon btn-apply mailings__card-filters-apply-button" data-bind="click: function () { reloadMailings(1);}">
                                            Применить
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="foq-table__section" data-bind="attr: { 'data-max': maxMailingsCount }">
                                <table class="table foq-table foq-table--filterable mailings__table">
                                    <thead>
                                        <tr>
                                            <!-- ko if: tableColumns.state.created -->
                                            <th>
                                                <div class="foq-table__head-cell-title cursor-pointer">
                                                    <div class="foq-table__head-cell-name foq-table__head-cell-name--active">
                                                        Создан
                                                    </div>
                                                    <i class="foq-table__sorting-icon foq-table__sorting-icon--order_desc" order-key="addedAt"></i>
                                                    <!-- ko if: createdAtStringFilter() !== '' -->
                                                    <i class="foq-table__filtering-icon"></i>
                                                    <!-- /ko -->
                                                </div>
                                                <input
                                                    class="foq-table__head-cell-filter"
                                                    data-bind="
                                                        textInput: createdAtStringFilter,
                                                        attr: {
                                                            placeholder: tableColumns.placeholders.created,
                                                        },
                                                    "
                                                />
                                            </th>
                                            <!-- /ko -->
                                            <!-- ko if: tableColumns.state.status -->
                                            <th>
                                                <div class="foq-table__head-cell-title cursor-pointer">
                                                    <div class="foq-table__head-cell-name">
                                                        Статус
                                                    </div>
                                                    <i class="foq-table__sorting-icon foq-table__sorting-icon--default" order-key="status"></i>
                                                    <!-- ko if: statusStringFilter() !== '' -->
                                                    <i class="foq-table__filtering-icon"></i>
                                                    <!-- /ko -->
                                                </div>
                                                <input
                                                    class="foq-table__head-cell-filter"
                                                    data-bind="
                                                        textInput: statusStringFilter,
                                                        attr: {
                                                            placeholder: tableColumns.placeholders.status,
                                                        },
                                                    "
                                                />
                                            </th>
                                            <!-- /ko -->
                                            <!-- ko if: tableColumns.state.name -->
                                            <th>
                                                <div class="foq-table__head-cell-title cursor-pointer">
                                                    <div class="foq-table__head-cell-name">
                                                        Название
                                                    </div>
                                                    <i class="foq-table__sorting-icon foq-table__sorting-icon--default" order-key="name"></i>
                                                    <!-- ko if: nameStringFilter() !== '' -->
                                                    <i class="foq-table__filtering-icon"></i>
                                                    <!-- /ko -->
                                                </div>
                                                <input
                                                    class="foq-table__head-cell-filter"
                                                    data-bind="
                                                        textInput: nameStringFilter,
                                                        attr: {
                                                            placeholder: tableColumns.placeholders.name,
                                                        },
                                                    "
                                                />
                                            </th>
                                            <!-- /ko -->
                                            <!-- ko if: tableColumns.state.sended -->
                                            <th>
                                                <div class="foq-table__head-cell-title">
                                                    <div class="foq-table__head-cell-name">
                                                        Конверсия
                                                    </div>
                                                    <!-- ko if: statisticsStringFilter() !== '' -->
                                                    <i class="foq-table__filtering-icon"></i>
                                                    <!-- /ko -->
                                                </div>

                                            </th>
                                            <!-- /ko -->
                                            <!-- ko if: tableColumns.state.author -->
                                            <th>
                                                <div class="foq-table__head-cell-title cursor-pointer">
                                                    <div class="foq-table__head-cell-name">
                                                        Автор
                                                    </div>
                                                    <i class="foq-table__sorting-icon foq-table__sorting-icon--default" order-key="author"></i>
                                                    <!-- ko if: authorStringFilter() !== '' -->
                                                    <i class="foq-table__filtering-icon"></i>
                                                    <!-- /ko -->
                                                </div>
                                                <input
                                                    class="foq-table__head-cell-filter"
                                                    data-bind="
                                                        textInput: authorStringFilter,
                                                        attr: {
                                                            placeholder: tableColumns.placeholders.author,
                                                        },
                                                    "
                                                />
                                            </th>
                                            <!-- /ko -->
                                            <!-- ko if: tableColumns.state.launched -->
                                            <th>
                                                <div class="foq-table__head-cell-title cursor-pointer">
                                                    <div class="foq-table__head-cell-name">
                                                        Запущен
                                                    </div>
                                                    <i class="foq-table__sorting-icon foq-table__sorting-icon--default" order-key="launchedAt"></i>
                                                    <!-- ko if: launchedAtStringFilter() !== '' -->
                                                    <i class="foq-table__filtering-icon"></i>
                                                    <!-- /ko -->
                                                </div>
                                                <input
                                                    class="foq-table__head-cell-filter"
                                                    data-bind="
                                                        textInput: launchedAtStringFilter,
                                                        attr: {
                                                            placeholder: tableColumns.placeholders.launched,
                                                        },
                                                    "
                                                />
                                            </th>
                                            <!-- /ko -->
                                            <!-- ko if: tableColumns.state.channels -->
                                            <th>
                                                <div class="foq-table__head-cell-title">
                                                    <div class="foq-table__head-cell-name">
                                                        Каналы связи
                                                    </div>
                                                </div>
                                            </th>
                                            <!-- /ko -->
                                            <th class="mailings__table-actions-head-cell" style="text-align: right">
                                                <button type="button" class="btn btn-icon btn-icon--simple foq-table__edit-columns-button" title="Настройка столбцов" data-bind="
                                                        click: openEditColumnsModal,
                                                        tooltip,
                                                        tooltipText: 'Настроить столбцы',
                                                    "></button>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- ko foreach: { data: mailings, beforeRemove: beforeRemove, afterAdd: afterAdd } -->
                                        <!-- ko if: !isNew() -->
                                        <tr class="foq-table__row mailings__table-row" data-bind="
                                                click: function () { if ([0, 1, 2].includes(status())) { $root.openDetailsModal(id()); } },
                                                css: { 'mailings__table-row--has-details': [0, 1, 2].includes(status()) },
                                                hidden: isNew()
                                            ">
                                            <!-- ko if: $root.tableColumns.state.created -->
                                            <td data-bind="text: createdAt, "></td>
                                            <!-- /ko -->
                                            <!-- ko if: $root.tableColumns.state.status -->
                                            <td>
                                                <span class="mailings__table-status" data-bind="class: $root.getTableStatusClass(status()), text: $root.getTableStatusText(status())">
                                                </span>
                                            </td>
                                            <!-- /ko -->
                                            <!-- ko if: $root.tableColumns.state.name -->
                                            <td data-bind="text: name()"></td>
                                            <!-- /ko -->
                                            <!-- ko if: $root.tableColumns.state.sended -->
                                            <td class="mailings__table-statistics-cell">
                                                <!-- ko if: status() !== 0 -->
                                                <table class="mailings__table-statistics-table m-0">
                                                    <tbody>
                                                        <tr data-bind="tooltip, tooltipText: 'Добавлено контактов'">
                                                            <td style="font-size: 11px;" class="mailings__table-statistics-table-value-cell  font-weight-bold">
                                                                <span data-bind="text: $root.getString(allClientsCount())"></span>
                                                                <div data-bind="text: $root.maxMailingsCount" style="height: 0; overflow: hidden;"></div>
                                                            </td>
                                                            <td width="100" class="mailings__table-statistics-table-indicator-cell">
                                                                <div style="width: 100px">
                                                                    <div class="mailings__table-statistics-table-indicator" style="width: 100%; background: #2E2F31;">
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td style="font-size: 11px;" colspan="2"><span class="pl-10p">Контактов</span></td>
                                                        </tr>
                                                        <!-- ko foreach: [
                                                            {
                                                                value: statistics().sent,
                                                                percent: toPercent(statistics().sent),
                                                                color: '#3A5CDC',
                                                                hint: 'Отправлено: ' + statistics().sent
                                                            },
                                                            {
                                                                value: statistics().open,
                                                                percent: toPercent(statistics().open),
                                                                color: '#8400FF',
                                                                hint: 'Опрос открыт: ' + statistics().open
                                                            },
                                                            {
                                                                value: statistics().progress,
                                                                percent: toPercent(statistics().progress),
                                                                color: '#2D99FF',
                                                                hint: 'В процессе: ' + statistics().progress
                                                            },
                                                            {
                                                                value: statistics().done,
                                                                percent: toPercent(statistics().done),
                                                                color: '#16CEB9',
                                                                hint: 'Заполнено: ' + statistics().done
                                                            }
                                                        ] -->
                                                        <tr data-bind="tooltip, tooltipText: hint">
                                                            <td style="font-size: 11px;" class="border-0 mailings__table-statistics-table-value-cell font-weight-bold" data-bind="text: $root.getString(value)"></td>
                                                            <td class="border-0 mailings__table-statistics-table-indicator-cell" style="overflow: hidden;">
                                                                <div class="mailings__table-statistics-table-indicator" data-bind="
                                                                        style: {
                                                                            width: percent + '%',
                                                                            backgroundColor: color
                                                                        }
                                                                    ">
                                                                </div>
                                                            </td>
                                                            <td width="10" class="border-0" style="font-size: 11px;" align="right">
                                                                <span class="pl-10p" data-bind="text: percent + '%'"></span>
                                                            </td>
                                                            <td class="border-0"></td>
                                                        </tr>
                                                        <!-- /ko -->
                                                    </tbody>
                                                </table>
                                                <!-- /ko -->

                                                <!-- ko if: status() === 0 -->
                                                —
                                                <!-- /ko -->
                                            </td>
                                            <!-- /ko -->
                                            <!-- ko if: $root.tableColumns.state.author -->
                                            <td data-bind="text: author"></td>
                                            <!-- /ko -->
                                            <!-- ko if: $root.tableColumns.state.launched -->
                                            <td>
                                                <!-- ko if: launchedAt() -->
                                                <span data-bind="css: {
                                                        'f-color-service': status() == 0
                                                    }, text: launchedAt()"></span>
                                                <!-- /ko -->
                                                <!-- ko ifnot: launchedAt() -->
                                                —
                                                <!-- /ko -->
                                            </td>
                                            <!-- /ko -->
                                            <!-- ko if: $root.tableColumns.state.channels -->
                                            <td>
                                                <!-- ko if: $data.customChannelSettings -->
                                                <svg viewBox="0 0 18 16" style="display: inline; vertical-align: middle; color: #8EA6FF" width="14" height="13">
                                                    <use href="#foquz-icon-user" xlink:href="#foquz-icon-user"></use>
                                                </svg>
                                                <!-- /ko -->
                                                <fc-button params="
                                                        label: window.CURRENT_USER.watcher ? 'Посмотреть' : 'Настроить',
                                                        color: 'primary',
                                                        mode: 'text',
                                                        linkMode: true,
                                                        stopPropagation: true,
                                                        linkAttrs: {
                                                            href: `./channels?id=${POLL.id}&amp;mailing_id=${$data.id()}`,
                                                        },
                                                    "></fc-button>
                                            </td>
                                            <!-- /ko -->
                                            <td class="mailings__table-actions-cell">
                                                <div class="d-flex">
                                                    <!-- ko if: status() === 0 && !window.CURRENT_USER.watcher -->
                                                    <button
                                                        class="btn btn-icon btn-icon--simple mailings__table-action-button mailings__table-launch-button"
                                                        title="Запустить"
                                                        data-bind="
                                                            disable: allClientsCount() == 0,
                                                            click: function (_, event) {
                                                                $root.openLaunchModal(id());
                                                                event.stopPropagation();
                                                            },
                                                            tooltip,
                                                            tooltipText: 'Запустить рассылку',
                                                        "
                                                    ></button>
                                                    <!-- /ko -->
                                                    <!-- ko if: status() === 1 && !window.CURRENT_USER.watcher -->
                                                    <button
                                                        class="btn btn-icon btn-icon--simple mailings__table-action-button mailings__table-stop-button"
                                                        title="Остановить"
                                                        data-bind="
                                                            click: function (_, event) {
                                                                $root.openStopModal(id());
                                                                event.stopPropagation();
                                                            },
                                                            tooltip,
                                                            tooltipText: 'Остановить рассылку',
                                                        "
                                                    ></button>
                                                    <!-- /ko -->
                                                    <!-- ko if: status() === 2 && !window.CURRENT_USER.watcher -->
                                                    <button
                                                        class="btn btn-icon btn-icon--simple mailings__table-action-button mailings__table-resume-button"
                                                        title="Возобновить"
                                                        data-bind="
                                                            click: function (_, event) {
                                                                $root.openResumeModal(id());
                                                                event.stopPropagation();
                                                            },
                                                            tooltip,
                                                            tooltipText: 'Возобновить рассылку',
                                                        "
                                                    ></button>
                                                    <!-- /ko -->
                                                    <button
                                                        class="btn btn-icon btn-icon--simple mailings__table-action-button mailings__table-delete-button"
                                                        title="Удалить"
                                                        data-bind="
                                                            click: function (_, event) {
                                                                $root.openDeleteModal(id());
                                                                event.stopPropagation();
                                                            },
                                                            tooltip,
                                                            tooltipText: 'Удалить',
                                                        "
                                                    ></button>
                                                    <!-- ko if: status() === 0 && !window.CURRENT_USER.watcher -->
                                                    <button
                                                        class="btn btn-icon btn-icon--simple mailings__table-action-button mailings__table-edit-button"
                                                        title="Редактировать"
                                                        data-bind="
                                                            click: function (_, event) {
                                                                $root.openDetailsModal(id());
                                                                event.stopPropagation();
                                                            },
                                                            tooltip,
                                                            tooltipText: 'Редактировать',
                                                        "
                                                    ></button>
                                                    <!-- /ko -->
                                                </div>
                                            </td>
                                        </tr>
                                        <!-- /ko -->
                                        <!-- /ko -->
                                    </tbody>
                                </table>
                                <!-- ko if: loading -->
                                <div id="main-loader" class="pages-loader" title="Пожалуйста, подождите ...">
                                    <i class="fa fa-spinner fa-pulse fa-2x fa-fw top"></i>
                                </div>
                                <!-- /ko -->

                                <!-- ko if: !loading() && !mailings().length -->
                                <div class="p-4 text-center f-color-service f-fs-2">Ничего не найдено</div>
                                <!-- /ko -->
                            </div>
                            <!-- /ko -->
                            <!-- ko ifnot: hasMailings -->
                            <div class="f-card__grow d-flex align-items-center justify-content-center f-color-text">
                                <div>Для опроса не добавлено ни одного приглашения</div>
                            </div>
                            <!-- /ko -->
                            <!-- /ko -->
                        </div>

                    <?php else : ?>
                        <div class="f-card__grow d-flex align-items-center justify-content-center f-color-text">
                            <div>Чтобы создать приглашения к опросу, нужно подключить хотя бы один <a href="<?= Url::to(['/foquz/foquz-poll/channels', 'id' => $poll->id]) ?>">канал связи</a></div>
                        </div>

                    <?php endif; ?>

                </div>
            </div>
        </div>
    </div>

    <!-- ko component: { name: 'modal-container', params: { opens: modalOpens } } -->
    <!-- /ko -->

    <dialogs-container params="ref: sidesheets, order: true"></dialogs-container>
    
    <success-message
        params="
            show: showDeleteModalSaveMessage,
            showCross: true,
            text: 'Приглашение к опросу удалено',
            delay: 3000,
        "
    ></success-message>
    <success-message
        class="foquz-success-message--error"
        params="
            show: showDeleteModalErrorMessage,
            showCross: true,
            text: 'Произошла ошибка. Попробуйте ещё раз.',
            delay: 3000,
        "
    ></success-message>
</div>



<script type="text/html" id="mailings-edit-columns-modal-content-template">
    <div data-bind="component: { name: 'edit-columns-modal-dialog', params: { data, modalElement, submit: function (cols) {data.submit(cols); close(); }, cancel: function () { close(); } } }" role="document"></div>
</script>


<template id="tag-input-template">
    <!-- ko template: { afterRender: onInit } -->
    <div class="tag-input__content">
        <!-- ko foreach: { data: value, afterAdd: afterAdd, beforeRemove: beforeRemove } -->
        <div class="tag-input__item">
            <!-- ko text: $data -->
            <!-- /ko -->

            <button class="btn btn-icon btn-default tag-input__item-remove-button" data-bind="click: function() { $component.removeTag($data); }">
            </button>
        </div>
        <!-- /ko -->

        <!-- ko if: addButton !== null -->
        <div class="dropdown">
            <div class="tag-input__add-tag-button-wrapper" data-toggle="dropdown" data-bind="element: addTagButtonWrapperElement">
                <button type="button" class="btn btn-circle tag-input__add-tag-button" title="Добавить теги">
                </button>

                <!-- ko if: addButton.label !== null && value().length === 0 -->
                <span class="tag-input__add-tag-button-label" data-bind="text: addButton.label"></span>
                <!-- /ko -->
            </div>

            <div class="dropdown-menu tag-input__dropdown-menu">
                <div class="tag-input__dropdown-menu-control-wrapper">
                    <input class="form-control tag-input__dropdown-menu-control" data-bind="textInput: dropdownMenuSearchTerm">

                    <!-- ko if: canCreate && dropdownMenuSearchTerm().length !== 0 -->
                    <button class="btn tag-input__dropdown-menu-control-create-tag-button" data-bind="click: function() { createTag(); }, attr: { disabled: dropdownMenuControlCreateTagButtonDisabled, title: dropdownMenuControlCreateTagButtonTitle }"></button>
                    <!-- /ko -->
                </div>

                <!-- ko if: dropdownMenuFilteredList().length > 0 -->
                <div class="tag-input__dropdown-menu-list" data-bind="scrollbar">
                    <!-- ko foreach: dropdownMenuFilteredList -->
                    <a class="dropdown-item" data-bind="click: function() { $component.addTag($data) }, text: $data"></a>
                    <!-- /ko -->
                </div>
                <!-- /ko -->

                <!-- ko if: dropdownMenuFilteredList().length === 0 -->
                <span class="tag-input__dropdown-menu-message">Совпадений не найдено</span>
                <!-- /ko -->
            </div>
        </div>
        <!-- /ko -->
    </div>
    <!-- /ko -->
</template>

<template id="tag-input-group-template">
    <!-- ko template: { afterRender: onInit } -->
    <div class="tag-input__content">
        <!-- ko foreach: { data: value, afterAdd: afterAdd, beforeRemove: beforeRemove } -->
        <div class="tag-input__item">
            <span data-bind="css: {
                    'color-success': $parent.isAuto($data)
                }, text: $data"></span>

            <button class="btn btn-icon btn-default tag-input__item-remove-button" data-bind="click: function() { $component.removeTag($data); }, ">
            </button>
        </div>
        <!-- /ko -->

        <!-- ko if: addButton !== null -->
        <div class="dropdown">
            <div class="tag-input__add-tag-button-wrapper" data-toggle="dropdown" data-bind="element: addTagButtonWrapperElement">
                <button type="button" class="btn btn-circle tag-input__add-tag-button" title="Добавить теги">
                </button>

                <!-- ko if: addButton.label !== null && value().length === 0 -->
                <span class="tag-input__add-tag-button-label" data-bind="text: addButton.label"></span>
                <!-- /ko -->
            </div>

            <div class="dropdown-menu tag-input__dropdown-menu">
                <div class="tag-input__dropdown-menu-control-wrapper">
                    <input class="form-control tag-input__dropdown-menu-control" data-bind="textInput: dropdownMenuSearchTerm">

                    <!-- ko if: canCreate && dropdownMenuSearchTerm().length !== 0 -->
                    <button class="btn tag-input__dropdown-menu-control-create-tag-button" data-bind="click: function() { createTag(); }, attr: { disabled: dropdownMenuControlCreateTagButtonDisabled, title: dropdownMenuControlCreateTagButtonTitle }"></button>
                    <!-- /ko -->
                </div>

                <!-- ko if: dropdownMenuFilteredList().length > 0 -->
                <div class="tag-input__dropdown-menu-list" data-bind="scrollbar">
                    <!-- ko let: {
                            autoTags: dropdownMenuFilteredList().filter(function(i) { return i.isAuto }),
                            defaultTags: dropdownMenuFilteredList().filter(function(i) { return !i.isAuto }),
                        } -->
                    <!-- ko if: autoTags.length -->
                    <div class="f-fs-2 color-success bold" style="padding: 4px 23px;">С условием</div>
                    <!-- ko foreach: autoTags -->
                    <a class="dropdown-item" data-bind="click: function() { $component.addTag($data) }, text: $data.name"></a>
                    <!-- /ko -->
                    <!-- /ko -->

                    <!-- ko if: defaultTags.length -->
                    <div class="f-fs-2 color-service bold" style="padding: 4px 23px;">Без условия</div>
                    <!-- ko foreach: defaultTags -->
                    <a class="dropdown-item" data-bind="click: function() { $component.addTag($data) }, text: $data.name"></a>
                    <!-- /ko -->
                    <!-- /ko -->

                    <!-- /ko -->
                </div>
                <!-- /ko -->

                <!-- ko if: dropdownMenuFilteredList().length === 0 -->
                <span class="tag-input__dropdown-menu-message">Совпадений не найдено</span>
                <!-- /ko -->
            </div>
        </div>
        <!-- /ko -->
    </div>
    <!-- /ko -->
</template>

<template id="progress-bar-template">
    <div class="progress-bar__indicator" data-bind="style: { width: ko.utils.unwrapObservable(value) + '%' }">
    </div>
    <span class="progress-bar__value">
        <!-- ko text: ko.utils.unwrapObservable(value).toFixed(1) -->
        <!-- /ko -->%
    </span>
</template>

<!-- для модалки рассылки -->
<?= $this->render('../../../../ko/legacy/models/table/template.php'); ?>
<?= $this->render('../../../../ko/legacy/components/birthday-filter/dense.template.php'); ?>

<!-- для модалки выбора клиентов -->
<?= $this->render('../../../../ko/legacy/components/birthday-filter/template.php'); ?>
