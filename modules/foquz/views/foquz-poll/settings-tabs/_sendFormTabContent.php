<?php

use app\modules\foquz\assets\FoquzAsset;

$asset = FoquzAsset::register($this);
$this->title = $model->name;
$company = $model->company;
$isPaidRate = !$company->isTariffBase();


$this->registerJs("
    window.pageData = {
        poll: " . \yii\helpers\Json::encode($model) . ",
        isPaidRate: " . (int) $isPaidRate . ",
        companyTariffId: " . (int) $company->tariff->id. ",
        notificationScripts: " . \yii\helpers\Json::encode($notificationScripts) . ",
    };
", $this::POS_HEAD);

$this->registerCSSFile('/js/poll.settings.css', ['depends' => [app\modules\foquz\assets\FoquzAsset::className()]]);
$this->registerJSFile('/js/poll.settings.js', ['depends' => [app\modules\foquz\assets\FoquzAsset::className()]]);
?>

<div class="poll-settings" data-page data-bind="childrenComplete: $root.onRender.bind($root)">
    <div class="f-card f-card--shadow f-card--min-height pt-3" data-bind="descendantsComplete: function() { rendering(false) }">

        <!-- ko if: rendering -->
        <div class="f-card__grow">
            <fc-spinner class="f-color-primary"></fc-spinner>
        </div>
        <!-- /ko -->

        <div class="f-card__grow f-card__section" style="display: none" data-bind="style: {
                display: rendering() ? 'none' : ''
            }">
            <h2 class="poll-settings__title">
                Приглашения к опросу
            </h2>
            <hr>
            <div class="row">
                <div class="col-12">
                    <!-- Не отправлять пройденный опрос -->
                    <div class="poll-settings__dont-sent-passed form-group mb-0">
                        <fc-switch params="
                                checked: dontSendIfPassed,
                                disabled: blocked,
                                label: $root.pageTranslator.t('Не отправлять опрос в приглашениях, если он уже был пройден контактом'),
                                hint: $root.pageTranslator.t('Если опция включена, то опрос респонденту можно отправить только один раз'),
                            "></fc-switch>
                    </div>
                </div>
            </div>
            <hr>
            <div class="row">
                <div class="col-12 col-md-8">
                    <!-- Не отправлять повторно, настройки -->
                    <div class="poll-settings__stop-sending form-group mb-0">
                        <fc-switch params="
                                checked: stopSending,
                                disabled: blocked,
                                label: $root.pageTranslator.t('Не отправлять повторно опрос контакту в приглашениях'),
                                hint: $root.pageTranslator.t('Не отправлять повторно опрос контакту в приглашениях'),
                            "></fc-switch>
                        <!-- ko template: {
                            foreach: templateIf(stopSending(), $data),
                            afterAdd: slideAfterAddFactory(400),
                            beforeRemove: slideBeforeRemoveFactory(400),
                        } -->
                        <div class="stop-sending-block pl-60">
                            <fc-select class="stop-sending-block__select" params="
                                    options: stopSendingConditions,
                                    value: stopSendingCondition,
                                    disabled: blocked,
                                "></fc-select>
                            <!-- ko if: stopSendingCondition() === 'period' -->
                            <div>
                                <div class="stop-sending-block-period">
                                    <fc-input class="stop-sending-block-period__input" params="
                                        mask: 'numbers',
                                        placeholder: '0',
                                        value: stopSendingPeriod,
                                        invalid: $parent.formControlErrorStateMatcher(stopSendingPeriod),
                                        valid: formControlSuccessStateMatcher(stopSendingPeriod),
                                        disabled: blocked,
                                        maxlength: 3,
                                    "></fc-input>
                                    <span class="stop-sending-block-period__label">дней</span>
                                </div>
                                <fc-error style="margin-left:30px;" params="show: formControlErrorStateMatcher(stopSendingPeriod), text: stopSendingPeriod.error"></fc-error>
                            </div>

                            <!-- /ko -->
                        </div>
                        <!-- /ko -->
                    </div>
                </div>
            </div>
            <hr>
            <div class="row">
                <div class="col-12">
                    <!-- Не отправлять, если использован промокод -->
                    <div class="poll-settings__dont-sent-promocode form-group mb-0">
                        <fc-switch params="checked: dontSendIfPromocodeUsed, disabled: blocked, label: $root.pageTranslator.t('Останавливать сценарий повторов, если был использован промокод из сообщения'), hint: $root.pageTranslator.t('Останавливать сценарий повторов, если был использован промокод из сообщения')"></fc-switch>
                    </div>
                </div>
            </div>
            <hr>
            <div class="row">
                <div class="col-12 col-md-8">
                    <!-- Игнорировать ограничение отправки опросов одному клиенту -->
                    <div class="poll-settings__reset-mailing-frequency form-group mb-0">
                        <fc-switch class="mb-0" params="checked: ignoreGlobalMailingFrequency, disabled: blocked, label: $root.pageTranslator.t('Игнорировать ограничения отправки опросов одному контакту и отправлять этот опрос контакту, если с момента отправки любого опроса прошло N дней'), hint: $root.pageTranslator.t('Игнорировать ограничение отправки опросов одному контакту')"></fc-switch>
                        <!-- ko template: {
                            foreach: templateIf(ignoreGlobalMailingFrequency(), $data),
                            afterAdd: slideAfterAddFactory(400),
                            beforeRemove: slideBeforeRemoveFactory(400),
                        } -->
                        <div class="reset-mailing-frequency__form mt-3">

                            <div class="reset-mailing-frequency__form-control">
                                <fc-input params="
                                        value: pollMailingFrequency,
                                        mask: 'numbers',
                                        placeholder: '0',
                                        invalid: $parent.formControlErrorStateMatcher(pollMailingFrequency),
                                        valid: formControlSuccessStateMatcher(pollMailingFrequency),
                                        maxlength: 5,
                                        disabled: blocked,
                                    " class="text-center"></fc-input>
                                <span class="reset-mailing-frequency__form-control-label" data-bind="text: $root.pageTranslator.t('дней')"></span>
                            </div>
                            <fc-error params="show: formControlErrorStateMatcher(pollMailingFrequency), text: pollMailingFrequency.error"></fc-error>
                        </div>
                        <!-- /ko -->
                    </div>
                </div>
            </div>
            <hr>
            <h2 class="poll-settings__title mt-40">
                Виджет
            </h2>
            <hr>
            <div class="row">
                <div class="col-12 col-md-8">
                    <!-- Игнорировать ограничение отправки опросов одному клиенту -->
                    <div class="poll-settings__reset-mailing-frequency form-group mb-0">
                        <fc-switch class="mb-0" params="checked: ignoreWidgetDisplay, disabled: blocked, label: $root.pageTranslator.t('Игнорировать ограничения показа виджета для одного посетителя сайта и показывать ему виджет, если с момента показа любого виджета прошло N дней'), hint: $root.pageTranslator.t('Игнорировать ограничения показа виджета для одного посетителя сайта и показывать ему виджет, если с момента показа любого виджета прошло N дней')"></fc-switch>
                        <!-- ko template: {
                            foreach: templateIf(ignoreWidgetDisplay(), $data),
                            afterAdd: slideAfterAddFactory(400),
                            beforeRemove: slideBeforeRemoveFactory(400),
                        } -->
                        <div class="reset-mailing-frequency__form mt-3">

                            <div class="reset-mailing-frequency__form-control">
                                <fc-input params="
                                        value: widget_display_ignore_limit,
                                        mask: 'numbers',
                                        placeholder: '0',
                                        invalid: $parent.formControlErrorStateMatcher(widget_display_ignore_limit),
                                        valid: formControlSuccessStateMatcher(widget_display_ignore_limit),
                                        maxlength: 5,
                                        disabled: blocked,
                                    " class="text-center"></fc-input>
                                <span class="reset-mailing-frequency__form-control-label" data-bind="text: $root.pageTranslator.t('дней')"></span>
                            </div>
                            <fc-error params="show: formControlErrorStateMatcher(widget_display_ignore_limit), text: widget_display_ignore_limit.error"></fc-error>
                        </div>
                        <!-- /ko -->
                    </div>
                </div>
            </div>
            <hr>
            <div class="row">
                <div class="col-12 col-md-10">
                    <!-- Не отправлять повторно, настройки -->
                    <div class="poll-settings__stop-sending form-group mb-0">
                        <fc-switch params="
                                checked: stopSendingWidget,
                                disabled: blocked,
                                label: $root.pageTranslator.t('Не показывать повторно опрос в виджете посетителю сайта'),
                                hint: $root.pageTranslator.t('Не показывать повторно опрос в виджете посетителю сайта'),
                            "></fc-switch>
                        <!-- ko template: {
                            foreach: templateIf(stopSendingWidget(), $data),
                            afterAdd: slideAfterAddFactory(400),
                            beforeRemove: slideBeforeRemoveFactory(400),
                        } -->
                        <div class="stop-sending-block pl-60 align-items-start">
                            <fc-select class="stop-sending-block__select" params="
                                    options: stopSendingWidgetConditions,
                                    value: stopSendingWidgetCondition,
                                    disabled: blocked,
                                "></fc-select>
                            <!-- ko if: stopSendingWidgetCondition() == 2 -->
                            <fc-time-amount
                                style="margin-left: 30px;"
                                params="
                                    days: stopSendingWidgetPeriod,
                                    time: stopSendingWidgetTime,
                                    errorMatcher: formControlErrorStateMatcher,
                                    successMatcher: formControlSuccessStateMatcher,
                                    disabled: blocked
                                "
                            ></fc-time-amount>
                            <!-- /ko -->
                        </div>
                        <!-- /ko -->
                    </div>

                </div>
            </div>
            <hr>


            <div class="row">
              <div class="col-12 col-md-8">
                <!-- Виджет новая анкета-->
                <!-- Создавать новую анкету для респондента, если с момента прохождения предыдущей прошло N дней. -->
                <div class="poll-settings__reset-mailing-frequency form-group mb-0">
                  <fc-switch class="mb-0" params="
                  checked: createWidgetNewAnswerLimit,
                  disabled: blocked,
                  label: $root.pageTranslator.t('Создавать новую анкету для респондента, если с момента прохождения предыдущей прошло N дней'),
                  hint: $root.pageTranslator.t('Новая анкета для респондента будет создана, если с момента его предыдущего ответа прошло указанное количество дней')
                  ">
                  </fc-switch>
                  <!-- ko template: {
                      foreach: templateIf(createWidgetNewAnswerLimit(), $data),
                      afterAdd: slideAfterAddFactory(400),
                      beforeRemove: slideBeforeRemoveFactory(400),
                  } -->
                  <div class="reset-mailing-frequency__form mt-3">

                    <div class="reset-mailing-frequency__form-control">
                      <fc-input params="
                                          value: widget_create_new_answer_limit,
                                          mask: 'numbers',
                                          placeholder: '1',
                                          invalid: $parent.formControlErrorStateMatcher(widget_create_new_answer_limit),
                                          valid: formControlSuccessStateMatcher(widget_create_new_answer_limit),
                                          maxlength: 3,
                                          disabled: blocked,
                                      " class="text-center"></fc-input>
                      <span class="reset-mailing-frequency__form-control-label" data-bind="text: $root.pageTranslator.t('дней')"></span>
                    </div>
                    <fc-error params="show: formControlErrorStateMatcher(widget_create_new_answer_limit), text: widget_create_new_answer_limit.error"></fc-error>
                  </div>
                  <!-- /ko -->
                </div>
              </div>
            </div>
            <hr>



            <h2 class="poll-settings__title mt-40">Создание новой анкеты для респондента</h2>
            <hr>
            <div class="row">
                <div class="col-12">
                    <!-- е создавать анкету по ссылке -->
                    <div class="poll-settings__dont-sent-passed form-group mb-0">
                        <fc-switch params="
                                checked: dontSendIfPassedLink,
                                disabled: blocked,
                                label: $root.pageTranslator.t('Не создавать анкету по ссылке, если уже был получен ответ от респондента'),
                                hint: $root.pageTranslator.t('Не создавать анкету по ссылке, если уже был получен ответ от респондента'),
                            "></fc-switch>
                    </div>
                </div>
            </div>
            <hr>
            <div class="row">
                <div class="col-12 col-md-10">
                    <div class="poll-settings__stop-sending form-group">
                        <fc-switch params="
                                checked: stopSendingLink,
                                disabled: blocked,
                                label: $root.pageTranslator.t('Не создавать повторно анкету для респондента по ссылке'),
                                hint: $root.pageTranslator.t('Не создавать повторно анкету для респондента по ссылке'),
                            "></fc-switch>
                        <!-- ko template: {
                            foreach: templateIf(stopSendingLink(), $data),
                            afterAdd: slideAfterAddFactory(400),
                            beforeRemove: slideBeforeRemoveFactory(400),
                        } -->
                        <div class="stop-sending-block pl-60 align-items-start">
                            <fc-select class="stop-sending-block__select" params="
                                    options: stopSendingLinkConditions,
                                    value: stopSendingLinkCondition,
                                    disabled: blocked,
                                "></fc-select>
                            <!-- ko if: stopSendingLinkCondition() === 'period' -->
                            <div>
                                <fc-time-amount
                                    style="margin-left: 30px;"
                                    params="
                                        days: stopSendingLinkPeriod,
                                        time: stopSendingLinkPeriodTime,
                                        errorMatcher: formControlErrorStateMatcher,
                                        successMatcher: formControlSuccessStateMatcher,
                                        disabled: blocked
                                    "
                                ></fc-time-amount>
                            </div>
                            <!-- /ko -->
                        </div>
                        <!-- /ko -->
                    </div>
                </div>
            </div>


        </div>

        <!-- ko ifnot: blocked -->
        <footer class="f-card__footer" data-bind="stickyFooter">
            <div class="poll-settings__actions">

                <fc-button class="poll-settings__reset" params="label: baseTranslator.t('Отменить'), icon: 'bin', click: function() { reset() }"></fc-button>

                <fc-button class="poll-settings__submit" params="label: baseTranslator.t('Сохранить'), icon: 'save', color: 'success', click: function() { submit() }"></fc-button>
            </div>

            <fc-success params="show: showSuccessMessage"></fc-success>
        </footer>
        <!-- /ko -->
    </div>

</div>
<script setup>
</script>
