<?php

$this->title = $poll->name;

$asset = \app\modules\foquz\assets\FoquzExternalAsset::register($this);
use yii\helpers\Json;
use yii\helpers\Url;

$this->registerJs("
    window.APIConfig = window.APIConfig || {};
    var POLL_NAME = '" .  addcslashes($poll->name, "\0..\37'\\") . "';
    var POLL_IS_AUTO = " . (int)$poll->is_auto . ";
    var POLL_TRIGGER = " . (int)$poll->trigger . ";
    var POLL_WITH_POINTS  = " . (int)$poll->point_system . ";
    var POLL = " . \yii\helpers\Json::encode($poll) . ";
    var IS_EXTERNAL = true;
    window.EXECUTOR_MODE = false;
    window,FILLIALS = [];
    window.CURRENT_USER = {};
", $this::POS_HEAD);

$this->registerCSSFile('/js/poll.answers-external.css?t=' . time(), ['depends' => [app\modules\foquz\assets\FoquzAsset::className()]]);
$this->registerJSFile('/js/poll.answers-external.js?t=' . time(), ['depends' => [app\modules\foquz\assets\FoquzAsset::className()]]);
$asset = \app\modules\foquz\assets\FoquzExternalAsset::register($this);

$disableFiltersBlock = true;
?>

<?= $this->render('./answers-external/_templates.php'); ?>
<div
    class="reviews-page question-statistics__content reviews-page--initializing"
    data-bind="
        childrenComplete: $root.onInit.bind($root),
        css: {
            'reviews-page--initializing': initializing(),
            'reviews-page--view-intersecting': viewTopIntersecting(),
        },
    "
>
    <header class="s-app__header">
        <div class="container-fluid s-app__container">
            <div class="d-flex justify-content-between align-items-center">
                <a class="s-app__logo mr-50p" href="<?= Url::to(['/']) ?>">
                    <img src="<?= Url::to(['/img/logo-ru.svg']) ?>" alt="" width="94">
                    <?php /* <img src="<?= Url::to(['/img/logo/logo-ny.svg']) ?>" alt="" width="94">  */ ?>
                </a>
                <div class="d-md-flex justify-content-between flex-grow-1 align-items-center d-none">
                    <h1 class="f-h1 pb-0 mb-0"><?= $poll->name ?></h1>
                </div>
            </div>
        </div>
    </header>
    <div class="container-fluid s-app__container">
        <div class="f-card f-card--shadow content pt-0">
            <div class="f-tabs">
                <nav class="nav f-tabs__nav">
                    <a
                        class="nav-item nav-link f-tabs__nav-item"
                        aria-selected="false"
                        data-bind="
                            attr: {
                                href: `/stats/${statsLinkId}?id=${window.POLL.id}`,
                            },
                        "
                    >
                        Статистика
                    </a>
                    <a
                        class="nav-item nav-link f-tabs__nav-item active"
                        href="#"
                        aria-selected="true"
                    >
                        Ответы
                    </a>
                </nav>
                <div class="f-tabs__content">
                    <div class="f-card__inner">

                        <!-- ko if: preparing -->
                        <div class="py-3">
                            <spinner></spinner>
                        </div>
                        <!-- /ko -->

                        <!-- ko ifnot: preparing -->
                        <div style="display: none" data-bind="style: {
                            display: preparing() ? 'none' : ''
                        }">
                            <!-- ko ifnot: noAnswers  -->
                            <?= $this->render('./answers-external/_actions.php', [
                                'poll' => $poll,
                                'mode' => 'poll',
                                'advantageSuppliersExcelEnabled' => $advantageSuppliersExcelEnabled,
                            ]);
                            ?>

                            <?= $this->render('./answers-external/_mobile-filters.php', ['mode' => 'answers', 'executor' => 0]); ?>

                            <div data-filters style="display: none" data-closed>
                                <?php if (!$disableFiltersBlock) { ?>
                                    <?= $this->render('./answers-external/_points.php', ['poll' => $poll, 'mode' => 'poll']); ?>
                                    <?= $this->render('./answers-external/_filters.php', ['poll' => $poll, 'mode' => 'poll']); ?>
                                <?php } ?>
                            </div>
                            <!-- /ko -->

                            <?= $this->render('./answers-external/_meta.php', ['poll' => $poll, 'mode' => 'poll']); ?>

                            <!-- ko if:noAnswers -->
                            <div class="f-card__section f-card__grow justify-content-center align-items-center d-flex f-color-text">
                                Ответов пока нет
                            </div>
                            <!-- /ko -->

                            <!-- ko ifnot: noAnswers -->
                            <?= $this->render('./answers-external/_data.php', ['mode' => 'poll', 'poll' => $poll]); ?>
                            <!-- /ko -->
                        </div>
                        <!-- /ko -->
                    </div>
                </div>
            </div>
        </div><!-- .content -->
    </div>
    <!-- ko component: { name: 'modal-container', params: { opens: modalOpens } } -->
    <!-- /ko -->
    <dialogs-container params="ref: dialogs"></dialogs-container>
    <dialogs-container params="ref: detailsDialogs"></dialogs-container>
</div><!-- .reviews-page -->
