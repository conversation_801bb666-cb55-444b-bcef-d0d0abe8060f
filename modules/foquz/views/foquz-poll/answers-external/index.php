<?php

if (isset($_GET['lang'])) {
    \Yii::$app->language = $_GET['lang'];
}

$this->title = \Yii::t('main', 'Ответы');

use app\modules\foquz\assets\FoquzAsset;
use yii\helpers\Json;
use yii\helpers\Url;

$asset = FoquzAsset::register($this);
$isAutoPoolEnabled = (Yii::$app->user->identity->company->auto_poll_enabled) === 1;



$this->registerJs("
    var ACCESS_TOKEN = '" . Yii::$app->user->identity->access_token . "';
    var USER_ID = " . Yii::$app->user->id . ";
    window.EXECUTOR_MODE = false;
    window.isAutoPoolEnabled = " . Json::encode($isAutoPoolEnabled) . ";
    window.IS_EXTERNAL = true;
", $this::POS_HEAD);

$this->registerJSFile('/js/answers.main.js?t=' . time(), ['depends' => [app\modules\foquz\assets\FoquzAsset::className()]]);

$this->registerCSSFile('/js/answers.main.css?t=' . time(), ['depends' => [app\modules\foquz\assets\FoquzAsset::className()]]);
?>



<?= $this->render('./_templates.php'); ?>



<div class="reviews-page <?= $totalCount == 0 ? 'reviews-page--empty' : '' ?>" data-bind="childrenComplete: $root.onInit.bind($root), css: {

'reviews-page--grouped': groupedMode(),
'reviews-page--view-intersecting': viewTopIntersecting(),
'reviews-page--active-poll-fixed': activePollFixed(),
}" data-app-container>
    <div class="f-card f-card--shadow f-card--min-height f-card--lg">

        <div class="f-tabs">
            <?= $this->render('./_tabs.php', ['page' => 'answers']); ?>


            <div class="f-tabs__content">

                <?php if ($totalCount == 0) : ?>
                    <div class="f-card__inner">
                        <?= $this->render('./_empty.php', ['mode' => 'answers', 'executor' => 0]); ?>
                    </div>
                <?php else : ?>
                    <div class="f-card__inner">

                        <!-- ko if: preparing -->
                        <div class="py-3">
                            <spinner></spinner>
                        </div>
                        <!-- /ko -->


                        <!-- ko ifnot: preparing -->
                        <div style="display: none" data-bind="style: {
                            display: preparing() ? 'none' : ''
                        }">

                            <?php if (!Yii::$app->user->identity->isFilialEmployee()) : ?>
                                <!-- ko if: noAnswers -->
                                <div class="f-card__section f-card__divider reviews-page-actions">


                                        <button class="f-btn f-btn-lg" type="button" data-bind="click: completePollForClient"><?= \Yii::t('answers', 'Пройти опрос за респондента') ?></button>

                                </div>
                                <!-- /ko -->
                            <?php endif; ?>

                            <!-- ko ifnot: noAnswers -->
                            <?= $this->render('./_actions.php', ['mode' => 'answers', 'executor' => 0]); ?>



                            <?= $this->render('./_mobile-filters.php', ['mode' => 'answers', 'executor' => 0]); ?>

                            <div data-filters style="display: none" data-closed>
                                <?= $this->render('./_points.php', ['mode' => 'answers', 'executor' => 0]); ?>
                                <?= $this->render('./_filters.php', ['mode' => 'answers', 'executor' => 0]); ?>
                            </div>
                            <!-- /ko -->



                            <?= $this->render('./_meta.php', ['mode' => 'answers', 'executor' => 0]); ?>

                            <!-- ko if: noAnswers -->
                            <div class="f-card__section f-card__grow justify-content-center align-items-center d-flex f-color-service">
                                Ответов пока нет
                            </div>
                            <!-- /ko -->

                            <!-- ko ifnot: noAnswers -->
                            <?= $this->render('./_data.php', ['mode' => 'answers']); ?>
                            <!-- /ko -->

                        </div>
                        <!-- /ko -->

                        <!-- ko component: { name: 'modal-container', params: { opens: modalOpens } } -->
                        <!-- /ko -->

                    </div>
                <?php endif; ?>

            </div>
        </div>

        <dialogs-container params="ref: dialogs"></dialogs-container>
        <dialogs-container params="ref: detailsDialogs"></dialogs-container>

    </div>
</div>
