<?php

use yii\helpers\Url;
use yii\web\View;

$this->title = 'Компании';
$getParam = function ($name) {
    return isset($_GET[$name]) ? $_GET[$name] : '';
};

use app\modules\foquz\assets\FoquzAsset;

$asset = FoquzAsset::register($this);

$this->registerJs("
    var PARAMS = {
        page: 1,
        sort: '{$getParam('sort')}',
        companyService: '{$getParam('companyService')}',
        companyAlias: '{$getParam('companyAlias')}',
        companyName: '{$getParam('companyName')}',
        companyCreate: '{$getParam('companyCreate')}',
        withTariffsProblems: '{$getParam('withTariffsProblems')}',
        tariff: '{$getParam('tariff')}',
        referalName: '{$getParam('referalName')}',
        countReferals: '{$getParam('countReferals')}',
        referal: '{$getParam('referal')}'
    };
", $this::POS_HEAD);

$this->registerJSFile('/js/company.list.js', ['depends' => [app\modules\foquz\assets\FoquzAsset::className()]]);

$this->registerCSSFile('/js/company.list.css', ['depends' => [app\modules\foquz\assets\FoquzAsset::className()]]);
?>

<div class="companies-list companies__content companies__content--initializing" data-bind="childrenComplete: onInit, css: { 'companies__content--initializing': initializing() }">
    <div class="f-card f-card--lg f-card--shadow f-card--min-height" data-bind="using: table">
        <div class="f-card__section f-card__divider">
            <div class="d-flex justify-content-end">
                <a class="btn button-add companies__card-new-company-button" type="button" href="<?= Url::to(['/foquz/company/create']) ?>">
                    Новая компания
                </a>
            </div>
        </div>

        <div class="f-card__section f-card__divider">
            <div class="d-flex align-items-center mt-0 flex-wrap mx-n10p pb-0">


                <div class="form-group dense-form-group px-10p mb-3">
                    <label class="form-label">Рефералы компании</label>

                    <select data-bind="
                        value: filters.referal,
                        lazySelect2: {
                            wrapperCssClass: 'select2-container--form-control',
                            dropdownCssClass: 'dense-form-group__dropdown',
                            minimumResultsForSearch: 0,
                            containerCss: { 'min-width': '50px' },
                            allowClear: true,
                            placeholder: 'Все'
                        }">
                        <option></option>
                        <?php foreach ($companies as $company) : ?>
                            <option value="<?= $company->id ?>"><?= $company->name ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="form-group dense-form-group px-10p mb-3">
                    <label class="form-label">Юр.лицо получателя</label>

                    <collection-select params="collection: legalEntitiesCollection, value: filters.legalEntity, placeholder: 'Все', dense: true"></collection-select>

                </div>

                <div class="form-group dense-form-group px-10p mb-3">
                    <label class="form-label">Тариф</label>


                    <select data-bind="
                        selectedOptions: filters.tariffs,
                        lazySelect2: {
                            wrapperCssClass: 'select2-container--form-control',
                            dropdownCssClass: 'dense-form-group__dropdown',
                            minimumResultsForSearch: Infinity,
                            containerCss: { 'min-width': '50px' },
                            placeholder: 'Все'
                        }" multiple>

                        <option></option>

                        <?php foreach ($tariffs as $id => $name) : ?>
                            <option value="<?= $id ?>"><?= $name ?></option>
                        <?php endforeach; ?>

                    </select>
                </div>

                <div class="px-10p mb-3">
                    <input-checkbox params="checked: filters.withTariffsProblems">Есть проблема с тарифом</input-checkbox>
                </div>

                <div class="px-10p mb-3">
                    <input-checkbox params="checked: filters.autoPollEnabled">Есть автоматические опросы/рассылки</input-checkbox>
                </div>

                <div class="px-10p ml-auto mb-3">
                    <button class="f-btn f-btn-link" data-bind="click: function() { resetFilters() }">Сбросить</button>
                    <button class="f-btn f-btn-lg f-btn-success" data-bind="click: function() { applyFilters() }">Применить</button>
                </div>
            </div>
        </div>

        <div class="f-card__section f-card__grow">
            <interactive-table params="table: $data, scroll: true">
                <table class="table f-table f-table--searchable f-table--outer companies-table f-table-dense">
                    <thead>
                        <tr>
                            <th class="f-table__outer-element-head-cell"></th>
                            <th data-bind="component: {
                                name: 'table-head-cell',
                                params: {
                                    sort: sort,
                                    sortName: 'created_at',
                                    withSearch: true,
                                    searchValue: search.companyCreate,
                                    onSearch: function() { reset(); }
                                }
                                }">Дата&nbsp;создания</th>
                            <th data-bind="component: {
                                name: 'table-head-cell',
                                params: {
                                    sort: sort,
                                    sortName: 'name',
                                    withSearch: true,
                                    searchValue: search.companyName,
                                    onSearch: function() { reset(); }
                                }
                                }">Название</th>
                            <th data-bind="component: {
                                name: 'table-head-cell',
                                params: {
                                    sort: sort,
                                    sortName: 'tariffs.title',
                                    withSearch: true,
                                    searchValue: search.tariff,
                                    onSearch: function() { reset(); }
                                }
                                }">Тариф</th>
                            <th class="text-nowrap" data-bind="component: {
                                name: 'table-head-cell',
                                params: {
                                    sort: sort,
                                    sortName: 'bonuses_balance',
                                    withSearch: true,
                                    searchValue: search.bonuses_balance,
                                    onSearch: function() { reset(); }
                                }
                                }">Бонусный счет</th>
                            <th data-bind="component: {
                                name: 'table-head-cell',
                                params: {
                                    sort: sort,
                                    sortName: 'referalName',
                                    withSearch: true,
                                    searchValue: search.referalName,
                                    onSearch: function() { reset(); }
                                }
                                }">Кто&nbsp;привел</th>
                            <th data-bind="component: {
                                name: 'table-head-cell',
                                params: {
                                    sort: sort,
                                    sortName: 'countReferals',
                                    withSearch: true,
                                    searchValue: search.countReferals,
                                    onSearch: function() { reset(); }
                                }
                                }">Приведено</th>
                            <th data-bind="component: {
                                name: 'table-head-cell',
                                params: {
                                    sort: sort,
                                    sortName: 'alias',
                                    withSearch: true,
                                    searchValue: search.companyAlias,
                                    onSearch: function() { reset(); }
                                }
                                }">Домен</th>
                            <th data-bind="component: {
                                name: 'table-head-cell',
                                params: {
                                    sort: sort,
                                    sortName: 'company_iiko_access.name',
                                    withSearch: true,
                                    searchValue: search.companyService,
                                    onSearch: function() { reset(); }
                                }
                                }">Доступы&nbsp;к&nbsp;сервисам</th>
                            <th width="60"></th>
                            <th class="f-table__outer-element-head-cell"></th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- ko foreach: { data: items, as: 'item' } -->
                        <tr class="f-table__row" data-bind="css: { 'f-table__row--error': item.hasError }">

                            <td class="f-table__outer-element-cell"></td>
                            <td data-bind="text: item.createdAt">
                            </td>
                            <td>
                                <div class="d-flex position-relative">
                                    <!-- ko if: item.auto_poll_enabled === '1' -->
                                    <a class="btn btn-icon btn-icon--simple p-0 companies__table-auto-pool-icon" title="Включены автоматические опросы/рассылки"></a>
                                    <!-- /ko -->

                                    <span data-bind="html: item.name"></span>
                                </div>
                            </td>

                            <td data-bind="text: item.tariff"></td>
                            <td>
                                <!-- ko if: item.bonuses -->
                                <span data-bind="text: item.bonuses.toLocaleString()"></span>
                                <!-- /ko -->

                                <!-- ko ifnot: item.bonuses -->
                                —
                                <!-- /ko -->
                            </td>
                            <td>
                                <!-- ko if: item.referalName -->
                                <a data-bind="text: item.referalName, attr: {
                                    href: '/foquz/company/update?id=' + item.referalId
                                }"></a>
                                <!-- /ko -->

                                <!-- ko ifnot: item.referalName -->
                                —
                                <!-- /ko -->
                            </td>
                            <td>
                                <!-- ko if: item.countReferals > 0 -->
                                <span class="bold" data-bind="text: item.countReferals"></span>
                                <!-- /ko -->

                                <!-- ko ifnot: item.countReferals > 0 -->
                                —
                                <!-- /ko -->
                            </td>

                            <td>
                                <a class="companies__table-domain" data-bind="attr:{href : 'http://'+ item.alias}" target="_blank">
                                    <!-- ko text: item.alias -->
                                    <!--/ko-->
                                </a>
                            </td>
                            <td>
                                <!-- ko if: item.companyIikoAccesses.length > 0 -->
                                <!-- ko foreach: item.companyIikoAccesses -->
                                <!-- ko text: name -->
                                <!-- /ko -->
                                <!-- ko if: $index() !== item.companyIikoAccesses.length - 1 -->,
                                <!-- /ko -->
                                <!-- /ko -->
                                <!-- /ko -->

                                <!-- ko if: item.companyIikoAccesses.length === 0 -->—
                                <!-- /ko -->
                            </td>
                            <td class="companies__table-actions-cell">
                                <div class="d-flex">
                                    <a class="btn btn-icon btn-icon--simple companies__table-action-button companies__table-edit-button" title="Редактировать" data-bind="attr:{href:'/foquz/company/update?id='+item.id}">
                                    </a>
                                </div>
                            </td>
                            <td class="f-table__outer-element-cell"></td>
                        </tr>
                        <!-- /ko -->
                    </tbody>
                </table>


            </interactive-table>
        </div>

    </div>

    <foquz-modals-container params="modals: table.modals"></foquz-modals-container>
</div>
