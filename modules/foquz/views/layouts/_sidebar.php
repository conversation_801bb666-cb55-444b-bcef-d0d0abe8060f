<?php

use app\models\components\CheckAgreementsBehavior;
use app\modules\foquz\models\Tariff;
use yii\helpers\Url;

$hasUser = !(Yii::$app->user->identity === null);

$isExecutor = $hasUser && Yii::$app->user->identity->isExecutor();
$isSuperAdmin = $hasUser && Yii::$app->user->identity->superadmin;
$isEditor = $hasUser && Yii::$app->user->identity->isEditor();
$isWatcher = $hasUser && Yii::$app->user->identity->isWatcher();
$isWikiEditor = $hasUser && Yii::$app->user->identity->isWikiEditor();
$isFilialEmployee = $hasUser && Yii::$app->user->identity->isFilialEmployee();
$isReportAdmin = $hasUser && Yii::$app->user->identity->isReportAdmin();
$isRespondent = $hasUser && Yii::$app->user->identity->isRespondent();
$isAutoPollEnabled = ($hasUser && Yii::$app->user->identity->company->auto_poll_enabled) or (isset($company) and $company->auto_poll_enabled);
$isClientsEnabled = $hasUser && Yii::$app->user->identity->isClientsSectionAccess();
$isTariffBase = $hasUser && Yii::$app->user->identity->company->isTariffBase();
$acceptArgeement = $hasUser && Yii::$app->user->identity->isAgreementAccepted();
$behavior = CheckAgreementsBehavior::$instance;
if ($behavior && !$behavior->checkOn) {
    $acceptArgeement = true;
}
$hasSidebar = $hasUser && !$isExecutor && !$isRespondent && $acceptArgeement;
$sidebarMenu = [
  'polls', 'mailings', 'answers', 'reports', 'clients', /*'cpoints',*/ 'settings', 'widget',
  'requests',
  'payment', 'help'
];

if ($isSuperAdmin) {
  $sidebarMenu = ['companies', 'users', 'coupons', 'admin-report', 'legals', 'help-editor'];
} else if ($isEditor || $isWatcher) {
  if ($isClientsEnabled) {
    $sidebarMenu = ['polls', 'answers', 'reports', 'clients', 'widget', 'help'];
  } else {
    $sidebarMenu = ['polls', 'answers', 'reports', 'widget', 'help'];
  }
} else if ($isWikiEditor) {
  $sidebarMenu = ['help-editor', 'payment'];
} else if ($isFilialEmployee) {
  $sidebarMenu = ['polls', 'answers', 'reports', 'help'];
} else if ($isReportAdmin) {
  $sidebarMenu = ['polls', 'mailings', 'answers', 'reports', 'clients', /*'cpoints',*/ 'payment', 'help',];
}

//Временно убран из меню в рамках задачи #3608
$paymentKey = array_search('payment', $sidebarMenu);
if ($paymentKey !== false) {
    unset($sidebarMenu[$paymentKey]);
}

//Если рассылки для компании отключены, убираем их из меню
if (empty(Yii::$app->user->identity->company->mailings_enabled)) {
    $mailingstKey = array_search('mailings', $sidebarMenu);
    if ($mailingstKey !== false) {
        unset($sidebarMenu[$mailingstKey]);
    }
}
//Если тариф Базовый - убрать раздел Заявки, Виджеты, Отчеты.
if ($isTariffBase) {
    $menuKey = array_search('requests', $sidebarMenu);
    if ($menuKey !== false) {
        unset($sidebarMenu[$menuKey]);
    }
    $menuKey = array_search('widget', $sidebarMenu);
    if ($menuKey !== false) {
        unset($sidebarMenu[$menuKey]);
    }
    $menuKey = array_search('reports', $sidebarMenu);
    if ($menuKey !== false) {
        unset($sidebarMenu[$menuKey]);
    }
}

if (!empty(Yii::$app->view->params['accessDenied'])) {
    $sidebarMenu = [];
}

$sidebarMenuItems = [
  'polls' => [
    'id' => 'surveys',
    'active' => stristr(Url::current(), '/foquz/foquz-poll') || stristr(Url::current(), '/foquz/foquz-question'),
    'url' => Url::to(['/foquz']),
    'title' => \Yii::t('main', 'Опросы'),
    'tooltip' => \Yii::t('main', 'Опросы'),
    'mobile' => 1
  ],
  'mailings' => [
    'id' => 'mailings',
    'active' => stristr(Url::current(), '/foquz/mailings'),
    'url' => Url::to(['/foquz/mailings']),
    'title' => \Yii::t('main', 'Рассылки'),
    'tooltip' => \Yii::t('main', 'Рассылки'),
    'mobile' => 0
  ],
  'answers' => [
    'id' => 'answers',
    'active' => stristr(Url::current(), '/foquz/answers'),
    'url' => Url::to(['/foquz/answers']),
    'title' => \Yii::t('main', 'Ответы'),
    'tooltip' => \Yii::t('main', 'Ответы'),
    'mobile' => 1
  ],
  'reports' => [
    'id' => 'reports',
    'active' => stristr(Url::current(), '/foquz/reports'),
    'url' => Url::to(['/foquz/reports']),
    'title' => \Yii::t('main', 'Отчёты'),
    'tooltip' => \Yii::t('main', 'Отчёты'),
    'mobile' => 0
  ],
  'clients' => [
    'id' => 'clients',
    'active' => stristr(Url::current(), '/foquz/foquz-contact'),
    'url' => Url::to(['/foquz/foquz-contact']),
    'title' => \Yii::t('main', 'Контакты'),
    'tooltip' => \Yii::t('main', 'Контакты'),
    'mobile' => 0
  ],
  /*'cpoints' => [ // убрано в рамках задачи #4484
    'id' => 'contact-points',
    'active' => stristr(Url::current(), '/foquz/contact-points'),
    'url' => Url::to(['/foquz/contact-points']),
    'title' => \Yii::t('main', 'Точки контакта'),
    'tooltip' => \Yii::t('main', 'Точки контакта'),
    'mobile' => 0
  ],*/
  'settings' => [
    'id' => 'settings',
    'active' => stristr(Url::current(), '/foquz/settings'),
    'url' => Url::to(['/foquz/settings']),
    'title' => \Yii::t('main', 'Настройки'),
    'tooltip' => \Yii::t('main', 'Настройки'),

    'mobile' => 0
  ],
  'help' => [
    'id' => 'help',
    'active' => false,
    'url' => 'https://foquz.ru/foquz/user-wiki',
    'title' => \Yii::t('main', 'Помощь'),
    'tooltip' => \Yii::t('main', 'Помощь'),
    'mobile' => 0,
    'target' => '_blank'
  ],
  'companies' => [
    'id' => 'companies',
    'active' => stristr(Url::current(), '/foquz/company') && !stristr(Url::current(), '/foquz/company/report'),
    'url' => Url::to(['/foquz/company']),
    'title' => \Yii::t('main', 'Компании'),
    'tooltip' => \Yii::t('main', 'Компании'),
    'mobile' => 0
  ],
  'legals' => [
    'id' => 'legals',
    'active' => stristr(Url::current(), '/foquz/legal-entities'),
    'url' => Url::to(['/foquz/legal-entities']),
    'title' => \Yii::t('main', 'Юр. лица'),
    'tooltip' => \Yii::t('main', 'Юр. лица'),
    'mobile' => 0
  ],
  'payment' => [
    'id' => 'payment',
    'active' => stristr(Url::current(), '/foquz/payments'),
    'url' => Url::to(['/foquz/payments']),
    'title' => \Yii::t('main', 'Оплата'),
    'tooltip' => \Yii::t('main', 'Оплата'),
    'tariffWarning' => $tariffWarning['visible'],
    'tariffWarningText' => $tariffWarning['tooltip'],
    'mobile' => 0
  ],
  'users' => [
    'id' => 'users',
    'active' => stristr(Url::current(), '/foquz/users'),
    'url' => Url::to(['/foquz/users']),
    'title' => \Yii::t('main', 'Пользователи'),
    'tooltip' => \Yii::t('main', 'Пользователи'),
    'mobile' => 0
  ],
  'help-editor' => [
    'id' => 'help',
    'active' => stristr(Url::current(), '/foquz/wiki'),
    'url' => Url::to(['/foquz/wiki']),
    'title' => \Yii::t('main', 'Помощь'),
    'tooltip' => \Yii::t('main', 'Помощь'),
    'mobile' => 0,

  ],
  'widget' => [
    'id' => 'widget',
    'active' => stristr(Url::current(), '/foquz/widget-preferences') || stristr(Url::current(), '/foquz/informer') || stristr(Url::current(), '/foquz/widgets'),
    'url' => Url::to(['/foquz/widgets']),
    'title' => \Yii::t('main', 'Расширения'),
    'tooltip' => \Yii::t('main', 'Расширения'),
    'mobile' => 0
  ],
  'requests' => [
    'id' => 'requests',
    'active' => stristr(Url::current(), '/foquz/requests-projects'),
    'url' => Url::to(['/foquz/requests-projects']),
    'title' => \Yii::t('main', 'Заявки'),
    'tooltip' => \Yii::t('main', 'Заявки'),
    'mobile' => 0
  ],
  'coupons' => [
    'id' => 'coupons',
    'active' => stristr(Url::current(), '/foquz/coupons'),
    'url' => Url::to(['/foquz/coupons']),
    'title' => \Yii::t('main', 'Купоны'),
    'tooltip' => \Yii::t('main', 'Купоны'),
    'mobile' => 0
  ],
  'admin-report' => [
    'id' => 'admin-report',
    'active' => stristr(Url::current(), '/foquz/company/report'),
    'url' => Url::to(['/foquz/company/report']),
    'title' => \Yii::t('main', 'Отчёт'),
    'tooltip' => \Yii::t('main', 'Отчёт'),
    'mobile' => 0
  ]
];

$isProfilePage = stristr(Url::current(), '/foquz/profile');
$isSettingsPage = stristr(Url::current(), '/foquz/settings');

?>

<?php if ($hasSidebar) : ?>
  <div class="sidebar">
    <fc-sidebar params="isEditor: <?= $isEditor ? 1 : 0 ?>">
      <div class="sidebar__row">
        <div class="sidebar__button">
          <button type="button" class="btn btn-menu btn-menu--close"></button>
        </div>
        <div class="sidebar__logo">
          <a class="s-app__logo mr-50p" href="<?= Url::to(['/foquz']) ?>">
              <img src="<?= Url::to(['/img/logo-ru.svg']) ?>" alt="" width="94">
              <?php /* <img src="<?= Url::to(['/img/logo/logo-ny.svg']) ?>" alt="" width="94">  */ ?>
          </a>
        </div>
      </div>

      <div class="dropdown">
        <div class="sidebar__account">
          <a class="sidebar__picture-link" href="#picture" type="button" data-toggle="dropdown" data-flip="false">
            <img class="sidebar__account-picture js-user-avatar" alt="" src="<?= Yii::$app->getUser()->getIdentity()->getThumbUploadUrl('avatar', 'preview') ?>" />
          </a>
          <div class="sidebar__info" data-toggle="dropdown" data-flip="false">
            <a class="sidebar__username-link js-user-name" href="/foquz/profile">
              <?= Yii::$app->getUser()->getIdentity()->getCorrectName() ?>
            </a>
            <?php if (Yii::$app->user->identity->isWatcher()) {
              echo ('<div class="sidebar__username-role">
              Наблюдатель
            </div>');
            }
            ?>
          </div>
          <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
            <?php if (empty(Yii::$app->view->params['accessDenied'])) : ?>
                <a class="dropdown-item" href="/foquz/profile"> <?= \Yii::t('main', 'Настройка профиля') ?></a>
            <?php endif; ?>
            <a class="dropdown-item" href="<?= Url::to(['/user-management/auth/logout']) ?>">
              <?= \Yii::t('main', 'Выход') ?>
              <i class="icon-logout"></i>
            </a>
          </div>
        </div>
      </div>

      <?php if (
              !Yii::$app->user->identity->superadmin &&
              empty(Yii::$app->view->params['accessDenied']) &&
              Yii::$app->getUser()->can('pollProcessing')
      ) : ?>
        <!-- ko if: editorWithoutFolders -->
        <div class="sidebar__create-survays" data-bind="fbPopper, title: '<?= \Yii::t('main', 'Нет доступных папок для создания опроса. Обратитесь к администратору') ?>'">
          <div class="dropdown">
            <button class="button-add button-add--with-dropdown btn disabled" type="button">
              <?= \Yii::t('main', 'Создать') ?>
            </button>
          </div>
        </div>
        <!-- /ko -->

        <!-- ko ifnot: editorWithoutFolders -->
        <div class="sidebar__create-survays" data-bind="fbPopper, title: '<?= \Yii::t('main', 'Создать') ?>'">
          <div class="dropdown">
            <button class="button-add button-add--with-dropdown btn" type="button" data-toggle="dropdown" data-flip="false">
              <?= \Yii::t('main', 'Создать') ?>
            </button>

            <div class="dropdown-menu">

              <a class="dropdown-item text-nowrap" href="javascript:void(0)" data-bind="click: function(_, event) { createPoll({}, event) }" data-poll-create>
                <?= \Yii::t('main', 'Ручной опрос') ?>
              </a>
              <a class="dropdown-item text-nowrap" href="javascript:void(0)" data-bind="click: function(_, event) { createPoll({ pointSystem: 1 }, event) }" data-poll-create>
                <?= \Yii::t('main', 'Ручной опрос с баллами') ?>
              </a>

              <?php if (!$isEditor && !empty(Yii::$app->user->identity->company->mailings_enabled)) : ?>
                <a class="dropdown-item text-nowrap" href="<?= Url::to(['/foquz/mailings/create']) ?>" data-mailing-create>
                  <?= \Yii::t('main', 'Ручная рассылка') ?>
                </a>
              <?php endif; ?>
              <a class="dropdown-item text-nowrap" href="javascript:void(0)" data-import-poll>
                <?= \Yii::t('main', 'Импортировать опрос из JSON-файла') ?>
              </a>
              <hr class="dropdown-hr">
              <?php if (!$isAutoPollEnabled) : ?>
                <span class="dropdown-auto-pool-not-connected-text"><?= \Yii::t('main', 'Подключить') ?>:</span>
              <?php endif; ?>

              <a class="dropdown-item text-nowrap" data-bind="click: function(_, event) { createPoll({ isAuto: 1 }, event) }" data-poll-create>
                <?= \Yii::t('main', 'Автоматический опрос') ?>
              </a>
              <a class="dropdown-item text-nowrap" data-bind="click: function(_, event) { createPoll({ isAuto: 1, pointSystem: 1 }, event) }" data-poll-create>
                <?= \Yii::t('main', 'Автоматический опрос с баллами') ?>
              </a>

              <?php if (!$isEditor && !empty(Yii::$app->user->identity->company->mailings_enabled)) : ?>
                <a class="dropdown-item text-nowrap" href="<?= Url::to(['/foquz/mailings/create', 'isAuto' => 1]) ?>" data-mailing-create>
                  <?= \Yii::t('main', 'Автоматическая рассылка') ?>
                </a>
              <?php endif; ?>
            </div>
          </div>
        </div>
        <!-- /ko -->



      <?php endif; ?>

      <div class="sidebar__menu">
        <ul class="sidebar__menu-bar">

          <?php
          foreach ($sidebarMenu as $menuItemName) :
            $menuItem = $sidebarMenuItems[$menuItemName];
            $menuItemClassName = "sidebar__item-link";
            $menuItemClassName .= " sidebar__item-link--" . $menuItem['id'];
            if ($menuItem['active']) $menuItemClassName .= " active";
            if (isset($menuItem['blocked'])) $menuItemClassName .= " sidebar__item-link--disabled";
            $target = isset($menuItem['target']) ? $menuItem['target'] : '';
          ?>

            <li class="sidebar__menu-item <?= $menuItem['mobile'] ? '' : 'd-none d-md-block' ?>">
              <a class="<?= $menuItemClassName ?>" target="<?= $target ?>" href="<?= $menuItem['url'] ?>" data-bind="fbPopper, title: '<?= $menuItem['tooltip'] ?>'">
                <?= $menuItem['title'] ?>
              </a>
              <?php if (isset($menuItem['tariffWarning'])) : ?>
                <div class="f-exclamation-mark f-exclamation-mark--tariff" data-bind="fbPopper, title: '<?= $menuItem['tariffWarningText'] ?>'"></div>
              <?php endif; ?>
            </li>

          <?php endforeach; ?>

          <li class="sidebar__menu-item mt-2 mb-0 pb-10p">
            <?php if ($isProfilePage) : ?>
              <lang-toggler class="d-md-none" params="view: 'modal', mode: 'user', id: '<?= Yii::$app->user->identity->id ?>'"></lang-toggler>
            <?php endif; ?>

          </li>


          <li class="sidebar__menu-item">
            <button class="f-btn f-btn-primary f-btn--round jivo-btn" data-jivo type="button" onclick="openJivo()" data-bind="fbPopper, title: '<?= \Yii::t('main', 'Задать вопрос оператору') ?>'">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M16.75 1H3.25C2.00898 1 1 2.00898 1 3.25V12.2508C1 13.4918 2.00898 14.5008 3.25 14.5008H7.00035V18.251C7.00035 18.5955 7.3941 18.7959 7.67184 18.592L13.0007 14.5008H16.75C17.991 14.5008 19 13.4918 19 12.2508V3.25C19 2.00898 17.991 1 16.75 1Z" stroke="white" stroke-width="2" />
              </svg>
            </button>
          </li>

        </ul>


      </div>
    </fc-sidebar>
  </div>
<?php endif; ?>