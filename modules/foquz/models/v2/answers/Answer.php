<?php

namespace app\modules\foquz\models\v2\answers;

use app\modules\foquz\models\FoquzContact;
use app\modules\foquz\models\FoquzPollAnswer;
use yii\helpers\ArrayHelper;

class Answer
{
    private $answer;
    private $answerItems = [];
    private $answerResponse;

    public function __construct(FoquzPollAnswer $answer, AnswersResponse $answersResponse)
    {
        $this->answer = $answer;
        $this->answerResponse = $answersResponse;
        $this->buildResponse();
    }

    private function buildResponse(): void
    {
        $questions = $this->answerResponse->getPollQuestions($this->answer->foquz_poll_id);
        foreach ($questions as $question) {
            $answerItem = new AnswerItem($question->id, $this->answer, $this->answerResponse);
            $this->answerItems[] = $answerItem->getResponse();
        }
    }

    public function getResponse(): array
    {
        if ($this->answer->foquzPoll->point_system) {
            $points = [
                'value' => $this->answer->points ?: 0,
                'max' => $this->answer->max_points ?: 0,
            ];
        }

        return [
            'id' => md5($this->answer->id),
            'key'  => $this->answer->getAnswerKey() ?: '',
            'poll_id' => $this->answer->foquz_poll_id,
            'poll_name' => $this->answer->foquzPoll->name,
            'filial_name' => $this->answer->answerFilial->name ?? null,
            'poll_type' => $this->answer->foquzPoll->is_auto ? 'Автоматический' : 'Ручной',
            'answer_date' => date('d.m.Y H:i', strtotime($this->answer->updated_at)),
            'sent_date' => !empty($this->answer->sends[0]->sended) ? date('d.m.Y H:i', strtotime($this->answer->sends[0]->sended)) : null,
            'ip' => $this->answer->ip_address,
            'os' => $this->answer->os,
            'browser' => $this->answer->useragent,
            'device_type' => $this->answer->device,
            'fio' => $this->answer->contact ? trim(implode(' ', [$this->answer->contact->last_name, $this->answer->contact->first_name, $this->answer->contact->patronymic])) : null,
            'phone' => $this->answer->contact ? FoquzContact::formatPhone($this->answer->contact->phone) : null,
            'email' => $this->answer->contact->email ?? null,
            'customer_id'  => $this->answer->contact->company_client_id ?? null,
            'custom_fields' => json_decode($this->answer->custom_fields) ?? [],
            'status' => $this->answer->status,
            'processing_status' => $this->answer->processing->statusName ?? null,
            'language' => !empty($this->answer->language) ? $this->answer->pollLanguage->name : 'Русский',
            'user_agreement' => (bool) $this->answer->user_agreement,
            'points' => $points ?? null,
            'answers' => $this->answerItems,
            'answer_tags' => ArrayHelper::getColumn($this->answer->tags, 'tag'),
            'link' => $this->answer->linkQuote?->name,
            'quote' => $this->answer->answerItemQuote?->foquzPollAnswerQuotes?->name,
        ];
    }
}
