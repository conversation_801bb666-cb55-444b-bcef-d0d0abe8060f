<?php

namespace app\modules\foquz\models;

use yii\db\ActiveQuery;
use yii\db\ActiveRecord;
use yii\helpers\ArrayHelper;

/**
 * This is the model class for table "{{%foquz_poll_design}}".
 *
 * @property int $id
 * @property int $foquz_poll_id
 * @property string $background_image
 * @property string $mobile_background_image
 * @property string $logo_image
 * @property string $main_color
 * @property string $background_color
 * @property string $header_color
 * @property string|null $star_color Цвет звёзд
 * @property string|null $rating_color Цвет рейтинга
 * @property string|null $nps_color_from Цвет рейтинга NPS, от
 * @property string|null $nps_color_to Цвет рейтинга NPS, до
 * @property string|null $sem_diff_color_from Цвет рейтинга сем. дифференциала, от
 * @property string|null $sem_diff_color_to Цвет рейтинга сем. дифференциала, до
 * @property boolean $is_use_header
 * @property string $font_family
 * @property string $title_font_size
 * @property string $font_size
 * @property string $text_on_bg
 * @property string $text_on_place
 * @property string $link_color
 * @property int $from_template
 * @property string $logo_link
 * @property string $logo_type
 * @property string $logo_text
 * @property string $logo_font_family
 * @property string $logo_color
 * @property int $logo_position Позиционирование логотипа (1 - слева, 2 - по центру)
 * @property int $logo_margins Отступы сверху/снизу (для логотипа)
 * @property int $logo_height Высота логотипа
 * @property int $logo_text_size Размер текста для логотипа
 * @property boolean $logo_text_bold Жирный текст для логотипа
 * @property boolean $logo_text_italic Курсивный текст для логотипа
 * @property boolean $small_header_mobile Уменьшенный размер шапки для мобильных экранов (1 - да, 0 - нет)
 * @property string $back_text
 * @property string $back_button_background_color Цвет фона кнопок "Назад" и "Отчёт о тестировании"
 * @property string $back_button_text_color Цвет текста кнопок "Назад" и "Отчёт о тестировании"
 * @property string $back_button_stroke_color Цвет обводки кнопок "Назад" и "Отчёт о тестировании"
 * @property int $back_button_radius Радиус скругления кнопок "Назад" и "Отчёт о тестировании"
 * @property string $next_text
 * @property string $next_button_background_color Цвет фона кнопок "Вперёд" и "Завершить"
 * @property string $next_button_text_color Цвет текста кнопок "Вперёд" и "Завершить"
 * @property string $next_button_stroke_color Цвет обводки кнопок "Вперёд" и "Завершить"
 * @property int $next_button_radius Радиус скругления кнопок "Вперёд" и "Завершить"
 * @property string $start_button_background_color Цвет фона кнопок "Пройти опрос", "Начать заново" и "Готово"
 * @property string $start_button_text_color Цвет текста кнопок "Пройти опрос", "Начать заново" и "Готово"
 * @property string $start_button_stroke_color Цвет обводки кнопок "Пройти опрос", "Начать заново" и "Готово"
 * @property int $start_button_radius Радиус скругления кнопок "Пройти опрос", "Начать заново" и "Готово"
 * @property string|null $finish_text Текст для кнопки Завершить
 * @property string|null $finish_link Ссылка для кнопки Завершить
 * @property string $unrequired_text
 * @property boolean $show_process
 * @property boolean $show_numbers
 * @property boolean $darkening_background
 * @property string $place_under_buttons
 * @property boolean $show_prev_button
 * @property string $main_place_color
 * @property boolean $choose_language
 * @property boolean $full_width Показать прохождение на всю ширину экрана
 * @property boolean $disable_question_autoscroll Отключить автоматическую прокрутку вопросов на странице
 * @property boolean $in_use_cover Показ верхнего колонтитула
 * @property string|null $cover_image Изображение для верхнего колонтитула
 * @property int $cover_position Видимая область изображения для верхнего колонтитула
 * @property boolean $cover_only_first_page Показ верхнего колонтитула только на первой странице
 * @property boolean $cover_full_width Растянуть верхний колонтитул на всю ширину экрана
 *
 * @property FoquzPollDesignTemplate $template
 * @property FoquzPoll $foquzPoll
 */
class FoquzPollDesign extends BaseModel
{
    const TYPE_IMAGE = 'image';
    const TYPE_TEXT = 'text';

    const DEFAULT_BG_IMAGE = '/img/themes/background4.jpg';
    const DEFAULT_LOGO_IMAGE = '/img/poll-design__custom-logo.svg';

    const SHOW_NONE = null;
    const SHOW_QUESTION_NUMBERS = 1;
    const SHOW_PROGRESS_BAR = 2;

    public const LOGO_POSITION_LEFT = 1;
    public const LOGO_POSITION_CENTER = 2;

    const SHOW_PROCESS = [
        self::SHOW_NONE => 'Ничего',
        self::SHOW_QUESTION_NUMBERS => 'Отображать номера вопросов',
        self::SHOW_PROGRESS_BAR => 'Отображать прогресс бар',
    ];

    const COVER_POSITION_TOP = 0;
    const COVER_POSITION_CENTER = 1;
    const COVER_POSITION_BOTTOM = 2;
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%foquz_poll_design}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['foquz_poll_id'], 'required'],
            [
                [
                    'background_image', 'mobile_background_image', 'logo_image', 'main_color', 'background_color', 'header_color', 'font_family',
                    'title_font_size', 'font_size', 'text_on_bg', 'text_on_place', 'place_under_buttons', 'link_color', 'logo_link', 'logo_type',
                    'logo_text', 'logo_font_family', 'logo_color', 'main_place_color', 'back_button_background_color', 'back_button_text_color',
                    'back_button_stroke_color', 'next_button_background_color', 'next_button_text_color', 'next_button_stroke_color',
                    'start_button_background_color', 'start_button_text_color', 'start_button_stroke_color', 'cover_image',
                ], 'string', 'max' => 255],
            [['back_text', 'next_text', 'finish_text', 'unrequired_text'], 'string', 'max' => 40],
            [['star_color', 'rating_color', 'nps_color_from', 'nps_color_to', 'sem_diff_color_from', 'sem_diff_color_to'], 'string', 'max' => 7],
            [['finish_link'], 'string'],
            [['is_use_header', 'show_process', 'darkening_background', 'in_use_cover'], 'safe'],
            [[
                'show_prev_button', 'from_template', 'logo_margins', 'logo_height', 'logo_text_size', 'logo_position',
                'back_button_radius', 'next_button_radius', 'start_button_radius', 'cover_position',
            ], 'integer'],
            [['back_button_radius', 'next_button_radius', 'start_button_radius',], 'default', 'value' => 0],
            [['logo_position'], 'in', 'range' => [self::LOGO_POSITION_LEFT, self::LOGO_POSITION_CENTER]],
            [['cover_position'], 'in', 'range' => [self::COVER_POSITION_TOP, self::COVER_POSITION_CENTER, self::COVER_POSITION_BOTTOM]],
            [['logo_text_bold', 'logo_text_italic', 'small_header_mobile'], 'boolean'],
            ['show_process', 'in', 'range' => array_keys(self::SHOW_PROCESS)],
            [['foquz_poll_id'], 'exist', 'skipOnError' => true, 'targetClass' => FoquzPoll::className(), 'targetAttribute' => ['foquz_poll_id' => 'id']],
            [['choose_language', 'full_width', 'disable_question_autoscroll', 'cover_only_first_page', 'cover_full_width'], 'boolean'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'foquz_poll_id' => 'Foquz Poll ID',
            'background_image' => 'Background Image',
            'logo_image' => 'Logo Image',
            'main_color' => 'Основной цвет',
            'background_color' => 'Цвет фона',
            'header_color' => 'Цвет шапки',
            'star_color' => 'Цвет звёзд',
            'rating_color' => 'Цвет рейтинга',
            'nps_color_from' => 'Цвет рейтинга NPS, от',
            'nps_color_to' => 'Цвет рейтинга NPS, до',
            'sem_diff_color_from' => 'Цвет рейтинга сем. дифференциала, от',
            'sem_diff_color_to' => 'Цвет рейтинга сем. дифференциала, до',
            'finish_text' => 'Текст для кнопки Завершить',
            'finish_link' => 'Ссылка для кнопки Завершить',
            'unrequired_text' => 'Метка необязательного вопроса',
            'choose_language' => 'Выбор языка опроса',
            'full_width' => 'Показать прохождение на всю ширину экрана',
            'disable_question_autoscroll' => 'Отключить автоматическую прокрутку вопросов на странице',
            'logo_position' => 'Позиционирование логотипа (1 - слева, 2 - по центру)',
            'logo_margins' => 'Отступы сверху/снизу (для логотипа)',
            'logo_height' => 'Высота логотипа',
            'logo_text_size' => 'Размер текста для логотипа',
            'logo_text_bold' => 'Жирный текст для логотипа',
            'logo_text_italic' => 'Курсивный текст для логотипа',
            'small_header_mobile' => 'Уменьшенный размер шапки для мобильных экранов (1 - да, 0 - нет)',
            'back_button_background_color' => 'Цвет фона кнопок \"Назад\" и \"Отчёт о тестировании\"',
            'back_button_text_color' => 'Цвет текста кнопок \"Назад\" и \"Отчёт о тестировании\"',
            'back_button_stroke_color' => 'Цвет обводки кнопок \"Назад\" и \"Отчёт о тестировании\"',
            'back_button_radius' => 'Радиус скругления кнопок \"Назад\" и \"Отчёт о тестировании\"',
            'next_button_background_color' => 'Цвет фона кнопок \"Вперёд\" и \"Завершить\"',
            'next_button_text_color' => 'Цвет текста кнопок \"Вперёд\" и \"Завершить\"',
            'next_button_stroke_color' => 'Цвет обводки кнопок \"Вперёд\" и \"Завершить\"',
            'next_button_radius' => 'Радиус скругления кнопок \"Вперёд\" и \"Завершить\"',
            'start_button_background_color' => 'Цвет фона кнопок \"Пройти опрос\", \"Начать заново\" и \"Готово\"',
            'start_button_text_color' => 'Цвет текста кнопок \"Пройти опрос\", \"Начать заново\" и \"Готово\"',
            'start_button_stroke_color' => 'Цвет обводки кнопок \"Пройти опрос\", \"Начать заново\" и \"Готово\"',
            'start_button_radius' => 'Радиус скругления кнопок \"Пройти опрос\", \"Начать заново\" и \"Готово\"',
            'in_use_cover' => 'Показ обложки (верхнего колонтитула)',
            'cover_image' => 'Изображение для верхнего колонтитула',
            'cover_position' => 'Видимая область изображения для верхнего колонтитула',
            'cover_only_first_page' => 'Показ верхнего колонтитула только на первой странице',
            'cover_full_width' => 'Растянуть верхний колонтитул на всю ширину экрана',
        ];
    }

    public function fields()
    {
        return parent::fields()+[
            'backgroundImage' => function($model) {
                return $model->background_image ?? '';
            },
            'logo' => function($model) {
                return $model->logo_image ?? '';
            },
            'mainColor' => 'main_color',
            'logoLink' => 'logo_link',
            'bgColor' => 'background_color',
            'headerColor' => 'header_color',
            'starColor' => 'star_color',
            'ratingColor' => 'rating_color',
            'npsColorFrom' => 'nps_color_from',
            'npsColorTo' => 'nps_color_to',
            'semDiffColorFrom' => 'sem_diff_color_from',
            'semDiffColorTo' => 'sem_diff_color_to',
            'textOnBg' => 'text_on_bg',
            'textOnPlace' => 'text_on_place',
            'linkColor' => 'link_color',
            'fontFamily' => 'font_family',
            'titleFontSize' => 'title_font_size',
            'fontSize' => 'font_size',
            'isUseHeader' => function($model) {
                return (bool)$model->is_use_header;
            },
            'templateId' => 'from_template',
            'logoType' => 'logo_type',
            'logoText' => 'logo_text',
            'logoFontFamily' => 'logo_font_family',
            'logoColor' => 'logo_color',
            'backText' => 'back_text',
            'nextText' => 'next_text',
            'finishText' => 'finish_text',
            'finishLink' => 'finish_link',
            'show_process' => 'show_process',
            'placeUnderButtons' => 'place_under_buttons',
            'show_prev_button',
            'full_width',
            'disable_question_autoscroll',
        ];
    }

    /**
     * @return ActiveQuery
     */
    public function getTemplate()
    {
        return $this->hasOne(FoquzPollDesignTemplate::class, ['id' => 'from_template']);
    }

    /**
     * @return ActiveQuery
     */
    public function getFoquzPoll()
    {
        return $this->hasOne(FoquzPoll::className(), ['id' => 'foquz_poll_id']);
    }

    public function getLogo()
    {
        $logo = ($this->image ?: new FoquzThemeFile())->getBehavior('logoImage')->getUploadUrl('logo_image');

        if (empty($logo)) {
            $logo = '/design/foquz-logo-survey.svg';
        }

        return $logo;
    }

    public function getBackground($type = 'preview')
    {
        return $this->background_image ? : '/img/themes/background4.png';
        //return ($this->image ?: new FoquzThemeFile())->getBehavior('backgroundImage')->getThumbUploadUrl('background_image', $type);
    }

    public function getImage()
    {
        return $this->background_image;
    }

    public static function createDesignFromStandartTemplate(int $poll_id): array
    {
        $model = new FoquzPollDesign();
        $standardTemplate = FoquzPollDesignTemplate::find()->where(['company_id' => NULL])->one();

        $model->attributes = $standardTemplate->attributes;
        $model->from_template = $standardTemplate->id;
        $model->foquz_poll_id = $poll_id;
        $model->cover_position = self::COVER_POSITION_TOP;
        $model->save();
        return $model->toArray();
    }

    public function getUploadDirForBackgrounds(): string
    {
        return "uploads/foquz/poll/{$this->foquzPoll->id}/bg";
    }

    public function getUploadDirForLogos(): string
    {
        return "uploads/foquz/poll/{$this->foquzPoll->id}/logo";
    }

    public function getUploadDirForCovers(): string
    {
        return "uploads/foquz/poll/{$this->foquzPoll->id}/cover";
    }

    public function fillModelDefault(): void
    {
        $this->background_image = self::DEFAULT_BG_IMAGE;
        $this->logo_image = self::DEFAULT_LOGO_IMAGE;
        $this->main_color = 'rgba(63, 101, 241, 1)';
        $this->background_color = 'rgba(207, 216, 220, 1)';
        $this->header_color = '#000000';
        $this->star_color = '#F8CD1C';
        $this->rating_color = '#3F65F1';
        $this->nps_color_from = '#F96261';
        $this->nps_color_to = '#00C968';
        $this->sem_diff_color_from = '#73808D';
        $this->sem_diff_color_to = '#73808D';
        $this->is_use_header = 1;
        $this->font_family = 'Arial, Helvetica, sans-serif';
        $this->title_font_size = 30;
        $this->font_size = 14;
        $this->text_on_bg = 'rgba(255, 255, 255, 1)';
        $this->text_on_place = 'rgba(0, 0, 0, 1)';
        $this->link_color = 'rgba(255, 255, 255, 1)';
        $this->from_template = 1;
        $this->logo_link = 'http://foquz.ru';
        $this->logo_type = 'image';
        $this->unrequired_text = 'Необязательный';
        $this->show_process = 1;
        $this->darkening_background = 1;
        $this->place_under_buttons = 'dark';
        $this->show_prev_button = 1;
        $this->main_place_color = 'rgba(255, 255, 255, 1)';
        $this->choose_language = 1;
        $this->logo_position = self::LOGO_POSITION_LEFT;
        $this->logo_margins = 2;
        $this->logo_height = 40;
        $this->logo_text_size = 14;
        $this->small_header_mobile = true;
        $this->back_button_background_color = 'rgba(255, 255, 255, 0)';
        $this->back_button_text_color = 'rgba(255, 255, 255, 1)';
        $this->back_button_stroke_color = 'rgba(255, 255, 255, 1)';
        $this->back_button_radius = 24;
        $this->next_button_background_color = 'rgba(63, 101, 241, 0)';
        $this->next_button_text_color = 'rgba(255, 255, 255, 1)';
        $this->next_button_stroke_color = 'rgba(63, 101, 241, 1)';
        $this->next_button_radius = 24;
        $this->start_button_background_color = 'rgba(63, 101, 241, 1)';
        $this->start_button_text_color = 'rgba(255, 255, 255, 1)';
        $this->start_button_stroke_color = 'rgba(63, 101, 241, 1)';
        $this->start_button_radius = 24;
    }
}
