<?php

namespace app\modules\foquz\models;

use app\models\User;

/**
 * This is the model class for table "user_ip_filter".
 *
 * @property int $id
 * @property int $user_id ID пользователя
 * @property string $filter IP или CIDR фильтр
 * @property int $mode Режим фильтрации (1 - черный список, 2 - белый список)
 *
 * @property User $user
 */
class UserIpFilter extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'user_ip_filter';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['user_id', 'filter'], 'required'],
            [['user_id', 'mode'], 'integer'],
            [['filter'], 'string', 'max' => 64],
            [['user_id'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['user_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => 'ID пользователя',
            'filter' => 'IP или CIDR фильтр',
            'mode' => 'Режим фильтрации (1 - черный список, 2 - белый список)',
        ];
    }

    /**
     * Gets query for [[User]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUser()
    {
        return $this->hasOne(User::class, ['id' => 'user_id']);
    }
}
