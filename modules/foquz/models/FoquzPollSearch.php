<?php


namespace app\modules\foquz\models;


use app\helpers\DateTimeHelper;
use app\helpers\DevHelper;
use app\models\User;
use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use yii\db\Expression;
use yii\helpers\ArrayHelper;
use yii\helpers\Url;

/**
 * Class FoquzPollSearch
 * @package app\modules\foquz\models
 */
class FoquzPollSearch extends FoquzPoll
{
    public $viewType = 'grid';
    public $sort;
    public $withAnswer;
    public $archive;
    public $isFavorite;
    public $limit;
    public $author;
    public $company;
    public $openFolder;
    public $lastPolls;
    public $withoutTemplates;
    public $goalPolls;
    public $rawSql;
    public $isPager;
    public $offset;
    public $pollType;

    public function rules()
    {
        $rules = parent::rules();

        $rules['viewType'] = [['viewType', 'sort', 'status', 'author', 'pollType'], 'string'];
        $rules['integerType'] = [['limit', 'folder_id', 'offset', 'company'], 'integer'];
        $rules['withAnswer'] = [['withAnswer', 'archive', 'isFavorite', 'is_folder', 'openFolder', 'rawSql', 'lastPolls', 'goalPolls', 'isPager'], 'boolean'];

        return $rules;
    }

    private static $_statByPolls = null;

    private static function loadStatByPolls($companyId)
    {
        if (self::$_statByPolls===null) {
            self::$_statByPolls = [];
            $user = Yii::$app->user->identity;
            $filials = [];
            if($user && $user->isFilialEmployee() && $user->getUserFilials()->count() > 0) {
                $filials = ArrayHelper::getColumn($user->userFilials, 'filial_id');
            }

            $data = FoquzPoll::find()
                ->where(['foquz_poll.company_id' => $companyId]);

            if (!empty($filials)) {
                $data
                    ->select(["id2"=>"foquz_poll.id", "status"=>"a.status", "c"=>"count(*)"])
                    ->leftJoin('foquz_poll_answer a',  'a.foquz_poll_id=foquz_poll.id')
                    ->leftJoin('orders o', 'o.id = a.order_id')
                    ->andWhere(['NOT', ['a.id' => null]])
                    ->andWhere(['in', 'IF(foquz_poll.is_auto, o.filial_id, a.answer_filial_id)', $filials])
                    ->groupBy("id2, status")
                    ->asArray();
                $data = $data->all();

                foreach ($data as $row) {
                    if (!isset(self::$_statByPolls[$row['id2']])) {
                        self::$_statByPolls[$row['id2']] = [];
                    }
                    self::$_statByPolls[$row['id2']][$row['status']] = $row['c'];

                }
            } else {
                $data = $data
                    ->select([
                        'id',
                        'foquz_poll.sent_answers_count',
                        'opened_answers_count',
                        'in_progress_answers_count',
                        'filled_answers_count'
                    ])
                    ->asArray()
                    ->all();
                /** @var FoquzPoll $poll */
                foreach ($data as $poll) {
                    self::$_statByPolls[$poll['id']] = [
                        FoquzPollAnswer::STATUS_NEW => $poll['sent_answers_count'],
                        FoquzPollAnswer::STATUS_OPEN => $poll['opened_answers_count'],
                        FoquzPollAnswer::STATUS_IN_PROGRESS => $poll['in_progress_answers_count'],
                        FoquzPollAnswer::STATUS_DONE => $poll['filled_answers_count'],
                    ];
                }
            }
        }
    }

    public static function statByPoll($id, $key)
    {
        self::loadStatByPolls(Yii::$app->user->identity->company->id);
        $sum = self::$_statByPolls[$id][FoquzPollAnswer::STATUS_DONE] ?? 0;
        if (in_array($key, ['viewCounter', 'answerCounter', 'totalSendCount'])) {
            $sum += self::$_statByPolls[$id][FoquzPollAnswer::STATUS_IN_PROGRESS] ?? 0;
        }
        if (in_array($key, ['viewCounter', 'totalSendCount'])) {
            $sum += self::$_statByPolls[$id][FoquzPollAnswer::STATUS_OPEN] ?? 0;
        }
        if (in_array($key, ['totalSendCount'])) {
            //print_r(@self::$_statByPolls[$id]);
            $sum += isset(self::$_statByPolls[$id][FoquzPollAnswer::STATUS_EMAIL_OPEN]) ? self::$_statByPolls[$id][FoquzPollAnswer::STATUS_EMAIL_OPEN] : 0;
            $sum += self::$_statByPolls[$id][FoquzPollAnswer::STATUS_NEW] ?? 0;
        }
        return $sum;

    }

    public function search2($params)
    {





        $this->load($params);

        $user = Yii::$app->user->identity;
        $filials = []; $allFilials = false;
        if($user && $user->isFilialEmployee()) {
            if ($user->getUserFilials()->count() > 0) {
                $filials = ArrayHelper::getColumn($user->userFilials, 'filial_id');
            } else {
                $filials = ArrayHelper::getColumn(\app\models\Filial::find()->where(["company_id"=>Yii::$app->user->identity->company->id])->all(), "id");
                $allFilials = true;
            }
        }


        $query = self::find()
            ->alias('FP')
            ->distinct()
            ->select([
                'FP.id',
                'FP.name',
                'FP.created_at',
                'FP.is_tmp',
                'FP.is_published',
                'FP.status',
                'FP.is_active',
                'FP.is_auto',
                'FP.folder_id',
                'FP.goals_count',
                'FP.created_by',
                'FP.is_folder',
                'FP.datetime_start',
                'FP.datetime_end',
                'FP.description',
                'FP.answers_count',
             //   "viewCounter" => 'SUM(IF(a.status IN ("' . FoquzPollAnswer::STATUS_OPEN . '","' . FoquzPollAnswer::STATUS_IN_PROGRESS . '","' . FoquzPollAnswer::STATUS_DONE . '"), 1, 0))',
             //   "answerCounter" => 'SUM(IF(a.status IN ("' . FoquzPollAnswer::STATUS_DONE . '","' . FoquzPollAnswer::STATUS_IN_PROGRESS . '"), 1, 0))',
             //   "totalSendCount" => 'SUM(IF(a.id IS NOT NULL, 1, 0))',
             //   "totalDone" => 'SUM(IF(a.status IN ("' . FoquzPollAnswer::STATUS_DONE . '"), 1, 0))',
                'userName' => new Expression("
                    case when 
                        CB.name IS NULL or CB.name = ''
                            then 
                        CB.username
                    else 
                        CB.name
                    end
                "),
                'favorite' => new Expression('
                (
                    SELECT 1 FROM ' . FoquzPollFavorite::tableName() . ' 
                    WHERE foquz_poll_id=FP.id AND user_id=' . Yii::$app->getUser()->getId() . '
                )'),
            ])
            ->where([
                'FP.is_tmp' => false,
                'FP.deleted' => 0,
                'FP.status' => $this->status,
                'FP.company_id' => Yii::$app->user->identity->company->id,
            ])
            ->asArray()
            ->joinWith('createdBy CB');
            //->leftJoin("foquz_poll_answer a", "FP.id=a.foquz_poll_id")
            //->leftJoin('orders o', 'o.id = a.order_id')
            //->groupBy("FP.id");


        $addAnswers = false;
        if(count($filials) > 0) {
            $query->leftJoin("foquz_poll_answer a", "FP.id=a.foquz_poll_id")->leftJoin('orders o', 'o.id = a.order_id');

            $wc = [
                    "or",
                    ['in', 'IF(FP.is_auto, o.filial_id, a.answer_filial_id)', $filials],
                    "is_folder=1"
            ];
            if ($allFilials) $wc[] = "IF(FP.is_auto, o.filial_id, a.answer_filial_id) is null";
            $query
                ->andWhere($wc)
                ->groupBy('FP.id')
                ->having(new Expression("COUNT(a.id) > 0 or is_folder=1"));
        }


        if ($this->isFavorite) {
            $query->andWhere(['FP.id' => ArrayHelper::map(FoquzPollFavorite::findAll([
                'user_id' => Yii::$app->getUser()->getId()
            ]), 'foquz_poll_id', 'foquz_poll_id')]);
        }


        $editorFoldersIds = null;
        if ($user->isEditor() || $user->isWatcher()) {
            $editorFoldersIds = FoquzPoll::getEditorFolderIdsAll();
        }

        if (!$this->openFolder) {
            if ($this->folder_id) {
                if ($this->is_folder) {
                    $query->andWhere(['FP.id' => $this->folder_id]);
                } else {
                    $query->andWhere(['FP.folder_id' => $this->folder_id]);
                }
            }

            // отображение только верхнего уровня папок
            if ($this->is_folder) {
                $query->andWhere(['FP.folder_id' => null]);
            }

            $query->andFilterWhere(['is_folder' => $this->is_folder]);

            if ($this->goalPolls) {
                $query->andWhere(['not', ['FP.goals_count' => null]]);
                $query->andWhere(new Expression('
                            (
                                SELECT COUNT(*) from foquz_poll_answer FPA 
                                WHERE FPA.foquz_poll_id=FP.id AND FPA.status IN ("' . FoquzPollAnswer::STATUS_DONE . '","' . FoquzPollAnswer::STATUS_IN_PROGRESS . '")
                            )'). ' >= FP.goals_count');
            }

            if ($user->isEditor() || $user->isWatcher()) {
                $query->andWhere([
                    "or",
                    ["FP.is_folder"=>0, 'FP.folder_id'=>$editorFoldersIds["folders"]],
                    [
                        "and",
                        ["FP.is_folder"=>1],
                        [
                            "or",
                            ['FP.id'=>$editorFoldersIds["folders"]],
                            ['FP.id'=>$editorFoldersIds["parents"]],

                        ]
                    ]
                ]);

                /*
                if ($this->is_folder) {
                    $query->andWhere(['FP.id' => $editorFolders['all']]);
                    $query->andWhere(['NOT IN', 'FP.id', $editorFolders['exclude']]);
                } else {
                    $query->andWhere(['FP.folder_id' => EditorFolder::find()->select('folder_id')->where(['user_id' => $user->id])->column()]);
                }*/
            }

        } else {
            if ($user->isEditor() || $user->isWatcher()) {
                $query->andWhere([
                    "or",
                    ["FP.is_folder"=>0, 'FP.folder_id'=>$editorFoldersIds["folders"]],
                    [
                        "and",
                        ["FP.is_folder"=>1],
                        [
                            "or",
                            ['FP.id'=>$editorFoldersIds["folders"]],
                            ['FP.id'=>$editorFoldersIds["parents"]],

                        ]
                    ]
                ]);

/*

                $query->andWhere(['NOT IN', 'FP.folder_id', $editorFolders['exclude']]);
                $query->andWhere(['NOT IN', 'FP.id', $editorFolders['exclude']]);
                if (!in_array($this->folder_id, EditorFolder::find()->select('folder_id')->where(['user_id' => $user->id])->column())) {
                    $query->andWhere(['AND', ['FP.folder_id' => $editorFolders['all']], ['FP.is_folder' => true]]);
                }*/
            }
            $query->andWhere(['FP.folder_id' => $this->folder_id]);
        }

        if($this->withoutTemplates) {
            $query->andWhere(['FP.is_template' => false]);
        }

        if ($this->isFavorite) {
            $query->andWhere(new Expression('
                (
                    SELECT 1 FROM ' . FoquzPollFavorite::tableName() . ' 
                    WHERE foquz_poll_id=FP.id AND user_id=' . Yii::$app->getUser()->getId() . '
                )') . ' IS NOT NULL');
        }

        $orderByParams = [];

        if ($this->pollType === 'lastPolls') {
            unset($orderByParams['favorite']);
        }

        switch ($this->author) {
            case 'all':
                break;
            case 'mine':
                $query->andWhere(['FP.created_by' => Yii::$app->getUser()->getId()]);
                break;
            default:
                if (!empty($this->author)) {
                    $query->andWhere(['FP.created_by' => $this->author]);
                }
                break;
        }

        if($this->company) {
            $query->andWhere(['company_id' => $this->company]);
        }


        if ($this->withAnswer === 'true') {
            $query->andWhere(['OR', [">", "FP.in_progress_answers_count", 0], [">", "FP.filled_answers_count", 0]]);
        }

        if ($this->archive) {
            $query->andWhere(['FP.status' => FoquzPoll::STATUS_ARCHIVE]);
        }


        if (!$this->isPager && $this->limit) {
            $query->limit($this->limit);
        }


        $queryCount = clone $query;

        switch ($this->sort) {
            case 'name':
                $query
                    ->orderBy(array_merge($orderByParams, ['FP.name' => SORT_ASC]));
                break;
            case 'answer':
                $query
                    ->orderBy(array_merge($orderByParams, ['(in_progress_answers_count + filled_answers_count)' => SORT_DESC, 'FP.created_at' => SORT_DESC]));
                break;
            case 'view':
                $query->orderBy(array_merge($orderByParams, ['(opened_answers_count + in_progress_answers_count + filled_answers_count)' => SORT_DESC]));
                break;
            case 'created_at':
            default:
                $query
                    ->orderBy(array_merge($orderByParams, ['FP.id' => SORT_DESC]));
                break;
        }

        if ($this->isPager) {
            $query->offset($this->limit * $this->offset);
            $query->limit($this->limit);
        }

        if (!$this->isPager && !$this->limit) {
            $query->limit(4);
        }

        if ($this->rawSql) {
            qx($query);
        }

        switch ($this->pollType) {
            case 'lastPolls':
                $query->offset(0);
                $query->limit(12);
                $queryCount->limit(12);
                break;
        }

        $results = $query->all();
        foreach ($results as $key => $result) {
            $results[$key]['favorite'] = ($result['favorite'] == '1' ? true : false);

            $totalSendCount = self::statByPoll($results[$key]['id'], 'totalSendCount'); //$results[$key]["totalSendCount"];
            $totalOpenCount = self::statByPoll($results[$key]['id'], 'viewCounter'); //$results[$key]["viewCounter"];
            $totalInProgress = self::statByPoll($results[$key]['id'], 'answerCounter'); //$results[$key]["answerCounter"];
            $totalDone =  self::statByPoll($results[$key]['id'], 'totalDone');// $results[$key]["totalDone"];
            $currentGoal = self::statByPoll($results[$key]['id'], 'answerCounter'); //$results[$key]["answerCounter"];

            //$totalSendCount = $results[$key]["totalSendCount"];
            //$totalOpenCount = $results[$key]["viewCounter"];
            //$totalInProgress = $results[$key]["answerCounter"];
            //$totalDone =  $results[$key]["totalDone"];
            //$currentGoal = $results[$key]["answerCounter"];


            $user = User::findOne($result['created_by']);
            $results[$key]['createdBy'] = null;
            $results[$key]['link'] = Url::to(FoquzPoll::findOne($result['id'])->getViewUrl());
            $results[$key]['created_at'] = date('d.m.Y', $results[$key]['created_at']);
            $results[$key]['userAvatar'] = $user ? $user->getThumbUploadUrl('avatar', 'preview') : null;
            $results[$key]['totalSendCount'] = $totalSendCount;
            $results[$key]['totalOpenCount'] = $totalOpenCount;
            $results[$key]['totalInProgress'] = $totalInProgress;
            $results[$key]['isArchived'] = $result['status'] == FoquzPoll::STATUS_ARCHIVE ? true : false;
            $results[$key]['totalDone'] = $totalDone;
            $results[$key]['currentGoal'] = $currentGoal;
            $results[$key]['totalGoal'] = $result['goals_count'];
            $results[$key]['backgroundImage'] = '/img/themes/background4.png';
            $results[$key]['folderId'] = (int)$result['folder_id'];
            $results[$key]['childIds'] = FoquzPoll::getChildPollIds((int)$result['id']);
            $results[$key]['datetime_start'] = DateTimeHelper::addGMT3($result['datetime_start']);
            $results[$key]['datetime_end'] = DateTimeHelper::addGMT3($result['datetime_end']);
            if ((Yii::$app->user->identity->isEditor() || Yii::$app->user->identity->isWatcher())  && $result['is_folder']) {
                $inactive = false;
                //if (!in_array($result['folder_id'], array_diff($editorFolders['all'], $editorFolders['exclude']))) {
                  //  $inactive = true;
                //}
                $results[$key]['inactive'] = $inactive;
            }

            $poll = null;

            if ((int)$result['is_folder'] === 1) {
                $poll = FoquzPoll::find()->where(['folder_id' => $result['id'], 'is_tmp' => 0, 'deleted' => 0])->orderBy(['updated_at' => SORT_DESC])->one();

                FoquzPoll::$folderPollCount = 0;
                $results[$key]['totalPoll'] = FoquzPoll::getRecursiveCount($result['id'], $filials, $allFilials, Yii::$app->user->identity->isEditor() || Yii::$app->user->identity->isWatcher() ? $editorFoldersIds["folders"] : null);

                FoquzPoll::$folderChildIds = [];

                $customDesign = $poll ? FoquzPollDesign::findOne(['foquz_poll_id' => $poll->id]) : new FoquzPollDesign();
            } else {
                $customDesign = FoquzPollDesign::findOne(['foquz_poll_id' => $result['id']]);

                $results[$key]['folderName'] = ArrayHelper::getValue(FoquzPoll::findOne($result['folder_id']), 'name');
            }

            if ($customDesign && !empty($customDesign->getBackground())) {
                $results[$key]['backgroundImage'] = $customDesign->getBackground();
            } else {
                if ((int)$result['is_folder'] !== 1) {
                    $results[$key]['backgroundImage'] = '/img/themes/background4.png';
                }
            }

        }

        return [
            'results' => array_values($results),
            'pagerCount' => count($results), //$query->count(),
            'counter' => $queryCount->count(),
        ];
    }
}
