<?php

if (isset($_GET['lang'])) {
    \Yii::$app->language = $_GET['lang'];
}

/**
 * @var $this yii\web\View
 * @var $model webvimark\modules\UserManagement\models\forms\LoginForm
 */

use app\modules\foquz\models\LegalEntity;
use yii\helpers\Url;
use yii\helpers\Json;
use webvimark\modules\UserManagement\components\GhostHtml;
use webvimark\modules\UserManagement\UserManagementModule;
use yii\web\View;

$signUp = $_GET['signup'] ?? 0;

$privacyPolicyLink = LegalEntity::defaultDocuments()['policy'];
$termsOfUseLink = LegalEntity::defaultDocuments()['terms'];

if (isset($policy)) {
    $privacyPolicyLink = Url::base(true) . $policy;
}

if (isset($terms)) {
    $termsOfUseLink = Url::base(true) . $terms;
}

$this->registerJs("
    window.MODEL = " . Json::encode($model) . ";
    window.ERRORS = " . Json::encode($model->errors) . ";
    window.APP_LANG = '" . \Yii::$app->language . "';
", $this::POS_HEAD);

$this->registerJSFile('/js/login.main.js', ['depends' => [app\modules\foquz\assets\FoquzAsset::className()]]);


if (isset(Yii::$app->params['smart_captcha_client_key']) && Yii::$app->request->hostName !== 'localhost') {
    $this->registerJsFile('https://smartcaptcha.yandexcloud.net/captcha.js?render=onload&onload=onloadFunction',['position' => View::POS_BEGIN, 'async'=>true, 'defer'=>true]);
    $captchaClientKey = Yii::$app->params['smart_captcha_client_key'];
    $captcha = <<<JS
    function onloadFunction() {
        if (!window.smartCaptcha) return;

        window.smartCaptchaWidgetId = window.smartCaptcha.render('captcha-container', {
            sitekey: '{$captchaClientKey}',
            invisible: true,
            hideShield: true
        });
    }
    JS;

    $this->registerJs($captcha, View::POS_BEGIN);
}

$this->title = "Регистрация и вход - FOQUZ";

?>

<noscript>
    <div><img src="https://mc.yandex.ru/watch/64508650" style="position:absolute; left:-9999px;" alt="" /></div>
</noscript>
<!-- /Yandex.Metrika counter -->

<div class="app app--initializing login-page">
    <div class="app__body">

        <div class="app__content">

            <div class="login " data-bind="descendantsComplete: $root.onInit.bind($root)">

                <header class="login-page__header">
                    <div class="login-page__logo">
                        <a class="s-app__logo mr-50p" href="<?= Yii::$app->params['protocol'] ?>://<?= substr(Yii::$app->params['company_domen'], 1) ?>">
                            <img src="<?= Url::to(['/img/logo-ru.svg']) ?>" alt="" width="94">
                            <?php /* <img src="<?= Url::to(['/img/logo/logo-ny.svg']) ?>" alt="" width="94">  */ ?>
                        </a>                    </div>
                    <div class="login-page__title">
                        <?= \Yii::t('user', 'Вход / регистрация') ?>
                    </div>
                </header>

                <div class="login-page__content">
                    <div class="tab-group">
                        <nav class="nav nav-tabs">
                            <a
                                class="nav-item nav-link <?= $signUp ? '' : 'active' ?>"
                                id="nav-common-tab"
                                data-toggle="tab"
                                href="#login"
                                role="tab"
                                aria-controls="nav-login"
                                aria-selected="<?= $signUp ? 'false' : 'true' ?>"
                                data-bind="click: () => $root.isSubmitted(false)"
                            >
                                <?= \Yii::t('user', 'Вход') ?>
                            </a>

                            <a
                                class="nav-item nav-link <?= $signUp ? 'active' : '' ?>"
                                id="nav-inter-screens-tab"
                                data-toggle="tab"
                                href="#registration"
                                role="tab"
                                aria-controls="nav-registration"
                                aria-selected="<?= $signUp ? 'true' : 'false' ?>"
                                data-bind="click: () => $root.isSubmitted(false)"
                            >
                                <?= \Yii::t('user', 'Регистрация') ?>
                            </a>
                        </nav>

                        <div class="tab-content" id="nav-tabContent">
                            <div class="tab-pane <?= $signUp ? '' : 'show active' ?>" id="login" role="tabpanel" aria-labelledby="nav-login-tab" data-bind="using: loginFormModel">

                                <div class="login__body">

                                    <form id="login-form" method="post" action="/user-management/auth/login">
                                        <input id="csrf" type="hidden" name="<?= Yii::$app->request->csrfParam ?>" value="<?= Yii::$app->request->getCsrfToken() ?>" />
                                        <div class="form-group">
                                            <label class="form-label"><?= \Yii::t('main', 'Логин / Email') ?></label>
                                            <input class="form-control" name="username" autocomplete="off" data-bind="textInput: name, css: {
                                                    'is-invalid': $root.formControlErrorStateMatcher(name),
                                                    'is-valid': $root.formControlSuccessStateMatcher(name)
                                                }">
                                                <!-- ko if: $root.formControlErrorStateMatcher(name) -->
                                                <div class="form-error" data-bind="text: name.error()"></div>
                                                <!-- /ko -->
                                            <?php if (isset($model->errors['username']) && strlen($model->username) > 0) : ?>
                                                <div class="form-error"><?= $model->errors['username'][0] ?></div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label"><?= \Yii::t('main', 'Пароль') ?></label>
                                            <input class="form-control" name="password" type="password" autocomplete="off" data-bind="textInput: password, css: {
                                                                'is-invalid': $root.formControlErrorStateMatcher(password),
                                                                'is-valid': $root.formControlSuccessStateMatcher(password)
                                                            }">
                                            <!-- ko if: $root.formControlErrorStateMatcher(password) -->
                                                <div class="form-error" data-bind="text: password.error()"></div>
                                            <!-- /ko -->
                                            <?php if (isset($model->errors['password']) && is_string($model->password) && strlen($model->password) > 0) : ?>
                                                <div class="form-error"><?= $model->errors['password'][0] ?></div>
                                            <?php endif; ?>
                                        </div>

                                        <div class="login__controls">
                                            <div class="d-flex flex-wrap justify-content-between align-items-center flex-grow-1">
                                                <div class="form-check mr-3">
                                                    <input class="form-check-input" name="rememberMe" id="rememberMe" type="checkbox" checked>
                                                    <label class="form-check-label" for="rememberMe"><?= \Yii::t('user', 'Запомнить меня') ?></label>
                                                </div>
                                                <a href="<?= Url::to('/user-management/auth/password-recovery') ?>" class="mr-4 recovery-link"><?= \Yii::t('user', 'Восстановить пароль') ?></a>
                                            </div>

                                            <div class="">
                                                <button
                                                    class="login-button f-btn f-btn-primary f-btn-lg"
                                                    type="button"
                                                    data-bind="
                                                        click: (_,event) => $root.submit(),
                                                        attr: {
                                                            'disabled': $root.isSubmitted()
                                                                && ($root.formControlErrorStateMatcher(password) || $root.formControlErrorStateMatcher(name))
                                                        }
                                                    "
                                                >
                                                    <?= \Yii::t('user', 'Войти') ?>
                                                </button>
                                            </div>

                                        </div>
                                    </form>
                                </div>

                            </div>

                            <div class="tab-pane <?= $signUp ? 'show active' : '' ?>" id="registration" role="tabpanel" aria-labelledby="nav-registration-tab">
                                <div class="login__body login__body_long">
                                    <div class="login__some-form">
                                        <!-- ko template: {
                                            foreach: templateIf($root.activeRegistrationStep()===0, $data),
                                            afterAdd: fadeAfterAddFactory(450,200),
                                            beforeRemove: fadeBeforeRemoveFactory(200)
                                        } -->
                                        <div data-bind="using: registrationFormModelStep1">
                                            <div
                                                class="form-group"
                                                data-bind="
                                                    css: {
                                                        'is-invalid': $root.formControlErrorStateMatcher(email),
                                                        'is-valid': $root.formControlSuccessStateMatcher(email)
                                                    }
                                                "
                                            >
                                                <fc-label
                                                    params="
                                                        text: '<?= \Yii::t('main', 'Email') ?>',
                                                        hint: '<?= \Yii::t('main', 'Email') ?>'
                                                    "
                                                ></fc-label>
                                                <input
                                                    id="registration-email"
                                                    name="registration-email"
                                                    class="form-control"
                                                    data-bind="textInput: email"
                                                >
                                                <!-- ko if: $root.formControlErrorStateMatcher(email) -->
                                                <div class="form-error" data-bind="text: email.error()"></div>
                                                <!-- /ko -->
                                                <!-- ko if: $root.serverErrors.email() -->
                                                <div class="form-error" data-bind="text: $root.serverErrors.email()"></div>
                                                <!-- /ko -->
                                            </div>
                                            <div class="row">
                                                <div class="form-group col-12 col-md-6">
                                                    <fc-label
                                                        params="
                                                            text: 'Пароль',
                                                            hint: 'Пароль'
                                                        "
                                                    ></fc-label>
                                                    <password-field
                                                        data-bind="
                                                            css: {
                                                                'is-invalid': $root.formControlErrorStateMatcher(password),
                                                                'is-valid': $root.formControlSuccessStateMatcher(password)
                                                            }
                                                        "
                                                        params="
                                                            value: password,
                                                            help: true,
                                                            isValid: $root.registrationSteps.passwordIsValid,
                                                            showErrorMsg: $root.formControlErrorStateMatcher(password),
                                                        "
                                                    ></password-field>
                                                </div>
                                                <div class="form-group col-12 col-md-6">
                                                    <fc-label
                                                        params="
                                                            text: 'Повторите пароль',
                                                            hint: 'Повторите пароль'
                                                        "
                                                    ></fc-label>
                                                    <password-field
                                                        data-bind="
                                                            css: {
                                                                'is-invalid': $root.formControlErrorStateMatcher(retypePassword),
                                                                'is-valid': $root.formControlSuccessStateMatcher(retypePassword)
                                                            }
                                                        "
                                                        params="
                                                            value: retypePassword
                                                        "
                                                    ></password-field>
                                                    <!-- ko if: $root.formControlErrorStateMatcher(retypePassword) -->
                                                    <div class="form-error" data-bind="text: retypePassword.error()"></div>
                                                    <!-- /ko -->
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <foquz-checkbox
                                                    params="checked: isConfirm"
                                                    data-bind="css: { 'is-invalid': $root.formControlErrorStateMatcher(isConfirm) }"
                                                >

                                                    <?= \Yii::t(
                                                        'user',
																												'Принимаю {agreement} и даю согласие на {approval}',
                                                        [
                                                            'agreement' => '<a href="' . $termsOfUseLink . '" target="_blank" download class="strong">' . \Yii::t('main', 'пользовательское соглашение') . '</a>',
                                                            'approval' => '<a href="' . $privacyPolicyLink . '" target="_blank" download class="strong">' . \Yii::t('user', 'обработку персональных данных') . '</a>'
                                                        ]
                                                    ) ?>

                                                </foquz-checkbox>

                                                <validation-feedback
                                                    params="show: $root.formControlErrorStateMatcher(isConfirm), text: isConfirm.error"
                                                ></validation-feedback>
                                            </div>

																						<div class="form-group">
                                                <foquz-checkbox
                                                    params="checked: isProcessingPolicy"
                                                    data-bind="css: { 'is-invalid': $root.formControlErrorStateMatcher(isProcessingPolicy) }"
                                                >

                                                    <?= \Yii::t(
                                                        'user',
																												'Соглашаюсь с {processingPolicy}',
                                                        [
                                                            'processingPolicy' => '<a href="https://foquz.ru/PPD.pdf" target="_blank" class="strong">' . \Yii::t('main', 'политикой обработки персональных данных') . '</a>',
                                                        ]
                                                    ) ?>

                                                </foquz-checkbox>

                                                <validation-feedback
                                                    params="show: $root.formControlErrorStateMatcher(isProcessingPolicy), text: isProcessingPolicy.error"
                                                ></validation-feedback>
                                            </div>

                                            <div>
                                                <fc-button
                                                    class="w-100"
                                                    params="
                                                        label: _t('Продолжить'),
                                                        size: 'full',
                                                        color: 'primary',
                                                        pending: $root.pending,
                                                        disabled: $root.hasServerErrors()
                                                            || ($root.isSubmitted() && !$root.registrationFormModelStep1.isValid()),
                                                        click: () => $root.submitStep(),
                                                    "
                                                ></fc-button>
                                            </div>
                                        </div>
                                        <!-- /ko -->
                                        
                                        <!-- ko template: {
                                            foreach: templateIf($root.activeRegistrationStep() === 1, $data),
                                            afterAdd: fadeAfterAddFactory(450,200),
                                            beforeRemove: fadeBeforeRemoveFactory(200)
                                        } -->
                                        <div data-bind="using: registrationFormModelStep2">
                                            <div
                                                class="form-group"
                                                data-bind="
                                                    css: {
                                                        'is-invalid': $root.formControlErrorStateMatcher(name),
                                                        'is-valid': $root.formControlSuccessStateMatcher(name)
                                                    },
                                                "
                                            >
                                                <fc-label
                                                    params="
                                                        text: _t('main', 'ФИО'),
                                                        optional: true
                                                    "
                                                ></fc-label>
                                                <input
                                                    class="form-control"
                                                    data-bind="textInput: name"
                                                >
                                            </div>

                                            <div class="row">
                                                <div
                                                    class="form-group col-12 col-md-6"
                                                    data-bind="
                                                        css: {
                                                            'is-invalid': $root.formControlErrorStateMatcher(phone),
                                                            'is-valid': $root.formControlSuccessStateMatcher(phone)
                                                        }
                                                    "
                                                >
                                                    <fc-label
                                                        params="text: _t('main', 'Телефон')"
                                                    ></fc-label>
                                                    <input
                                                        id="phone"
                                                        class="form-control"
                                                        type="text"
                                                        name="phone"
                                                        data-bind="
                                                            phoneInput,
                                                            textInput: phone,
                                                        "
                                                    >
                                                    <!-- ko if: $root.formControlErrorStateMatcher(phone) -->
                                                    <div class="form-error" data-bind="text: phone.error()"></div>
                                                    <!-- /ko -->
                                                    <!-- ko if: $root.serverErrors.phone() -->
                                                    <div class="form-error" data-bind="text: $root.serverErrors.phone()"></div>
                                                    <!-- /ko -->
                                                </div>
                                            </div>

                                            <div>
                                                <fc-button
                                                    class="w-100"
                                                    params="
                                                        label: _t('user', 'Зарегистрироваться'),
                                                        size: 'full',
                                                        color: 'primary',
                                                        disabled: $root.hasServerErrors()
                                                            || ($root.isSubmitted() && !$root.registrationFormModelStep2.isValid()),
                                                        pending: $root.pending,
                                                        click: (event) => {
                                                            ym(64508650,'reachGoal','registaracia')
                                                            $root.submitStep();
                                                        }
                                                    "
                                                ></fc-button>
                                            </div>
                                        </div>
                                        <!-- /ko -->
                                        <div class="form-group" id="captcha-container"></div>
                                    </div>

                                </div>

                                <script>
                                    var isNumberKey = function(evt) {
                                        var charCode = (evt.which) ? evt.which : event.keyCode
                                        if (charCode > 31 && (charCode < 48 || charCode > 57))
                                            return false;

                                        return true;
                                    }
                                </script>

                            </div>
                        </div>
                    </div>
                </div>


            </div>

        </div>
    </div>
    <?= $this->render('@app/modules/foquz/views/layouts/_footer.php', ['footerMode' => 'login']); ?>
</div>