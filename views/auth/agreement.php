<?php

use app\modules\foquz\models\LegalEntity;
use yii\helpers\Html;
use yii\helpers\Url;


$privacyPolicyLink = LegalEntity::defaultDocuments()['policy'];
$termsOfUseLink = LegalEntity::defaultDocuments()['terms'];

if (isset($policy)) {
    $privacyPolicyLink = Url::base(true) . $policy;
}

if (isset($terms)) {
    $termsOfUseLink = Url::base(true) . $terms;
}

$this->title = "Персональные данные";

$this->registerJSFile('/js/login.agreement.js', ['depends' => [app\modules\foquz\assets\FoquzAsset::className()]]);

?>
<div class="app app--initializing">
    <div class="app__body">
        <div class="app__content agreement-content">
            <div class="agreement">
                <div class="agreement__login">
                    <div class="login-page__logo">
                        <a class="s-app__logo mr-50p" href="<?= Yii::$app->params['protocol'] ?>://<?= substr(Yii::$app->params['company_domen'], 1) ?>">
                            <img src="<?= Url::to(['/img/logo-ru.svg']) ?>" alt="" width="94">
                        </a>
                    </div>
                </div>

                <div class="agreement-form tab-content" data-bind="using: agreementFormModel">
                    <div class="agreement-form__container">
                        <h2 class="agreement-form__title">Персональные данные</h2>
                        <form id="agreement-form" method="post" action="<?= Url::to(['/user-management/auth/agreement']) ?>">
                            <input id="csrf" type="hidden" name="<?= Yii::$app->request->csrfParam ?>" value="<?= Yii::$app->request->csrfToken ?>">
                            <div class="form-group">
                                <div class="mb-15p">
                                    <foquz-checkbox
                                        params="checked: accept_user_agreement"
                                        data-bind="css: { 'is-invalid': $root.formControlErrorStateMatcher(accept_user_agreement) }"
                                    >
                                        <?= \Yii::t(
                                            'user',
                                            'Принимаю {agreement} и даю согласие на {approval}',
                                            [
                                                'agreement' => '<a href="' . $termsOfUseLink . '" target="_blank" download class="strong">' . \Yii::t('main', 'пользовательское соглашение') . '</a>',
                                                'approval' => '<a href="' . $privacyPolicyLink . '" target="_blank" download class="strong">' . \Yii::t('user', 'обработку персональных данных') . '</a>'
                                            ]
                                        ) ?>

                                    </foquz-checkbox>
                                    <validation-feedback
                                        params="show: $root.formControlErrorStateMatcher(accept_user_agreement), text:  accept_user_agreement.error"
                                    ></validation-feedback>
                                </div>

                                <div>
                                    <foquz-checkbox
                                        params="checked: accept_personal_data_policy"
                                        data-bind="css: { 'is-invalid': $root.formControlErrorStateMatcher(accept_personal_data_policy) }"
                                    >
                                        <?= \Yii::t(
                                            'user',
                                            'Соглашаюсь с {processingPolicy}',
                                            [
                                                'processingPolicy' => '<a href="https://foquz.ru/PPD.pdf" target="_blank"   class="strong">' . \Yii::t('main', 'политикой обработки персональных данных') . '</a>',
                                            ]
                                        ) ?>

                                    </foquz-checkbox>
                                    <validation-feedback
                                        params="show: $root.formControlErrorStateMatcher(accept_personal_data_policy), text:    accept_personal_data_policy.error"
                                    ></validation-feedback>
                                </div>
                            </div>

                            <div>
                                <fc-button
                                    class="w-100"
                                    params="
                                        label: _t('Подтвердить'),
                                        size: 'full',
                                        color: 'primary',
                                        disabled: $root.isButtonDisabled(),
                                        click: () => $root.submit(),
                                    "
                                ></fc-button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?= $this->render('@app/modules/foquz/views/layouts/_footer.php', ['footerMode' => 'login']); ?>
</div>
