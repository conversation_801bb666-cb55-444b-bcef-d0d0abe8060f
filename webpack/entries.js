const path = require("path");
const fs = require("fs");

const root = path.resolve(__dirname, "../ko/pages");
const except = ["login"];

let entries = {
  "main.2": path.resolve(__dirname, "../ko/foquz.js"),
};

// fs.readdirSync(root, { withFileTypes: true })
//   .filter((d) => d.isDirectory())
//   .filter((d) => !except.includes(d))
//   .map((d) => d.name)
//   .forEach((directory) => {
//     let dirPath = path.join(root, `/${directory}`);
//     fs.readdirSync(dirPath, { withFileTypes: true })
//       .filter((d) => d.isDirectory())
//       .map((d) => d.name)
//       .forEach((page) => {
//         entries[`${directory}/${page}`] = path.join(
//           dirPath,
//           `/${page}/index.js`
//         );
//       });
//   });

const pages = {
  poll: [
    "sender",
    "questions",
    "logic",
    "process",
    "design",
    "stats",
    "answers",
    "answers-external",
    "conditions",
    "mailings",
    "settings",
    "list",
    "interscreens",
    "channels",
    "triggers",
    "charts",
  ],
  "contact-points": ["main"],
  "auto-poll-info": ["main"],
  profile: ["main"],
  mailing: ["answers", "conditions", "mailings", "sender", "settings", "list"],
  report: [
    "conversion",
    "consolidated",
    "channels",
    "contact-points",
    "list",
    "nps",
    "requests",
    "nps-dynamics",
    "cloud",
  ],
  settings: ["main"],
  contacts: ["main"],
  answers: ["main", "reviews", "feedback"],
  company: ["main", "list"],
  user: ["list"],
  wiki: ["main", "article", "view", "search"],
  legals: ["list"],
  payments: ["main"],
  login: ["main", "agreement"],
  widget: ["main", "overview"],
  landing: ["solution", "solutions", "cases", "case", "main", "article", "tariffs", "author"],
  oauth: ["main"],
  request: ["settings", "list", "public-list", "public-view", "answers"],
  admin: ["coupons", "report"],
  informer: ["main"],
  "print-answer": ["main"],
};

Object.keys(pages).forEach((key) => {
  pages[key].forEach((pageName) => {
    let fullName = `${key}.${pageName}`;
    entries[fullName] = path.resolve(
      __dirname,
      `../ko/pages/${key}/${pageName}/index.js`
    );
  });
});

entries["poll.stats.external"] = path.resolve(
  __dirname,
  "../ko/pages/poll/stats/external.js"
);

entries["poll.stats.widget"] = path.resolve(
  __dirname,
  "../ko/pages/poll/stats/widget.js"
);

entries["report.nps.print"] = path.resolve(
  __dirname,
  "../ko/pages/report/nps/print.js"
);

entries["report.requests.print"] = path.resolve(
  __dirname,
  "../ko/pages/report/requests/print.js"
);

entries["report.nps-dynamics.print"] = path.resolve(
  __dirname,
  "../ko/pages/report/nps-dynamics/print.js"
);

entries["report.cloud.print"] = path.resolve(
  __dirname,
  "../ko/pages/report/cloud/print.js"
);

entries["styleguide"] = path.resolve(__dirname, "../ko/styleguide/index.js");

const layouts = ["base", "external", "landing", "help", "executor", "requests"];
layouts.forEach((l) => {
  entries[`layout-${l}`] = path.resolve(
    __dirname,
    "../ko/layouts/" + l + "/index.js"
  );
});

module.exports = entries;
