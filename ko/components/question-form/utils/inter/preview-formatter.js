import { interblockTypes } from "Data/interblock-types";


export default function (data) {

  let interBlock = data.interBlock;
  let share = interBlock.share;
  let texts = interBlock.texts;

  let previewData = {
    intermediateBlock: {
      //code: interBlock.promocode.name,
        complaint_button: interBlock.hasComplainButton,
      complaint_button_text: texts.complain,
      poll_button_text: texts.takeSurvey,
      pool_id: interBlock.promocode.poolId,
      screen_type: interblockTypes[interBlock.type],
      show_question_number: interBlock.showNumber,
      socNetworks: {
        social_networks_enabled: interBlock.enableShare,
        ...share,
      },
      text: interBlock.text,
      unsubscribe_button: interBlock.hasUnsubscribeButton,
      unsubscribe_button_text: texts.unsubscribe,
      close_widget_button: interBlock.hasCloseButtonForWidget ? 1 : 0,
      close_widget_button_text: texts.closeButtonForWidget,
      ready_button: interBlock.hasReadyButton ? 1 : 0,
      ready_button_text: texts.ready,
      start_over_button: interBlock.hasStartOverButton ? 1 : 0,
      start_over_button_text: texts.startOver,
      external_link: interBlock.readyLink,
      scores_button: interBlock.hasPointsButton ? 1 : 0,
      scores_button_text: texts.points,
      logos_backcolor: interBlock.imagesBackground,
      agreement: interBlock.agreement,
      agreement_text: interBlock.agreementText,
      show_image_button_text: interBlock.show_image_button_text,
      image_show_time: interBlock.image_show_time,
      show_bg_instruction: interBlock.show_bg_instruction
    },
    endScreenImages: interBlock.images,
  };

  return previewData;
}
