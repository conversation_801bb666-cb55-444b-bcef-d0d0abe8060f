# Foquz-Core Project Guide

## Project Overview (from project-brief.md)

### Executive Summary

Foquz-core is a comprehensive, enterprise-grade survey and polling platform designed for collecting customer feedback, conducting market research, and managing multi-channel communication campaigns. The system supports complex survey logic, real-time analytics, and automated marketing workflows across multiple communication channels.

### System Architecture

**Technology Stack:**

*   **Backend Framework:** PHP 8.2 with Yii2 Framework
*   **Frontend Technologies:** TypeScript/JavaScript with Knockout.js MVVM framework
*   **Database:** MySQL
*   **Caching:** Redis
*   **Queueing:** RabbitMQ
*   **Deployment:** Docker containerization

---

## Codebase Structure and Conventions (from CODEBASE_STRUCTURE.mdc)

### Architecture Overview

Foquz-core uses a **dual-layer architecture** combining PHP backend views with Knockout.js frontend view models:

- **PHP Views**: Server-side rendering and data preparation
- **Knockout.js ViewModels**: Client-side logic, interactions, and dynamic UI

### Directory Structure Pattern

**Standard Module Pattern**
```
Feature: Statistics
├── PHP View:     modules/foquz/views/foquz-poll/stats.php
└── KO ViewModel: ko/pages/poll/stats/
```

**Frontend Structure**
```
ko/
├── pages/           # Page-level view models and components
├── presentation/    # UI Kit and reusable components
├── components/      # Knockout components
├── models/          # Data models and business logic
├── utils/           # Utility functions and helpers
├── bindings/        # Custom Knockout bindings
└── constants/       # Application constants
```

### UI Kit & Presentation Layer

**Presentation Folder Structure**
```
ko/presentation/
├── views/           # Reusable view templates
├── hooks/           # Custom hooks and lifecycle management
├── interactions/    # User interaction handlers
├── viewModels/      # Base view model classes
├── components/      # Presentation components
├── constants/       # UI constants and themes
└── bindings/        # Presentation-specific bindings
```

**Important**: Knockout components in `/presentation` are **automatically included** - no manual imports required.

### Critical Module: Answer Management

The answer system has **three distinct modules** with different purposes and complex view models:

**1. "answers" - Company-Wide Answers**
*   **Purpose**: All answers across all polls for a company
    ```
    ├── PHP Views:     modules/foquz/views/answers/
    └── KO ViewModels: ko/pages/answers/
    ```

**2. "poll-answers" - Single Poll Answers**
*   **Purpose**: Answers from a specific poll (internal access)
    ```
    ├── PHP Views:     modules/foquz/views/foquz-poll/answers/
    └── KO ViewModels: ko/models/answers/
    ```

**3. "answers-external" - External Poll Answers**
*   **Purpose**: Answers from a specific poll via external generated link
    ```
    ├── PHP Views:     modules/foquz/views/foquz-poll/answers-external/
    └── KO ViewModels: ko/models/answers-external/
    ```

#### ⚠️ Answer Module Complexity Warning

**Critical Note**: Answer view models are **extremely complex** because they:
- Recreate entire view model hierarchies for each operation
- Handle complex sorting/filtering logic from scratch
- Manage state across multiple nested components
- Process large datasets with real-time updates

**Navigation Tip**: When working with answer modules, expect to trace through multiple view model layers and be prepared for intricate state management patterns.

### Component Organization

**Page Components**
```
ko/pages/poll/stats/
├── index.js         # Main view model entry point
├── external.js      # External access logic
├── widget.js        # Widget-specific logic
├── template.php     # PHP template integration
├── style.less       # Component styles
├── components/      # Sub-components
├── modals/          # Modal dialogs
├── templates/       # Template partials
├── types/           # Type-specific handlers
└── utils/           # Component utilities
```

**Knockout Component Registration**

Components are automatically registered and available globally:

```javascript
// ✅ Automatically available - no imports needed
<component params="name: 'modal-container'"></component>
<foquz-modals-container></foquz-modals-container>
<dialogs-container></dialogs-container>
```

### Data Flow Pattern

**Typical Page Flow**
1.  **PHP Controller** prepares data and renders view
2.  **PHP View** (`stats.php`) outputs JavaScript globals:
    ```php
    $this->registerJs("
        var QUESTIONS = " . Json::encode($questions) . ";
        var POLL = " . Json::encode($poll) . ";
    ");
    ```
3.  **Knockout ViewModel** (`ko/pages/poll/stats/index.js`) consumes globals
4.  **Components** handle user interactions and state updates

**Asset Registration Pattern**
```php
// @NOTE: those files are generated by webpack and minified. Do not read or edit them.
// Check knockout view models and css files in those directories for the correct file names.

// CSS Registration
$this->registerCSSFile('/js/poll.stats.css', [
    'depends' => [FoquzAsset::className()]
]);

// JS Registration
$this->registerJSFile('/js/poll.stats.js', [
    'depends' => [FoquzAsset::className()]
]);
```

### Development Guidelines

**Working with Statistics Example**
```
PHP Entry Point:    modules/foquz/views/foquz-poll/stats.php
Frontend Logic:     ko/pages/poll/stats/index.js
Styling:           ko/pages/poll/stats/style.less
Sub-components:    ko/pages/poll/stats/components/
```

**Component Development**
1.  **No manual imports** for presentation components
2.  **Use data-bind** for Knockout integration
3.  **Follow naming conventions**: `question-statistics__*` for CSS classes
4.  **Leverage existing UI kit** from `/presentation`

**Answer Module Development**
- **Expect complexity** - view models recreate entire hierarchies
- **Test thoroughly** - sorting/filtering affects multiple layers
- **Trace carefully** - state management spans multiple components
- **Consider performance** - large datasets require optimization

### File Naming Conventions

**PHP Views**
- `kebab-case` for directories: `foquz-poll/`
- `camelCase` for files: `stats.php`

**Knockout Files**
- `kebab-case` for directories: `poll/stats/`
- `camelCase` for JavaScript: `index.js`, `external.js`
- `kebab-case` for styles: `style.less`, `widget.less`

**Component Classes**
- BEM methodology: `question-statistics__question-header`
- Feature prefixes: `poll-stats__actions`

### Integration Points

**PHP to Knockout Data Transfer**
```php
// PHP View
var POLL_DATA = <?= Json::encode($data) ?>;

// Knockout ViewModel
this.pollData = window.POLL_DATA;
```

**Asset Dependencies**
All frontend assets depend on `FoquzAsset::className()` for proper loading order and dependency management.

### Sidesheets - Primary UI Pattern

**Sidesheets** are the main interface pattern for forms, similar to dialog or modal window, detailed views, and API interactions. They slide in from the right side of the screen.

**Sidesheet Structure**
```
ko/dialogs/[feature]-sidesheet/
├── index.js         # Component registration
├── model.js         # ViewModel with DialogWrapper
├── template.html    # Sidesheet content template
└── style.less       # Sidesheet-specific styles
```

**Opening Sidesheets**
```javascript
// ko/pages/poll/sender/poll-link/model.js
import { DialogsModule } from "Utils/dialogs-module";

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params);
    DialogsModule(this); // Enables openSidesheet method
  }

  openLinkSideshit(linkModel) {
    this.openSidesheet({
      name: "poll-link-sidesheet",
      params: {
        pollLink: this.pollLink,
        linkModel: linkModel,
        blocked: this.blocked,
      },
    });
  }
}
```

### Sidesheet vs Dialog

**Use Sidesheets for:**
- Forms and data entry
- Detailed views and editing
- Multi-step workflows
- API interactions requiring feedback

**Use Dialogs for:**
- Simple confirmations
- Quick actions
- Small information displays
- Yes/No decisions

### Key Sidesheet Features

**Built-in Functionality:**
- **Responsive design** - Adapts to screen size
- **Animation system** - Slide in/out transitions
- **Event handling** - Submit, hide, change events
- **Auto-scrolling** - Handles overflow content
- **Close button** - Positioned outside sidesheet
- **Mask overlay** - Click-to-close background

---

**Remember**: This is a mature codebase with established patterns. Follow existing conventions and be especially careful when modifying answer-related modules due to their complexity.
