<?php

namespace unit\quotes;


use tests\unit\quotes\AnswerQuoteBase;
use tests\unit\quotes\fixtures\FoquzPollAnswerFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerItemFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerItemQuoteFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerQuotesDetailFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerQuotesFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerQuotesGroupFixture;
use tests\unit\quotes\fixtures\FoquzPollFixture;
use tests\unit\quotes\fixtures\FoquzPollLinkQuotesFixture;
use tests\unit\quotes\fixtures\FoquzQuestionDetailFixture;
use tests\unit\quotes\fixtures\FoquzQuestionFixture;
use tests\unit\quotes\fixtures\FoquzQuestionIntermediateBlockSettingFixture;
use tests\unit\quotes\fixtures\FoquzQuestionStarRatingOptionsFixture;
use yii\test\FixtureTrait;


/**
 * Одна квота с логикой по ИЛИ
 * Без групп
 * Три отдельных условия
 */
class AnswerQuote12Test extends AnswerQuoteBase
{
    use FixtureTrait;

    public function testAnswerQuote1()
    {
        // Вопрос 1 (Вар1)
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98300',
        ], 135736);
        $this->assertFireQuote($response);

    }

    public function testAnswerQuote2()
    {
        // Вопрос 1 (Вар2)
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98301',
        ], 135736);
        $this->assertNotFireQuote($response);

        // Вопрос 2 (Вар2)
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98305',
        ], 135737);
        $this->assertFireQuote($response);
    }

    public function testAnswerQuote3()
    {
        // Вопрос 1 (Вар2)
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98301',
        ], 135736);
        $this->assertNotFireQuote($response);

        // Вопрос 2 (Вар1)
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98304',
        ], 135737);
        $this->assertNotFireQuote($response);
    }



    public function fixtures()
    {
        return [
            'poll' => [
                'class' => FoquzPollFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset12/_foquz_poll.php'
            ],
            'questions' => [
                'class' => FoquzQuestionFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset12/_foquz_question.php'
            ],
            'questions_detail' => [
                'class' => FoquzQuestionDetailFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset12/_foquz_question_detail.php'
            ],
            'question_intermediate_block_setting' => [
                'class' => FoquzQuestionIntermediateBlockSettingFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset12/_foquz_question_intermediate_block_setting.php',
            ],
            'questions_star_rating_options' => [
                'class' => FoquzQuestionStarRatingOptionsFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset12/_foquz_question_star_rating_options.php'
            ],
            'link_quotes' => [
                'class' => FoquzPollLinkQuotesFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset12/_foquz_poll_link_quotes.php'
            ],
            'answer_quotes' => [
                'class' => FoquzPollAnswerQuotesFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset12/_foquz_poll_answer_quotes.php'
            ],
            'answer_quotes_group' => [
                'class' => FoquzPollAnswerQuotesGroupFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset12/_foquz_poll_answer_quotes_group.php'
            ],
            'answer_quotes_detail' => [
                'class' => FoquzPollAnswerQuotesDetailFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset12/_foquz_poll_answer_quotes_detail.php'
            ],
            'poll_answers' => [
                'class' => FoquzPollAnswerFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset12/_foquz_poll_answer.php'
            ],
            'poll_answers_detail' => [
                'class' => FoquzPollAnswerItemFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset12/_foquz_poll_answer_item.php'
            ],
            'poll_answer_item_quote' => [
                'class' => FoquzPollAnswerItemQuoteFixture::class,
            ],
        ];
    }
}