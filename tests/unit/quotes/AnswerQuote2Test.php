<?php

namespace unit\quotes;


use app\modules\foquz\models\quotes\criteria\AnswerCounter;
use app\modules\foquz\models\quotes\FoquzPollAnswerQuotes;
use app\modules\foquz\models\quotes\FoquzPollAnswerQuotesDetail;
use app\modules\foquz\models\quotes\FoquzPollLinkQuotes;
use app\modules\foquz\services\quotes\QuoteService;
use tests\unit\quotes\AnswerQuoteBase;
use tests\unit\quotes\fixtures\FoquzPollAnswerFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerItemFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerItemQuoteFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerQuotesDetailFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerQuotesFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerQuotesGroupFixture;
use tests\unit\quotes\fixtures\FoquzPollFixture;
use tests\unit\quotes\fixtures\FoquzPollLinkQuotesFixture;
use tests\unit\quotes\fixtures\FoquzQuestionDetailFixture;
use tests\unit\quotes\fixtures\FoquzQuestionFixture;

use yii\test\FixtureTrait;

/**
 * Лимит ответов 2
 * Одна квота с логикой по И
 * Одна группа с логикой по ИЛИ
 * Одно отдельное условие
 */
class AnswerQuote2Test extends AnswerQuoteBase
{
    use FixtureTrait;


    public function testAnswerQuote1()
    {
        // Вопрос 1 (Вар1)
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98300',
        ], 135735);
        $this->assertNotFireQuote($response);

        // Вопрос 2 (Вар2) 3 ответа
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98304',
        ], 135736);
        $this->assertFireQuote($response);
    }

    public function testAnswerQuote2()
    {
        // Вопрос 1 (Вар2) 4 ответа
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98301',
        ], 135735);
        $this->assertFireQuote($response);
    }

    public function testAnswerQuote3()
    {
        // Вопрос 1 (Вар1)
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98300',
        ], 135735);
        $this->assertNotFireQuote($response);

        // Вопрос 2 (Вар2) 3 ответа - сработает квота для группы
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98304',
        ], 135736);
        $this->assertFireQuote($response);
    }

    public function testAnswerQuote4()
    {
        // Вопрос 1 (Вар2) 4 ответа
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98300&detail_item[1]=98301&detail_item[2]=98302&detail_item[3]=is_self_answer&self_variant=text',
        ], 135735);
        $this->assertNotFireQuote($response);

    }

    public function testAnswerCounter()
    {
        $ac = new AnswerCounter(42476);

        $count = $ac->getAnswersCount(
            135735,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE,
            ['-1', '98300', '98301', '98302'],
            [
                ['-1', '98300', '98301', '98302']
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_OR,
            '2025-01-01 00:00:00',
        );
        $this->assertEquals(0, $count);

        $count = $ac->getAnswersCount(
            135735,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE,
            ['-1', '98302'],
            [
                ['-1', '98302']
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_OR,
            '2025-01-01 00:00:00',
        );
        $this->assertEquals(0, $count);

        $count = $ac->getAnswersCount(
            135736,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE,
            ['98304'],
            [
                ['98304']
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_OR,
            '2025-01-01 00:00:00',
        );
        $this->assertEquals(3, $count);

        $count = $ac->getAnswersCount(
            135735,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE,
            ['98301'],
            [
                ['98301']
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_OR,
            '2025-01-01 00:00:00',
        );
        $this->assertEquals(4, $count);
    }

    public function testCheckQuote()
    {
        $service = new QuoteService(FoquzPollLinkQuotes::findOne(10));
        $this->assertTrue($service->checkQuote(42476, FoquzPollAnswerQuotes::findOne(87)));

        $this->changeAnswerLimit(3);
        $this->assertTrue($service->checkQuote(42476, FoquzPollAnswerQuotes::findOne(87)));

        $this->changeAnswerLimit(7);
        $this->assertTrue($service->checkQuote(42476, FoquzPollAnswerQuotes::findOne(87)));

        $this->changeAnswerLimit(8);
        $this->assertFalse($service->checkQuote(42476, FoquzPollAnswerQuotes::findOne(87)));
    }

    public function fixtures()
    {
        return [
            'poll' => [
                'class' => FoquzPollFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset2/_foquz_poll.php'
            ],
            'questions' => [
                'class' => FoquzQuestionFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset2/_foquz_question.php'
            ],
            'questions_detail' => [
                'class' => FoquzQuestionDetailFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset2/_foquz_question_detail.php'
            ],
            'link_quotes' => [
                'class' => FoquzPollLinkQuotesFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset2/_foquz_poll_link_quotes.php'
            ],
            'answer_quotes' => [
                'class' => FoquzPollAnswerQuotesFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset2/_foquz_poll_answer_quotes.php'
            ],
            'answer_quotes_group' => [
                'class' => FoquzPollAnswerQuotesGroupFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset2/_foquz_poll_answer_quotes_group.php'
            ],
            'answer_quotes_detail' => [
                'class' => FoquzPollAnswerQuotesDetailFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset2/_foquz_poll_answer_quotes_detail.php'
            ],
            'poll_answers' => [
                'class' => FoquzPollAnswerFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset2/_foquz_poll_answer.php'
            ],
            'poll_answers_detail' => [
                'class' => FoquzPollAnswerItemFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset2/_foquz_poll_answer_item.php'
            ],
            'poll_answer_item_quote' => [
                'class' => FoquzPollAnswerItemQuoteFixture::class,
            ],
        ];
    }
}