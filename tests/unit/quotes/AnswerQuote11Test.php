<?php

namespace unit\quotes;


use app\modules\foquz\models\quotes\criteria\AnswerCounter;
use app\modules\foquz\models\quotes\FoquzPollAnswerQuotes;
use app\modules\foquz\models\quotes\FoquzPollAnswerQuotesDetail;
use tests\unit\quotes\AnswerQuoteBase;
use tests\unit\quotes\fixtures\FoquzPollAnswerFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerItemFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerItemQuoteFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerQuotesDetailFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerQuotesFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerQuotesGroupFixture;
use tests\unit\quotes\fixtures\FoquzPollFixture;
use tests\unit\quotes\fixtures\FoquzPollLinkQuotesFixture;
use tests\unit\quotes\fixtures\FoquzQuestionDetailFixture;
use tests\unit\quotes\fixtures\FoquzQuestionFixture;
use tests\unit\quotes\fixtures\FoquzQuestionStarRatingOptionsFixture;
use tests\unit\quotes\fixtures\RecipientQuestionDetailFixture;
use yii\test\FixtureTrait;


/**
 * Одна квота с логикой по ИЛИ
 * Без групп
 * Два отдельных условия Варианты ответов ДР
 */
class AnswerQuote11Test extends AnswerQuoteBase
{
    use FixtureTrait;


    /**
     * @description Проверка не срабавывания квоты на реципиенте
     */
    public function testAnswerQuote1()
    {
        // Донор
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98300&detail_item[1]=98301',
        ], 135735);
        $this->assertNotFireQuote($response);

        // Реципиент
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98300&detail_item[1]=98301',
        ], 135736);
        $this->assertNotFireQuote($response);
    }

    public function testAnswerQuote2()
    {
        // Донор
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98300&detail_item[1]=98301&detail_item[2]=is_self_answer&self_variant=abcd',
        ], 135735);
        $this->assertNotFireQuote($response);

        // Реципиент
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=-1',
        ], 135736);
        $this->assertFireQuote($response);
    }

    public function testAnswerQuote3()
    {
        // меняем первую квоту на "У донора выбран вариант Затрудняюсь ответить" 1 ответ
        $qd = FoquzPollAnswerQuotesDetail::findOne(252);
        $qd->behavior = \app\modules\foquz\models\quotes\FoquzPollAnswerQuotesDetail::BEHAVIOR_DONT_ASK;
        $qd->variants = [];
        $qd->save();

        FoquzPollAnswerQuotesDetail::findOne(253)->softDelete();

        // меняем лимит
        $this->changeAnswerLimit(1);

        // Донор
        $response = $this->runAction([
            'FoquzAnswerItem' => 'skipped=1',
        ], 135735);
        $this->assertFireQuote($response);
    }

    public function testAnswerQuote4()
    {
        FoquzPollAnswerQuotesDetail::findOne(252)->softDelete();

        // меняем вторую квоту на "У реципиента выбран вариант Затрудняюсь ответить" 1 ответ
        $qd = FoquzPollAnswerQuotesDetail::findOne(253);
        $qd->behavior = \app\modules\foquz\models\quotes\FoquzPollAnswerQuotesDetail::BEHAVIOR_DONT_ASK;
        $qd->variants = [];
        $qd->save();

        // меняем лимит
        $this->changeAnswerLimit(1);

        // Реципиент
        $response = $this->runAction([
            'FoquzAnswerItem' => 'skipped=1',
        ], 135736);
        $this->assertFireQuote($response);
    }

    public function testAnswerQuote5()
    {
        $ac = new AnswerCounter(42476);

        // меняем первую квоту на "У донора выбран вариант Вар1" 6 ответов
        $qd = FoquzPollAnswerQuotesDetail::findOne(252);
        $qd->question_id = 135735;
        $qd->behavior = \app\modules\foquz\models\quotes\FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE;
        $qd->variants = ['98300'];
        $qd->save();

        // меняем вторую квоту на "У донора выбран вариант Вар2" 5 ответов
        $qd = FoquzPollAnswerQuotesDetail::findOne(253);
        $qd->question_id = 135735;
        $qd->behavior = \app\modules\foquz\models\quotes\FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE;
        $qd->variants = ['98301'];
        $qd->save();

        $this->changeAnswerLogicOperation(\app\modules\foquz\models\quotes\FoquzPollAnswerQuotes::LOGIC_OPERATION_AND);

        $this->changeAnswerLimit(11);

        $count = $ac->getAnswersCount(
            135735,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE,
            ['98300'],
            [
                ['98300'],
                ['98301']
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_AND,
            '2025-01-01 00:00:00',
        );
        // если по И, ответы суммируются
        $this->assertEquals(11, $count);

        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98300',
        ], 135735);
        $this->assertFireQuote($response);


        $count = $ac->getAnswersCount(
            135735,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE,
            ['98301'],
            [
                ['98300'],
                ['98301']
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_AND,
            '2025-01-01 00:00:00',
        );
        // если по И, ответы суммируются
        $this->assertEquals(11, $count);

        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98301',
        ], 135735);
        $this->assertFireQuote($response);
    }

    public function testAnswerQuote6()
    {
        $ac = new AnswerCounter(42476);

        // меняем первую квоту на "У донора выбран вариант Вар1" 6 ответов
        $qd = FoquzPollAnswerQuotesDetail::findOne(252);
        $qd->question_id = 135735;
        $qd->behavior = \app\modules\foquz\models\quotes\FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE;
        $qd->variants = ['98300'];
        $qd->save();

        // меняем вторую квоту на "У донора выбран вариант Вар2" 5 ответов
        $qd = FoquzPollAnswerQuotesDetail::findOne(253);
        $qd->question_id = 135735;
        $qd->behavior = \app\modules\foquz\models\quotes\FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE;
        $qd->variants = ['98301'];
        $qd->save();

        $this->changeAnswerLogicOperation(\app\modules\foquz\models\quotes\FoquzPollAnswerQuotes::LOGIC_OPERATION_OR);
        $this->changeAnswerLimit(5);

        $count = $ac->getAnswersCount(
            135735,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE,
            ['98300'],
            [
                ['98300'],
                ['98301']
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_OR,
            '2025-01-01 00:00:00',
        );
        // если по ИЛИ, выбирается вариант с большим количеством ответов
        $this->assertEquals(6, $count);

        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98300',
        ], 135735);
        $this->assertFireQuote($response);


        $count = $ac->getAnswersCount(
            135735,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE,
            ['98301'],
            [
                ['98300'],
                ['98301']
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_OR,
            '2025-01-01 00:00:00',
        );
        // если по ИЛИ, выбирается вариант с большим количеством ответов
        $this->assertEquals(6, $count);

        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98301',
        ], 135735);
        $this->assertFireQuote($response);
    }

    public function testAnswerQuote7()
    {
        $ac = new AnswerCounter(42476);

        // меняем первую квоту на "У реципиента выбран вариант Вар1" 2 ответа
        $qd = FoquzPollAnswerQuotesDetail::findOne(252);
        $qd->question_id = 135736;
        $qd->behavior = \app\modules\foquz\models\quotes\FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE;
        $qd->variants = ['6811']; // для реципиента тут другие айдишники из recipient_question_detail
        $qd->save();

        // меняем вторую квоту на "У реципиента выбран вариант Вар2" 2 ответа
        $qd = FoquzPollAnswerQuotesDetail::findOne(253);
        $qd->question_id = 135736;
        $qd->behavior = \app\modules\foquz\models\quotes\FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE;
        $qd->variants = ['6812']; // для реципиента тут другие айдишники из recipient_question_detail
        $qd->save();

        $this->changeAnswerLogicOperation(\app\modules\foquz\models\quotes\FoquzPollAnswerQuotes::LOGIC_OPERATION_AND);
        $this->changeAnswerLimit(4);

        $count = $ac->getAnswersCount(
            135736,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE,
            ['98300'],
            [
                ['98300'],
                ['98301']
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_AND,
            '2025-01-01 00:00:00',
            135735
        );
        // если по И, ответы суммируются
        $this->assertEquals(4, $count);

        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98300',
        ], 135736);
        $this->assertFireQuote($response);


        $count = $ac->getAnswersCount(
            135736,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE,
            ['98301'],
            [
                ['98300'],
                ['98301']
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_OR,
            '2025-01-01 00:00:00',
            135735
        );
        // если по ИЛИ, выбирается вариант с большим количеством ответов
        $this->assertEquals(2, $count);

        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98301',
        ], 135736);
        $this->assertFireQuote($response);
    }

    public function testAnswerCounter()
    {
        $ac = new AnswerCounter(42476);

        $count = $ac->getAnswersCount(
            135735,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE,
            ['98300', '98301'],
            [
                ['98300', '98301']
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_OR,
            '2025-01-01 00:00:00',
        );
        $this->assertEquals(5, $count);

        $count = $ac->getAnswersCount(
            135736,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE,
            ['-1'],
            [
                ['-1']
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_OR,
            '2025-01-01 00:00:00',
            135735
        );
        $this->assertEquals(2, $count);

        $count = $ac->getAnswersCount(
            135735,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE,
            ['-1', '98300', '98301'],
            [
                ['-1', '98300', '98301']
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_OR,
            '2025-01-01 00:00:00',
        );
        $this->assertEquals(1, $count);


        $count = $ac->getAnswersCount(
            135736,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE,
            ['98301'],
            [
                ['98301']
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_OR,
            '2025-01-01 00:00:00',
            135735
        );
        $this->assertEquals(2, $count);

        $count = $ac->getAnswersCount(
            135735,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_DONT_ASK,
            [],
            [
                []
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_OR,
            '2025-01-01 00:00:00',
        );
        $this->assertEquals(1, $count);


        $count = $ac->getAnswersCount(
            135736,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_DONT_ASK,
            [],
            [
                []
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_OR,
            '2025-01-01 00:00:00',
            135735
        );
        $this->assertEquals(1, $count);
    }

    public function fixtures()
    {
        return [
            'poll' => [
                'class' => FoquzPollFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset11/_foquz_poll.php'
            ],
            'questions' => [
                'class' => FoquzQuestionFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset11/_foquz_question.php'
            ],
            'recipient_question_detail' => [
                'class' => RecipientQuestionDetailFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset11/_recipient_question_detail.php'
            ],
            'questions_detail' => [
                'class' => FoquzQuestionDetailFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset11/_foquz_question_detail.php'
            ],
            'questions_star_rating_options' => [
                'class' => FoquzQuestionStarRatingOptionsFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset11/_foquz_question_star_rating_options.php'
            ],
            'link_quotes' => [
                'class' => FoquzPollLinkQuotesFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset11/_foquz_poll_link_quotes.php'
            ],
            'answer_quotes' => [
                'class' => FoquzPollAnswerQuotesFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset11/_foquz_poll_answer_quotes.php'
            ],
            'answer_quotes_group' => [
                'class' => FoquzPollAnswerQuotesGroupFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset11/_foquz_poll_answer_quotes_group.php'
            ],
            'answer_quotes_detail' => [
                'class' => FoquzPollAnswerQuotesDetailFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset11/_foquz_poll_answer_quotes_detail.php'
            ],
            'poll_answers' => [
                'class' => FoquzPollAnswerFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset11/_foquz_poll_answer.php'
            ],
            'poll_answers_detail' => [
                'class' => FoquzPollAnswerItemFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset11/_foquz_poll_answer_item.php'
            ],
            'poll_answer_item_quote' => [
                'class' => FoquzPollAnswerItemQuoteFixture::class,
            ],
        ];
    }
}