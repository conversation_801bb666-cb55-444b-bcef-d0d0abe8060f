<?php

namespace unit\quotes;


use app\modules\foquz\models\FoquzPollAnswer;
use app\modules\foquz\models\quotes\criteria\AnswerCounter;
use app\modules\foquz\models\quotes\FoquzPollAnswerQuotes;
use app\modules\foquz\models\quotes\FoquzPollAnswerQuotesDetail;
use tests\unit\quotes\AnswerQuoteBase;
use tests\unit\quotes\fixtures\FoquzPollAnswerFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerItemFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerItemQuoteFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerQuotesDetailFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerQuotesFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerQuotesGroupFixture;
use tests\unit\quotes\fixtures\FoquzPollFixture;
use tests\unit\quotes\fixtures\FoquzPollLinkQuotesFixture;
use tests\unit\quotes\fixtures\FoquzQuestionDetailFixture;
use tests\unit\quotes\fixtures\FoquzQuestionFixture;
use tests\unit\quotes\fixtures\FoquzQuestionIntermediateBlockSettingFixture;
use tests\unit\quotes\fixtures\FoquzQuestionStarRatingOptionsFixture;
use yii\test\FixtureTrait;


/**
 * Одна квота с логикой по ИЛИ
 * Без групп
 * Два отдельных условия
 */
class AnswerQuote9Test extends AnswerQuoteBase
{
    use FixtureTrait;

    /**
     * @description Проверка срабавывания квоты на первом вопросе и установка статуса анкеты как quote-full
     */
    public function testAnswerQuote1()
    {
        // Вопрос 1 (Вар1,Вар3) 2 ответа
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98300&detail_item[1]=98302',
        ], 135735);
        $this->assertFireQuote($response);

        //анкета должна получить статус quota-full
        $m = $this->getResultAnketa();
        $this->assertEquals(FoquzPollAnswer::STATUS_QUOTE_FULL, $m->status);

        // Конечный экран для квотафул анкеты
        $this->assertEquals(135740, $response->data['endScreen']);
    }


    /**
     * @description Проверка срабавывания квоты на третьем вопросе и установка статуса анкеты как quote-full
     */
    public function testAnswerQuote2()
    {
        // конечный экран для квотафул не задан
        $m = FoquzPollAnswerQuotes::findOne(87);
        $m->end_screen = null;
        $m->save();

        // Вопрос 3 (Пропуск ответа) 2 ответа
        $response = $this->runAction([
            'FoquzAnswerItem' => '',
        ], 135737);
        $this->assertFireQuote($response);
        //анкета должна получить статус quota-full
        $m = $this->getResultAnketa();
        $this->assertEquals(FoquzPollAnswer::STATUS_QUOTE_FULL, $m->status);

        // Конечный экран для квотафул анкеты - стандартное завершение
        $this->assertEquals(0, $response->data['endScreen']);
    }


    /**
     * @description Проверка не срабавывания квоты на третьем вопросе и установка статуса анкеты как in-progress
     */
    public function testAnswerQuote3()
    {
        $this->changeAnswerLimit(4);

        // Вопрос 3 (Пропуск ответа) 2 ответа
        $response = $this->runAction([
            'FoquzAnswerItem' => '',
        ], 135737);

        $this->assertNotFireQuote($response);
        //анкета должна получить статус in-progress
        $m = $this->getResultAnketa();
        $this->assertEquals(FoquzPollAnswer::STATUS_IN_PROGRESS, $m->status);
    }

    /**
     * @description Проверка не срабавывания квот на первом и третьем вопросе и установка статуса анкеты как done
     */
    public function testAnswerQuote4()
    {
        $this->changeAnswerLimit(4);

        // Вопрос 1 (Вар1,Вар2)
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98300&detail_item[1]=98301',
        ], 135735);
        $this->assertNotFireQuote($response);
        //анкета должна получить статус in-progress
        $m = $this->getResultAnketa();
        $this->assertEquals(FoquzPollAnswer::STATUS_IN_PROGRESS, $m->status);


        // Звездный рейтинг
        $response = $this->runAction([
            'FoquzAnswerItem' => 'rating=3',
        ], 135736);
        $this->assertNotFireQuote($response);
        //анкета должна получить статус in-progress
        $m = $this->getResultAnketa();
        $this->assertEquals(FoquzPollAnswer::STATUS_IN_PROGRESS, $m->status);


        // Вопрос 3 (Пропуск ответа) 2 ответа
        $response = $this->runAction([
            'FoquzAnswerItem' => '',
        ], 135737);
        $this->assertNotFireQuote($response);
        //анкета должна получить статус quotefull
        $m = $this->getResultAnketa();
        $this->assertEquals(FoquzPollAnswer::STATUS_IN_PROGRESS, $m->status);


        // Звездный рейтинг 2
        $response = $this->runAction([
            'FoquzAnswerItem' => 'rating=3',
        ], 135738);
        $this->assertNotFireQuote($response);
        //анкета должна получить статус done
        $m = $this->getResultAnketa();
        $this->assertEquals(FoquzPollAnswer::STATUS_DONE, $m->status);

        // Конечный экран для заполненной анкеты
        $this->assertEquals(135742, $response['endScreen']);

    }


    /**
     * @description Проверка не попадания ответов в условия квот и установка статуса анкеты как screen-out
     */
    public function testAnswerQuote5()
    {
        // Вопрос 1 (Вар2) нет такого условия в квоте
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98301',
        ], 135735);
        $this->assertNotFireQuote($response);
        //анкета должна получить статус in-progress
        $m = $this->getResultAnketa();
        $this->assertEquals(FoquzPollAnswer::STATUS_IN_PROGRESS, $m->status);


        // Звездный рейтинг
        $response = $this->runAction([
            'FoquzAnswerItem' => 'rating=3',
        ], 135736);
        $this->assertNotFireQuote($response);
        //анкета должна получить статус in-progress
        $m = $this->getResultAnketa();
        $this->assertEquals(FoquzPollAnswer::STATUS_IN_PROGRESS, $m->status);


        // Вопрос 3 (Вар2) нет такого условия в квоте
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98304',
        ], 135737);
        $this->assertNotFireQuote($response);
        //анкета должна получить статус in-progress
        $m = $this->getResultAnketa();
        $this->assertEquals(FoquzPollAnswer::STATUS_IN_PROGRESS, $m->status);


        // Звездный рейтинг 2
        $response = $this->runAction([
            'FoquzAnswerItem' => 'rating=3',
        ], 135738);
        $this->assertNotFireQuote($response);
        //анкета должна получить статус screen-out
        $m = $this->getResultAnketa();
        $this->assertEquals(FoquzPollAnswer::STATUS_SCREEN_OUT, $m->status);

        // Конечный экран для screen-out анкеты
        $this->assertEquals(135741, $response['endScreen']);
    }


    /**
     * @description Проверка попадания одного ответа в условия квот и установка статуса анкеты как done
     */
    public function testAnswerQuote6()
    {
        $this->changeAnswerLimit(4);

        // Вопрос 1 (Вар2) нет такого условия в квоте
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98301',
        ], 135735);
        $this->assertNotFireQuote($response);
        //анкета должна получить статус in-progress
        $m = $this->getResultAnketa();
        $this->assertEquals(FoquzPollAnswer::STATUS_IN_PROGRESS, $m->status);


        // Звездный рейтинг
        $response = $this->runAction([
            'FoquzAnswerItem' => 'rating=3',
        ], 135736);
        $this->assertNotFireQuote($response);
        //анкета должна получить статус in-progress
        $m = $this->getResultAnketa();
        $this->assertEquals(FoquzPollAnswer::STATUS_IN_PROGRESS, $m->status);


        // Вопрос 3 (Пропуск ответа) 2 ответа
        $response = $this->runAction([
            'FoquzAnswerItem' => '',
        ], 135737);
        $this->assertNotFireQuote($response);
        //анкета должна получить статус in-progress
        $m = $this->getResultAnketa();
        $this->assertEquals(FoquzPollAnswer::STATUS_IN_PROGRESS, $m->status);


        // Звездный рейтинг 2
        $response = $this->runAction([
            'FoquzAnswerItem' => 'rating=3',
        ], 135738);
        $this->assertNotFireQuote($response);

        //анкета должна получить статус done
        $m = $this->getResultAnketa();
        $this->assertEquals(FoquzPollAnswer::STATUS_DONE, $m->status);

        // Конечный экран для заполненной анкеты
        $this->assertEquals(135742, $response['endScreen']);
    }



    /**
     * @description Проверка попадания одного ответа в условия квот и установка статуса анкеты как done при неограниченном лимите
     */
    public function testAnswerQuote7()
    {
        $this->changeAnswerLimit(0);

        // Вопрос 1 (Вар2) нет такого условия в квоте
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98301',
        ], 135735);
        $this->assertNotFireQuote($response);
        //анкета должна получить статус in-progress
        $m = $this->getResultAnketa();
        $this->assertEquals(FoquzPollAnswer::STATUS_IN_PROGRESS, $m->status);


        // Звездный рейтинг
        $response = $this->runAction([
            'FoquzAnswerItem' => 'rating=3',
        ], 135736);
        $this->assertNotFireQuote($response);
        //анкета должна получить статус in-progress
        $m = $this->getResultAnketa();
        $this->assertEquals(FoquzPollAnswer::STATUS_IN_PROGRESS, $m->status);


        // Вопрос 3 (Пропуск ответа) 2 ответа
        $response = $this->runAction([
            'FoquzAnswerItem' => '',
        ], 135737);
        $this->assertNotFireQuote($response);
        //анкета должна получить статус in-progress
        $m = $this->getResultAnketa();
        $this->assertEquals(FoquzPollAnswer::STATUS_IN_PROGRESS, $m->status);



        // Звездный рейтинг 2
        $response = $this->runAction([
            'FoquzAnswerItem' => 'rating=3',
        ], 135738);
        $this->assertNotFireQuote($response);

        //анкета должна получить статус done
        $m = $this->getResultAnketa();
        $this->assertEquals(FoquzPollAnswer::STATUS_DONE, $m->status);

        // Конечный экран для заполненной анкеты
        $this->assertEquals(135742, $response['endScreen']);
    }


    public function testAnswerCounter()
    {
        $ac = new AnswerCounter(42476);

        $count = $ac->getAnswersCount(
            135735,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE,
            ['98300', '98302'],
            [
                ['98300', '98302']
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_OR,
            '2025-01-01 00:00:00',
        );
        $this->assertEquals(2, $count);


        $count = $ac->getAnswersCount(
            135737,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_SKIP,
            [],
            [
                []
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_OR,
            '2025-01-01 00:00:00',
        );
        $this->assertEquals(2, $count);
    }


    public function fixtures()
    {
        return [
            'poll' => [
                'class' => FoquzPollFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset9/_foquz_poll.php'
            ],
            'questions' => [
                'class' => FoquzQuestionFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset9/_foquz_question.php'
            ],
            'questions_detail' => [
                'class' => FoquzQuestionDetailFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset9/_foquz_question_detail.php'
            ],
            'question_intermediate_block_setting' => [
                'class' => FoquzQuestionIntermediateBlockSettingFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset9/_foquz_question_intermediate_block_setting.php',
            ],
            'questions_star_rating_options' => [
                'class' => FoquzQuestionStarRatingOptionsFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset9/_foquz_question_star_rating_options.php'
            ],
            'link_quotes' => [
                'class' => FoquzPollLinkQuotesFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset9/_foquz_poll_link_quotes.php'
            ],
            'answer_quotes' => [
                'class' => FoquzPollAnswerQuotesFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset9/_foquz_poll_answer_quotes.php'
            ],
            'answer_quotes_group' => [
                'class' => FoquzPollAnswerQuotesGroupFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset9/_foquz_poll_answer_quotes_group.php'
            ],
            'answer_quotes_detail' => [
                'class' => FoquzPollAnswerQuotesDetailFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset9/_foquz_poll_answer_quotes_detail.php'
            ],
            'poll_answers' => [
                'class' => FoquzPollAnswerFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset9/_foquz_poll_answer.php'
            ],
            'poll_answers_detail' => [
                'class' => FoquzPollAnswerItemFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset9/_foquz_poll_answer_item.php'
            ],
            'poll_answer_item_quote' => [
                'class' => FoquzPollAnswerItemQuoteFixture::class,
            ],
        ];
    }
}