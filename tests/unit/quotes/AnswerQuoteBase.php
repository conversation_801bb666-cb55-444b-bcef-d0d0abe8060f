<?php

namespace tests\unit\quotes;

use app\modules\foquz\controllers\api\PController;
use app\modules\foquz\models\FoquzPollAnswer;
use app\modules\foquz\models\quotes\FoquzPollAnswerItemQuote;
use app\modules\foquz\models\quotes\FoquzPollAnswerQuotes;

use PHPUnit\Framework\TestCase;
use yii\queue\amqp_interop\Queue;
use yii\test\FixtureTrait;

class AnswerQuoteBase extends TestCase
{
    use FixtureTrait;

    /**
     * @var \UnitTester
     */
    protected $tester;

    protected $controller;


    public function __construct()
    {
        parent::__construct();

        // Mock Queue
        $mockQueue = $this->createMock(Queue::class);
        $mockQueue->method('push')->willReturn(true);
        $mockQueue->method('delay')->willReturn($mockQueue);
        \Yii::$app->set('rabbit_queue', $mockQueue);

        // Mock Cache
        $mockCache = $this->createMock(\yii\caching\Cache::class);
        $mockCache->method('get')->willReturn(false);
        $mockCache->method('set')->willReturn(true);
        \Yii::$app->set('cache', $mockCache);

        $this->controller = new PController('default-controller', \Yii::$app);
    }

    protected function setUp(): void
    {
        parent::setUp();
        $this->unloadFixtures();
        $this->loadFixtures();
    }

    protected function tearDown(): void
    {
        //$this->unloadFixtures();
        parent::tearDown();
    }

    protected function assertFireQuote($response)
    {
        if (!is_array($response)) {
            $response = $response->data;
        }
        $this->assertNotNull($response);
        $this->assertArrayHasKey('isQuoteLimitsOver', $response);
        $this->assertTrue($response['isQuoteLimitsOver']);
        $this->assertArrayHasKey('endScreen', $response);
    }

    protected function assertNotFireQuote($response)
    {
        if (!is_array($response)) {
            $response = $response->data;
        }
        $this->assertNotNull($response);
        $this->assertArrayNotHasKey('isQuoteLimitsOver', $response);
        $this->assertArrayHasKey('lastQuestion', $response);
    }

    protected function setPostData(array $postData)
    {
        $_SERVER['REQUEST_METHOD'] = 'POST';
        \Yii::$app->request->setBodyParams($postData);
    }

    protected function assertLogExists(int $answerId, int $answerItemId, int $quoteId)
    {
        $qLog = FoquzPollAnswerItemQuote::find()->where([
            'poll_id' => 42476,
            'answer_id' => $answerId,
            'answer_item_id' => $answerItemId,
            'quote_id' => $quoteId,
        ])->one();
        $this->assertNotNull($qLog);
    }
    protected function assertLogNonExists(int $answerId, int $answerItemId, int $quoteId)
    {
        $qLog = FoquzPollAnswerItemQuote::find()->where([
            'poll_id' => 42476,
            'answer_id' => $answerId,
            'answer_item_id' => $answerItemId,
            'quote_id' => $quoteId,
        ])->one();

        $this->assertNull($qLog);
    }

    protected function runAction(array $postData, int $questionId)
    {
        $this->setPostData($postData);
        return $this->controller->runAction('save-answer', [
            'authKey' => 'f0fbcc46eb606e0704b3d1dc3361d453',
            'questionId' => $questionId
        ]);
    }

    protected function changeAnswerLimit(int $limit)
    {
        $m = FoquzPollAnswerQuotes::findOne(87);
        $m->answers_limit = $limit;
        $m->save();
    }

    protected function changeAnswerLogicOperation(int $logicOperation)
    {
        $m = FoquzPollAnswerQuotes::findOne(87);
        $m->logic_operation = $logicOperation;
        $m->save();
    }

    protected function changePollPublished(int $published)
    {
        \Yii::$app->db->createCommand()->update('foquz_poll', ['is_published' => $published], ['id' => 87])->execute();

    }

    protected function getResultAnketa()
    {
        $m = FoquzPollAnswer::findOne(714880);
        if (!$m) {
            throw new \Exception('Answer not found');
        }
        return $m;
    }
}