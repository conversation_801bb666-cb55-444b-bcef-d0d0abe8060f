<?php

namespace unit\quotes;


use app\modules\foquz\models\quotes\criteria\AnswerCounter;
use app\modules\foquz\models\quotes\FoquzPollAnswerQuotes;
use app\modules\foquz\models\quotes\FoquzPollAnswerQuotesDetail;
use app\modules\foquz\models\quotes\FoquzPollAnswerQuotesGroup;
use tests\unit\quotes\AnswerQuoteBase;
use tests\unit\quotes\fixtures\FoquzPollAnswerFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerItemFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerItemQuoteFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerQuotesDetailFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerQuotesFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerQuotesGroupFixture;
use tests\unit\quotes\fixtures\FoquzPollFixture;
use tests\unit\quotes\fixtures\FoquzPollLinkQuotesFixture;
use tests\unit\quotes\fixtures\FoquzQuestionDetailFixture;
use tests\unit\quotes\fixtures\FoquzQuestionFixture;

use yii\test\FixtureTrait;


/**
 * Лимит ответов 2
 * Одна квота с логикой по И
 * Две группы с логикой по ИЛИ
 * Без отдельных условий
 */
class AnswerQuote7Test extends AnswerQuoteBase
{
    use FixtureTrait;


    public function testAnswerQuote1()
    {
        // Вопрос 1 (Вар1,Вар2,Свой вар) 1 ответ (условие в 1й группе)
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98300&detail_item[1]=98301&detail_item[2]=is_self_answer&self_variant=text',
        ], 135735);
        $this->assertFireQuote($response);
    }

    public function testAnswerQuote2()
    {
        // Вопрос 2 (Вар2) 3 ответа (условие в 1й группе)
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98304',
        ], 135736);
        $this->assertFireQuote($response);
    }

    public function testAnswerQuote3()
    {
        $this->changeAnswerLogicOperation(\app\modules\foquz\models\quotes\FoquzPollAnswerQuotes::LOGIC_OPERATION_OR);

        // Вопрос 3 (Вар1) 3 ответа (условие во 2й группе)
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98305',
        ], 135737);
        $this->assertFireQuote($response);
    }

    public function testAnswerQuote4()
    {
        $this->changeAnswerLimit(1);
        $q1 = FoquzPollAnswerQuotesGroup::findOne(139);
        $q1->logic_operation = FoquzPollAnswerQuotes::LOGIC_OPERATION_AND;
        $q1->save();

        $q2 = FoquzPollAnswerQuotesGroup::findOne(140);
        $q2->logic_operation = FoquzPollAnswerQuotes::LOGIC_OPERATION_AND;
        $q2->save();

        // Вопрос 4 (Затрудн ответить) 2 ответа (условие во 2й группе)
        $response = $this->runAction([
            'FoquzAnswerItem' => 'skipped=1',
        ], 135738);

        $this->assertFireQuote($response);
    }

    public function testAnswerQuote5()
    {
        $this->changeAnswerLimit(12);
        $q1 = FoquzPollAnswerQuotesGroup::findOne(139);
        $q1->logic_operation = FoquzPollAnswerQuotes::LOGIC_OPERATION_AND;
        $q1->save();

        $q2 = FoquzPollAnswerQuotesGroup::findOne(140);
        $q2->logic_operation = FoquzPollAnswerQuotes::LOGIC_OPERATION_AND;
        $q2->save();

        // Вопрос 1 (Вар1,Вар2,Свой вар) 1 ответ (условие в 1й группе не сработает, так как увеличили лимит)
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98300&detail_item[1]=98301&detail_item[2]=is_self_answer&self_variant=text',
        ], 135735);
        $this->assertNotFireQuote($response);

        // Вопрос 2 (Вар2) 3 ответа (условие в 1й группе)
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98304',
        ], 135736);
        $this->assertNotFireQuote($response);
    }

    public function testAnswerQuote6()
    {
        $q1 = FoquzPollAnswerQuotesGroup::findOne(139);
        $q1->logic_operation = FoquzPollAnswerQuotes::LOGIC_OPERATION_AND;
        $q1->save();

        // Вопрос 1 (Вар1,Вар2,Свой вар)
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98300&detail_item[1]=98301&detail_item[2]=is_self_answer&self_variant=text',
        ], 135735);
        $this->assertFireQuote($response);
    }



    public function testAnswerCounter()
    {
        $ac = new AnswerCounter(42476);

        $count = $ac->getAnswersCount(
            135735,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE,
            ['-1', '98300', '98301'],
            [
                ['-1', '98300', '98301']
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_OR,
            '2025-01-01 00:00:00',
        );
        $this->assertEquals(1, $count);

        $count = $ac->getAnswersCount(
            135736,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE,
            ['98304'],
            [
                ['98304']
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_OR,
            '2025-01-01 00:00:00',
        );
        $this->assertEquals(3, $count);

        $count = $ac->getAnswersCount(
            135737,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE,
            ['98305'],
            [
                ['98305']
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_OR,
            '2025-01-01 00:00:00',
        );
        $this->assertEquals(3, $count);

        $count = $ac->getAnswersCount(
            135738,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_DONT_ASK,
            [],
            [
                []
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_OR,
            '2025-01-01 00:00:00',
        );
        $this->assertEquals(2, $count);
    }

    public function fixtures()
    {
        return [
            'poll' => [
                'class' => FoquzPollFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset7/_foquz_poll.php'
            ],
            'questions' => [
                'class' => FoquzQuestionFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset7/_foquz_question.php'
            ],
            'questions_detail' => [
                'class' => FoquzQuestionDetailFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset7/_foquz_question_detail.php'
            ],
            'link_quotes' => [
                'class' => FoquzPollLinkQuotesFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset7/_foquz_poll_link_quotes.php'
            ],
            'answer_quotes' => [
                'class' => FoquzPollAnswerQuotesFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset7/_foquz_poll_answer_quotes.php'
            ],
            'answer_quotes_group' => [
                'class' => FoquzPollAnswerQuotesGroupFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset7/_foquz_poll_answer_quotes_group.php'
            ],
            'answer_quotes_detail' => [
                'class' => FoquzPollAnswerQuotesDetailFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset7/_foquz_poll_answer_quotes_detail.php'
            ],
            'poll_answers' => [
                'class' => FoquzPollAnswerFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset7/_foquz_poll_answer.php'
            ],
            'poll_answers_detail' => [
                'class' => FoquzPollAnswerItemFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset7/_foquz_poll_answer_item.php'
            ],
            'poll_answer_item_quote' => [
                'class' => FoquzPollAnswerItemQuoteFixture::class,
            ],
        ];
    }
}