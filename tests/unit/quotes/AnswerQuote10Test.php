<?php

namespace unit\quotes;


use app\modules\foquz\models\quotes\criteria\AnswerCounter;
use app\modules\foquz\models\quotes\FoquzPollAnswerQuotes;
use app\modules\foquz\models\quotes\FoquzPollAnswerQuotesDetail;
use tests\unit\quotes\AnswerQuoteBase;
use tests\unit\quotes\fixtures\FoquzPollAnswerFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerItemFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerItemQuoteFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerQuotesDetailFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerQuotesFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerQuotesGroupFixture;
use tests\unit\quotes\fixtures\FoquzPollFixture;
use tests\unit\quotes\fixtures\FoquzPollLinkQuotesFixture;
use tests\unit\quotes\fixtures\FoquzQuestionDetailFixture;
use tests\unit\quotes\fixtures\FoquzQuestionFixture;
use tests\unit\quotes\fixtures\FoquzQuestionStarRatingOptionsFixture;
use tests\unit\quotes\fixtures\DictionariesElementsFixture;
use tests\unit\quotes\fixtures\DictionariesFixture;
use tests\unit\quotes\fixtures\RecipientQuestionDetailFixture;
use yii\test\FixtureTrait;


/**
 * Одна квота с логикой по ИЛИ
 * Без групп
 * Одно отдельное условие Варианты ответов ДР
 */
class AnswerQuote10Test extends AnswerQuoteBase
{
    use FixtureTrait;
    

    /**
     * @description Проверка не срабавывания квоты на реципиенте
     */
    public function testAnswerQuote1()
    {
        // Донор
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=1876',
        ], 135735);
        $this->assertNotFireQuote($response);

        // Реципиент 4 ответа
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=1876',
        ], 135736);
        $this->assertFireQuote($response);
    }

    public function testAnswerQuote2()
    {
        // Донор
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=163',
        ], 135735);
        $this->assertNotFireQuote($response);

        // Реципиент 0 ответов
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=163',
        ], 135736);
        $this->assertNotFireQuote($response);
    }

    public function testAnswerCounter()
    {
        $ac = new AnswerCounter(42476);

        $count = $ac->getAnswersCount(
            135736,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE,
            ['1876'],
            [
                ['1876']
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_OR,
            '2025-01-01 00:00:00',
        );
        $this->assertEquals(4, $count);

    }

    public function fixtures()
    {
        return [
            'poll' => [
                'class' => FoquzPollFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset10/_foquz_poll.php'
            ],
            'questions' => [
                'class' => FoquzQuestionFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset10/_foquz_question.php'
            ],
            'dictionaries' => [
                'class' => DictionariesFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset10/_dictionaries.php'
            ],
            'dictionaries_elements' => [
                'class' => DictionariesElementsFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset10/_dictionaries_elements.php'
            ],
            'recipient_question_detail' => [
                'class' => RecipientQuestionDetailFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset10/_recipient_question_detail.php'
            ],
            'questions_detail' => [
                'class' => FoquzQuestionDetailFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset10/_foquz_question_detail.php'
            ],
            'questions_star_rating_options' => [
                'class' => FoquzQuestionStarRatingOptionsFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset10/_foquz_question_star_rating_options.php'
            ],
            'link_quotes' => [
                'class' => FoquzPollLinkQuotesFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset10/_foquz_poll_link_quotes.php'
            ],
            'answer_quotes' => [
                'class' => FoquzPollAnswerQuotesFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset10/_foquz_poll_answer_quotes.php'
            ],
            'answer_quotes_group' => [
                'class' => FoquzPollAnswerQuotesGroupFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset10/_foquz_poll_answer_quotes_group.php'
            ],
            'answer_quotes_detail' => [
                'class' => FoquzPollAnswerQuotesDetailFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset10/_foquz_poll_answer_quotes_detail.php'
            ],
            'poll_answers' => [
                'class' => FoquzPollAnswerFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset10/_foquz_poll_answer.php'
            ],
            'poll_answers_detail' => [
                'class' => FoquzPollAnswerItemFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset10/_foquz_poll_answer_item.php'
            ],
            'poll_answer_item_quote' => [
                'class' => FoquzPollAnswerItemQuoteFixture::class,
            ],
        ];
    }
}