<?php

namespace tests\unit\quotes;


use app\modules\foquz\models\quotes\criteria\AnswerCounter;
use app\modules\foquz\models\quotes\FoquzPollAnswerQuotes;
use app\modules\foquz\models\quotes\FoquzPollAnswerQuotesDetail;
use app\modules\foquz\models\quotes\FoquzPollLinkQuotes;
use app\modules\foquz\services\quotes\QuoteService;
use tests\unit\quotes\fixtures\FoquzPollAnswerFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerItemFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerQuotesDetailFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerQuotesFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerQuotesGroupFixture;
use tests\unit\quotes\fixtures\FoquzPollFixture;
use tests\unit\quotes\fixtures\FoquzPollLinkQuotesFixture;
use tests\unit\quotes\fixtures\FoquzQuestionDetailFixture;
use tests\unit\quotes\fixtures\FoquzQuestionFixture;

use tests\unit\quotes\fixtures\FoquzPollAnswerItemQuoteFixture;
use yii\test\FixtureTrait;


/**
 * Лимит ответов 2
 * Одна квота с логикой по ИЛИ
 * Одна группа с логикой по ИЛИ
 * Два отдельных условия
 */
class AnswerQuote1Test extends AnswerQuoteBase
{
    use FixtureTrait;

    /**
     * Квота срабатывает на втором вопросе группы
     */
    public function testAnswerQuote1()
    {
        // Вопрос 1 (Вар1,Вар3) (нет такого условия)
        $this->setPostData([
            'FoquzAnswerItem' => 'detail_item[0]=98300&detail_item[1]=98302',
        ]);
        $response = $this->controller->runAction('save-answer', [
            'authKey' => 'f0fbcc46eb606e0704b3d1dc3361d453',
            'questionId' => 135735
        ]);
        $this->assertNotFireQuote($response);
        $this->assertLogNonExists(714880, 364339, 87);

        // Вопрос 2 (Вар2) 3 ответа (условие есть)
        $this->setPostData([
            'FoquzAnswerItem' => 'detail_item[0]=98304',
        ]);
        $response = $this->controller->runAction('save-answer', [
            'authKey' => 'f0fbcc46eb606e0704b3d1dc3361d453',
            'questionId' => 135736
        ]);
        $this->assertFireQuote($response);
        $this->assertLogExists(714880, 364340, 87);
    }


    /**
     * Квота срабатывает на отдельном условии
     */
    public function testAnswerQuote3()
    {
        // Вопрос 1 (Вар2) 4 ответа  (условие есть)
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98301',
        ], 135735);
        $this->assertFireQuote($response);
        $this->assertLogExists(714880, 364339, 87);
    }

    /**
     * Квота срабатывает на отдельном условии
     */
    public function testAnswerQuote4()
    {
        // Вопрос 2 (Вар1, Вар2) 2 ответа (условие есть)
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98303&detail_item[1]=98304',
        ], 135736);
        $this->assertFireQuote($response);
        $this->assertLogExists(714880, 364340, 87);
    }

    /**
     * Квота не срабатывает когда ответ не попадает под условие
     */
    public function testAnswerQuote5()
    {
        // Вопрос 1 (Вар1, Вар2, Свойвар) 1 ответ  (нет такого условия)
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98300&detail_item[1]=98301&detail_item[2]=is_self_answer&self_variant=sometext',
        ], 135735);
        $this->assertNotFireQuote($response);
        $this->assertLogNonExists(714880, 364339, 87);
    }

    /**
     * Квота не срабатывает когда ответ не попадает под условие
     */
    public function testAnswerQuote6()
    {
        // Вопрос 1 (затрудняюсь ответить) 1 ответ  (нет такого условия)
        $response = $this->runAction([
            'FoquzAnswerItem' => 'skipped=1',
        ], 135735);
        $this->assertNotFireQuote($response);
        $this->assertLogNonExists(714880, 364339, 87);
    }

    /**
     * Квота не срабатывает когда ответ не попадает под условие (несколько ответов)
     */
    public function testAnswerQuote7()
    {
        // Вопрос 1 (затрудняюсь ответить) 1 ответ (нет такого условия)
        $response = $this->runAction([
            'FoquzAnswerItem' => 'skipped=1',
        ], 135735);
        $this->assertNotFireQuote($response);
        $this->assertLogNonExists(714880, 364339, 87);

        // Вопрос 2 (пропуск ответа) 2 ответа (нет такого условия)
        $response = $this->runAction([
            'FoquzAnswerItem' => '',
        ], 135736);
        $this->assertNotFireQuote($response);
        $this->assertLogNonExists(714880, 364340, 87);
    }

    /**
     * Квота не срабатывает когда лимит ответов больше чем в условии
     */
    public function testAnswerQuote8()
    {
        $this->changeAnswerLimit(5);

        // Вопрос 1 (Вар2) 4 ответа
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98301',
        ], 135735);
        $this->assertNotFireQuote($response);
        $this->assertLogExists(714880, 364339, 87);
    }


    /**
     * Квота не срабатывает при отключенном лимите ответов
     */
    public function testAnswerQuote9()
    {
        $this->changeAnswerLimit(0);

        // Вопрос 1 (Вар2) 4 ответа
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98301',
        ], 135735);
        $this->assertNotFireQuote($response);
        $this->assertLogExists(714880, 364339, 87);
    }

    public function testAnswerQuote10()
    {
        // Вопрос 1 (Вар3) (должно сработать условие Вар3,Свойвар)
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98302',
        ], 135735);

        $this->assertFireQuote($response);
        $this->assertLogExists(714880, 364339, 87);
    }


    public function testCheckQuote()
    {
        $service = new QuoteService(FoquzPollLinkQuotes::findOne(10));
        $this->assertTrue($service->checkQuote(42476, FoquzPollAnswerQuotes::findOne(87)));

        $this->changeAnswerLimit(3);
        $this->assertTrue($service->checkQuote(42476, FoquzPollAnswerQuotes::findOne(87)));

        $this->changeAnswerLimit(4);
        $this->assertTrue($service->checkQuote(42476, FoquzPollAnswerQuotes::findOne(87)));

        $this->changeAnswerLimit(5);
        $this->assertFalse($service->checkQuote(42476, FoquzPollAnswerQuotes::findOne(87)));
    }

    public function testAnswerCounter()
    {
        $ac = new AnswerCounter(42476);

        $count = $ac->getAnswersCount(
            135735,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE,
            ['-1', '98302'],
            [
                ['-1', '98302']
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_OR,
            '2025-01-01 00:00:00',
        );
        $this->assertEquals(0, $count);

        $count = $ac->getAnswersCount(
            135736,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE,
            ['98304'],
            [
                ['98304']
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_OR,
            '2025-01-01 00:00:00',
        );
        $this->assertEquals(3, $count);

        $count = $ac->getAnswersCount(
            135735,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE,
            ['98301'],
            [
                ['98301']
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_OR,
            '2025-01-01 00:00:00',
        );
        $this->assertEquals(4, $count);

        $count = $ac->getAnswersCount(
            135736,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE,
            ['98303', '98304'],
            [
                ['98303', '98304']
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_OR,
            '2025-01-01 00:00:00',
        );
        $this->assertEquals(2, $count);


        $count = $ac->getAnswersCount(
            135735,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE,
            ['-1',], // выбранные варианты в ответе
            [
                ['-1', '98302'] // варианты в критерии квоты
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_OR,
            '2025-01-01 00:00:00',
        );
        $this->assertEquals(3, $count);


        $count = $ac->getAnswersCount(
            135735,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE,
            ['98302',], // выбранные варианты в ответе
            [
                ['-1', '98302'] // варианты в критерии квоты
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_OR,
            '2025-01-01 00:00:00',
        );
        $this->assertEquals(2, $count);


        $count = $ac->getAnswersCount(
            135736,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE,
            ['98303',], // выбранные варианты в ответе
            [
                ['98303', '98304'] // варианты в критерии квоты
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_OR,
            '2025-01-01 00:00:00',
        );
        $this->assertEquals(4, $count);

        $count = $ac->getAnswersCount(
            135736,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE,
            ['98304',], // выбранные варианты в ответе
            [
                ['98303', '98304'] // варианты в критерии квоты
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_OR,
            '2025-01-01 00:00:00',
        );
        $this->assertEquals(3, $count);
    }

    public function fixtures()
    {
        return [
            'poll' => [
                'class' => FoquzPollFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset1/_foquz_poll.php'
            ],
            /*'question_priority_settings' => [
                'class' => FoquzQuestionPrioritySettingsFixture::class,
            ],
            'question_end_screen_logo' => [
                'class' => FoquzQuestionEndScreenLogoFixture::class,
            ],*/
            'questions' => [
                'class' => FoquzQuestionFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset1/_foquz_question.php'
            ],
            'questions_detail' => [
                'class' => FoquzQuestionDetailFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset1/_foquz_question_detail.php'
            ],
            'link_quotes' => [
                'class' => FoquzPollLinkQuotesFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset1/_foquz_poll_link_quotes.php'
            ],
            'answer_quotes' => [
                'class' => FoquzPollAnswerQuotesFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset1/_foquz_poll_answer_quotes.php'
            ],
            'answer_quotes_group' => [
                'class' => FoquzPollAnswerQuotesGroupFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset1/_foquz_poll_answer_quotes_group.php'
            ],
            'answer_quotes_detail' => [
                'class' => FoquzPollAnswerQuotesDetailFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset1/_foquz_poll_answer_quotes_detail.php'
            ],
            'poll_answers' => [
                'class' => FoquzPollAnswerFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset1/_foquz_poll_answer.php'
            ],
            'poll_answers_detail' => [
                'class' => FoquzPollAnswerItemFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset1/_foquz_poll_answer_item.php'
            ],
            'poll_answer_item_quote' => [
                'class' => FoquzPollAnswerItemQuoteFixture::class,
            ],
        ];
    }
}