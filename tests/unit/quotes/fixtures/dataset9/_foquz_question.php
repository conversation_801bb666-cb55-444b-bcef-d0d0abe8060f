<?php
return [
    [
        'id' => 135735,
        'poll_id' => 42476,
        'name' => 'question'.uniqid(),
        'text' => 'question'.uniqid(),
        'rating_type' => \app\modules\foquz\models\FoquzQuestion::RATING_TYPE_START,
        'type' => \app\modules\foquz\models\FoquzQuestion::TYPE_TEXT,
        'position' => 1,
        'is_tmp' => false,
        'service_name' => 'question'.uniqid(),
        'description' => 'Вопрос 1',
        'is_system' => 0,
        'is_source' => 0,
        'main_question_type' => \app\modules\foquz\models\FoquzQuestion::TYPE_VARIANTS,
        'created_at' => time(),
        'updated_at' => time(),
        'created_by' => 1,
        'updated_by' => 1,
        'self_variant_text' => 'Свой вариант',
    ],
    [
        'id' => 135736,
        'poll_id' => 42476,
        'name' => 'question'.uniqid(),
        'text' => 'question'.uniqid(),
        'rating_type' => \app\modules\foquz\models\FoquzQuestion::RATING_TYPE_START,
        'type' => \app\modules\foquz\models\FoquzQuestion::TYPE_TEXT,
        'position' => 2,
        'is_tmp' => false,
        'service_name' => 'question'.uniqid(),
        'description' => 'ЗР',
        'is_system' => 0,
        'is_source' => 0,
        'main_question_type' => \app\modules\foquz\models\FoquzQuestion::TYPE_STAR_RATING,
        'created_at' => time(),
        'updated_at' => time(),
        'created_by' => 1,
        'updated_by' => 1,
    ],
    [
        'id' => 135737,
        'poll_id' => 42476,
        'name' => 'question'.uniqid(),
        'text' => 'question'.uniqid(),
        'rating_type' => \app\modules\foquz\models\FoquzQuestion::RATING_TYPE_START,
        'type' => \app\modules\foquz\models\FoquzQuestion::TYPE_TEXT,
        'position' => 3,
        'is_tmp' => false,
        'service_name' => 'question'.uniqid(),
        'description' => 'Вопрос 3',
        'is_system' => 0,
        'is_source' => 0,
        'main_question_type' => \app\modules\foquz\models\FoquzQuestion::TYPE_VARIANTS,
        'created_at' => time(),
        'updated_at' => time(),
        'created_by' => 1,
        'updated_by' => 1,
        'self_variant_text' => 'Свой вариант',
    ],
    [
        'id' => 135738,
        'poll_id' => 42476,
        'name' => 'question'.uniqid(),
        'text' => 'question'.uniqid(),
        'rating_type' => \app\modules\foquz\models\FoquzQuestion::RATING_TYPE_START,
        'type' => \app\modules\foquz\models\FoquzQuestion::TYPE_TEXT,
        'position' => 4,
        'is_tmp' => false,
        'service_name' => 'question'.uniqid(),
        'description' => 'ЗР2',
        'is_system' => 0,
        'is_source' => 0,
        'main_question_type' => \app\modules\foquz\models\FoquzQuestion::TYPE_STAR_RATING,
        'created_at' => time(),
        'updated_at' => time(),
        'created_by' => 1,
        'updated_by' => 1,
    ],

    [
        'id' => 135739,
        'poll_id' => 42476,
        'name' => 'question'.uniqid(),
        'text' => 'question'.uniqid(),
        'rating_type' => \app\modules\foquz\models\FoquzQuestion::RATING_TYPE_START,
        'type' => \app\modules\foquz\models\FoquzQuestion::TYPE_TEXT,
        'position' => 5,
        'is_tmp' => false,
        'service_name' => 'question'.uniqid(),
        'description' => 'Стандартный конечный экран',
        'is_system' => 0,
        'is_source' => 0,
        'main_question_type' => \app\modules\foquz\models\FoquzQuestion::TYPE_INTERMEDIATE_BLOCK,
        'created_at' => time(),
        'updated_at' => time(),
        'created_by' => 1,
        'updated_by' => 1,
    ],
    [
        'id' => 135740,
        'poll_id' => 42476,
        'name' => 'question'.uniqid(),
        'text' => 'question'.uniqid(),
        'rating_type' => \app\modules\foquz\models\FoquzQuestion::RATING_TYPE_START,
        'type' => \app\modules\foquz\models\FoquzQuestion::TYPE_TEXT,
        'position' => 6,
        'is_tmp' => false,
        'service_name' => 'question'.uniqid(),
        'description' => 'Конечный экран Квотафул',
        'is_system' => 0,
        'is_source' => 0,
        'main_question_type' => \app\modules\foquz\models\FoquzQuestion::TYPE_INTERMEDIATE_BLOCK,
        'created_at' => time(),
        'updated_at' => time(),
        'created_by' => 1,
        'updated_by' => 1,
    ],
    [
        'id' => 135741,
        'poll_id' => 42476,
        'name' => 'question'.uniqid(),
        'text' => 'question'.uniqid(),
        'rating_type' => \app\modules\foquz\models\FoquzQuestion::RATING_TYPE_START,
        'type' => \app\modules\foquz\models\FoquzQuestion::TYPE_TEXT,
        'position' => 7,
        'is_tmp' => false,
        'service_name' => 'question'.uniqid(),
        'description' => 'Конечный экран Скринаут',
        'is_system' => 0,
        'is_source' => 0,
        'main_question_type' => \app\modules\foquz\models\FoquzQuestion::TYPE_INTERMEDIATE_BLOCK,
        'created_at' => time(),
        'updated_at' => time(),
        'created_by' => 1,
        'updated_by' => 1,
    ],
    [
        'id' => 135742,
        'poll_id' => 42476,
        'name' => 'question'.uniqid(),
        'text' => 'question'.uniqid(),
        'rating_type' => \app\modules\foquz\models\FoquzQuestion::RATING_TYPE_START,
        'type' => \app\modules\foquz\models\FoquzQuestion::TYPE_TEXT,
        'position' => 8,
        'is_tmp' => false,
        'service_name' => 'question'.uniqid(),
        'description' => 'Еще один конечный экран',
        'is_system' => 0,
        'is_source' => 0,
        'main_question_type' => \app\modules\foquz\models\FoquzQuestion::TYPE_INTERMEDIATE_BLOCK,
        'created_at' => time(),
        'updated_at' => time(),
        'created_by' => 1,
        'updated_by' => 1,
    ],
];