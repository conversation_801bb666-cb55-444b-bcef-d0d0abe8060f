<?php

namespace unit\quotes;


use app\modules\foquz\models\quotes\FoquzPollLinkQuotes;
use app\modules\foquz\services\quotes\QuoteService;
use tests\unit\quotes\AnswerQuoteBase;
use tests\unit\quotes\fixtures\FoquzPollAnswerFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerItemFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerItemQuoteFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerQuotesDetailFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerQuotesFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerQuotesGroupFixture;
use tests\unit\quotes\fixtures\FoquzPollFixture;
use tests\unit\quotes\fixtures\FoquzPollLinkQuotesFixture;
use tests\unit\quotes\fixtures\FoquzQuestionDetailFixture;
use tests\unit\quotes\fixtures\FoquzQuestionFixture;
use tests\unit\quotes\fixtures\FoquzQuestionIntermediateBlockSettingFixture;
use tests\unit\quotes\fixtures\FoquzQuestionStarRatingOptionsFixture;
use yii\test\FixtureTrait;


/**
 * Квота по ссылкам
 */
class AnswerQuote15Test extends AnswerQuoteBase
{
    use FixtureTrait;

    public function testLinkQuote1()
    {
        $qs = new QuoteService(FoquzPollLinkQuotes::findOne(10));
        $res = $qs->checkLinkQuote();

        $this->assertTrue($res['result']);
    }

    /**
     * С конечным экраном
     */
    public function testLinkQuote2()
    {
        $this->runAction([
            'FoquzAnswerItem' => 'rating=5',
        ], 135736);

        $qs = new QuoteService(FoquzPollLinkQuotes::findOne(10));
        $res = $qs->checkLinkQuote();

        $this->assertFalse($res['result']);
        $this->assertEquals(FoquzPollLinkQuotes::QUOTE_LIMIT_IS_OVER, $res['reason']);
        $this->assertEquals(135741, $res['endScreen']);
    }

    /**
     * Без конечного экрана
     */
    public function testLinkQuote3()
    {
        $q = FoquzPollLinkQuotes::findOne(10);
        $q->end_screen_quotefull = null;
        $q->save(false);
        $this->runAction([
            'FoquzAnswerItem' => 'rating=5',
        ], 135736);

        $qs = new QuoteService(FoquzPollLinkQuotes::findOne(10));
        $res = $qs->checkLinkQuote();

        $this->assertFalse($res['result']);
        $this->assertEquals(FoquzPollLinkQuotes::QUOTE_LIMIT_IS_OVER, $res['reason']);
        $this->assertEquals(null, $res['endScreen']);
    }

    /**
     * задана дата Закончить прием ответов
     */
    public function testLinkQuote4()
    {
        $q = FoquzPollLinkQuotes::findOne(10);
        $q->datetime_end = date('Y-m-d H:i:s');
        $q->limit = 10;
        $q->save(false);
        $this->runAction([
            'FoquzAnswerItem' => 'rating=5',
        ], 135736);

        $qs = new QuoteService(FoquzPollLinkQuotes::findOne(10));
        $res = $qs->checkLinkQuote();

        $this->assertFalse($res['result']);
        $this->assertEquals(FoquzPollLinkQuotes::QUOTE_TIME_IS_OVER, $res['reason']);
    }


    public function fixtures()
    {
        return [
            'poll' => [
                'class' => FoquzPollFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset15/_foquz_poll.php'
            ],
            'questions' => [
                'class' => FoquzQuestionFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset15/_foquz_question.php'
            ],
            'questions_detail' => [
                'class' => FoquzQuestionDetailFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset15/_foquz_question_detail.php'
            ],
            'question_intermediate_block_setting' => [
                'class' => FoquzQuestionIntermediateBlockSettingFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset15/_foquz_question_intermediate_block_setting.php',
            ],
            'questions_star_rating_options' => [
                'class' => FoquzQuestionStarRatingOptionsFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset15/_foquz_question_star_rating_options.php'
            ],
            'link_quotes' => [
                'class' => FoquzPollLinkQuotesFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset15/_foquz_poll_link_quotes.php'
            ],
            'answer_quotes' => [
                'class' => FoquzPollAnswerQuotesFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset15/_foquz_poll_answer_quotes.php'
            ],
            'answer_quotes_group' => [
                'class' => FoquzPollAnswerQuotesGroupFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset15/_foquz_poll_answer_quotes_group.php'
            ],
            'answer_quotes_detail' => [
                'class' => FoquzPollAnswerQuotesDetailFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset15/_foquz_poll_answer_quotes_detail.php'
            ],
            'poll_answers' => [
                'class' => FoquzPollAnswerFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset15/_foquz_poll_answer.php'
            ],
            'poll_answers_detail' => [
                'class' => FoquzPollAnswerItemFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset15/_foquz_poll_answer_item.php'
            ],
            'poll_answer_item_quote' => [
                'class' => FoquzPollAnswerItemQuoteFixture::class,
            ],
        ];
    }
}