<?php

namespace unit\quotes;


use app\modules\foquz\models\quotes\criteria\AnswerCounter;
use app\modules\foquz\models\quotes\FoquzPollAnswerQuotes;
use app\modules\foquz\models\quotes\FoquzPollAnswerQuotesDetail;
use tests\unit\quotes\AnswerQuoteBase;
use tests\unit\quotes\fixtures\FoquzPollAnswerFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerItemFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerItemQuoteFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerQuotesDetailFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerQuotesFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerQuotesGroupFixture;
use tests\unit\quotes\fixtures\FoquzPollFixture;
use tests\unit\quotes\fixtures\FoquzPollLinkQuotesFixture;
use tests\unit\quotes\fixtures\FoquzQuestionDetailFixture;
use tests\unit\quotes\fixtures\FoquzQuestionFixture;

use yii\test\FixtureTrait;


/**
 * Лимит ответов 2
 * Логика работы условий - через ИЛИ
 * Без групп
 * Все условия относятся к одному вопросу (Вопрос1)
 */
class AnswerQuote5Test extends AnswerQuoteBase
{
    use FixtureTrait;


    public function testAnswerQuote1()
    {
        // Вопрос 1 (Вар1) 3 ответа (условие есть)
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98300',
        ], 135735);
        $this->assertFireQuote($response);
    }

    public function testAnswerQuote2()
    {
        // Вопрос 1 (Вар2) 4 ответа (условие есть)
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98301',
        ], 135735);
        $this->assertFireQuote($response);
    }


    /**
     * Считаем максимальное кол-во ответов на первый вопрос по какому-то из этих вариантов
     */
    public function testAnswerQuote3()
    {
        $this->changeAnswerLimit(4);
        // Вопрос 1 (Вар1) 3 ответа (условие есть)
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98300',
        ], 135735);
        $this->assertFireQuote($response);

    }
    /**
     * Считаем сумму ответов на первый вопрос у которых были (вар1 или вар2)
     */
    public function testAnswerQuote4()
    {
        $this->changeAnswerLogicOperation(\app\modules\foquz\models\quotes\FoquzPollAnswerQuotes::LOGIC_OPERATION_AND);
        $this->changeAnswerLimit(7);

        // Вопрос 1 (Вар2) 4 ответа (условие есть)
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98301',
        ], 135735);

        $this->assertFireQuote($response);
    }

    public function testAnswerCounter()
    {
        $ac = new AnswerCounter(42476);

        $count = $ac->getAnswersCount(
            135735,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE,
            ['98300'],
            [
                ['98300', '98301']
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_OR,
            '2025-01-01 00:00:00',
        );
        $this->assertEquals(3, $count);

        $count = $ac->getAnswersCount(
            135735,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE,
            ['98301'],
            [
                ['98301', '98301']
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_OR,
            '2025-01-01 00:00:00',
        );
        $this->assertEquals(4, $count);
    }

    public function fixtures()
    {
        return [
            'poll' => [
                'class' => FoquzPollFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset5/_foquz_poll.php'
            ],
            'questions' => [
                'class' => FoquzQuestionFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset5/_foquz_question.php'
            ],
            'questions_detail' => [
                'class' => FoquzQuestionDetailFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset5/_foquz_question_detail.php'
            ],
            'link_quotes' => [
                'class' => FoquzPollLinkQuotesFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset5/_foquz_poll_link_quotes.php'
            ],
            'answer_quotes' => [
                'class' => FoquzPollAnswerQuotesFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset5/_foquz_poll_answer_quotes.php'
            ],
            'answer_quotes_group' => [
                'class' => FoquzPollAnswerQuotesGroupFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset5/_foquz_poll_answer_quotes_group.php'
            ],
            'answer_quotes_detail' => [
                'class' => FoquzPollAnswerQuotesDetailFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset5/_foquz_poll_answer_quotes_detail.php'
            ],
            'poll_answers' => [
                'class' => FoquzPollAnswerFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset5/_foquz_poll_answer.php'
            ],
            'poll_answers_detail' => [
                'class' => FoquzPollAnswerItemFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset5/_foquz_poll_answer_item.php'
            ],
            'poll_answer_item_quote' => [
                'class' => FoquzPollAnswerItemQuoteFixture::class,
            ],
        ];
    }
}