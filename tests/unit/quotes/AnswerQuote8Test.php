<?php

namespace unit\quotes;


use app\modules\foquz\models\FoquzPollAnswer;
use app\modules\foquz\models\FoquzQuestion;
use app\modules\foquz\models\quotes\criteria\AnswerCounter;
use app\modules\foquz\models\quotes\FoquzPollAnswerQuotes;
use app\modules\foquz\models\quotes\FoquzPollAnswerQuotesDetail;
use tests\unit\quotes\AnswerQuoteBase;
use tests\unit\quotes\fixtures\FoquzPollAnswerFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerItemFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerItemQuoteFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerQuotesDetailFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerQuotesFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerQuotesGroupFixture;
use tests\unit\quotes\fixtures\FoquzPollFixture;
use tests\unit\quotes\fixtures\FoquzPollLinkQuotesFixture;
use tests\unit\quotes\fixtures\FoquzQuestionDetailFixture;
use tests\unit\quotes\fixtures\FoquzQuestionFixture;
use tests\unit\quotes\fixtures\FoquzQuestionStarRatingOptionsFixture;
use yii\test\FixtureTrait;


/**
 * Две квоты с логикой по ИЛИ
 * Без групп
 * Одно и Два отдельных условия
 */
class AnswerQuote8Test extends AnswerQuoteBase
{
    use FixtureTrait;


    /**
     * @description Проверка срабавывания квоты на первом вопросе и установка статуса анкеты как quote-full
     */
    public function testAnswerQuote1()
    {
        // Вопрос 1 (Вар1,Вар3) 2 ответа
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98300&detail_item[1]=98302',
        ], 135735);
        $this->assertFireQuote($response);

        //анкета должна получить статус quota-full
        $m = $this->getResultAnketa();
        $this->assertEquals(FoquzPollAnswer::STATUS_QUOTE_FULL, $m->status);
    }

    /**
     * @description Проверка срабавывания квоты на последнем вопросе и установка статуса анкеты как quote-full
     */
    public function testAnswerQuote2()
    {
        FoquzQuestion::findOne(135736)->delete();

        // Вопрос 1 (Вар1,Вар3) 2 ответа
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98300&detail_item[1]=98302',
        ], 135735);
        $this->assertFireQuote($response);

        //анкета должна получить статус quota-full
        $m = $this->getResultAnketa();
        $this->assertEquals(FoquzPollAnswer::STATUS_QUOTE_FULL, $m->status);
    }

    /**
     * @description Выполняется условие второй квоты, но сама квота не срабатывает
     * Следующая квота уже не проверяется
     * Анкета получает статус done
     */
    public function testAnswerQuote3()
    {
        // Вопрос 1 (Вар2) 3 ответа
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98301',
        ], 135735);
        $this->assertNotFireQuote($response);


        // Звездный рейтинг
        $response = $this->runAction([
            'FoquzAnswerItem' => 'rating=3',
        ], 135736);
        $this->assertNotFireQuote($response);

        //анкета должна получить статус done
        $m = $this->getResultAnketa();
        $this->assertEquals(FoquzPollAnswer::STATUS_DONE, $m->status);
    }


    /**
     * @description Анкета не подходит ни под одну из квот
     * Анкета получает статус screen-out
     */
    public function testAnswerQuote4()
    {
        // Вопрос 1 (Вар1,Вар2,Вар3) нет такого условия
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98300&detail_item[1]=98301&detail_item[2]=98302',
        ], 135735);
        $this->assertNotFireQuote($response);


        // Звездный рейтинг
        $response = $this->runAction([
            'FoquzAnswerItem' => 'rating=3',
        ], 135736);
        $this->assertNotFireQuote($response);

        //анкета должна получить статус screen-out
        $m = $this->getResultAnketa();
        $this->assertEquals(FoquzPollAnswer::STATUS_SCREEN_OUT, $m->status);
    }


    /**
     * @description Квоты на ответы не настроены. Анкета получает статус done
     */
    public function testAnswerQuote5()
    {

        FoquzPollAnswerQuotes::findOne(87)->softDelete();
        FoquzPollAnswerQuotes::findOne(88)->softDelete();
        FoquzPollAnswerQuotes::findOne(89)->softDelete();

        // Вопрос 1 (Вар1,Вар2,Вар3) нет такого условия
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98300&detail_item[1]=98301&detail_item[2]=98302',
        ], 135735);
        $this->assertNotFireQuote($response);

        // Звездный рейтинг
        $response = $this->runAction([
            'FoquzAnswerItem' => 'rating=3',
        ], 135736);
        $this->assertNotFireQuote($response);

        //анкета должна получить статус done
        $m = $this->getResultAnketa();
        $this->assertEquals(FoquzPollAnswer::STATUS_DONE, $m->status);
    }

    /**
     * @description должны учитываться только ответы созданные после добавления квоты
     */
    public function testAnswerQuote6()
    {
        $m = FoquzPollAnswerQuotes::findOne(87);
        $m->created_at = date('Y-m-d H:i:s');
        $m->updated_at = date('Y-m-d H:i:s');
        $m->save();

        $m = FoquzPollAnswerQuotes::findOne(88);
        $m->created_at = date('Y-m-d H:i:s');
        $m->updated_at = date('Y-m-d H:i:s');
        $m->save();

        $m = FoquzPollAnswerQuotes::findOne(89);
        $m->created_at = date('Y-m-d H:i:s');
        $m->updated_at = date('Y-m-d H:i:s');
        $m->save();


        // Вопрос 1 (Вар1,Вар3) 2 ответа
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98300&detail_item[1]=98302',
        ], 135735);
        $this->assertNotFireQuote($response);


        // Вопрос 1 (Вар1,Вар3) 2 ответа
        $response = $this->runAction([
            'FoquzAnswerItem' => 'detail_item[0]=98300&detail_item[1]=98302',
        ], 135735);
        $this->assertNotFireQuote($response);

    }


    public function testAnswerCounter()
    {
        $ac = new AnswerCounter(42476);

        $count = $ac->getAnswersCount(
            135735,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE,
            ['98300', '98302'],
            [
                ['98300', '98302']
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_OR,
            '2025-01-01 00:00:00',
        );
        $this->assertEquals(2, $count);


        $count = $ac->getAnswersCount(
            135735,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE,
            ['98301'],
            [
                ['98301']
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_OR,
            '2025-01-01 00:00:00',
        );
        $this->assertEquals(3, $count);


    }
    public function fixtures()
    {
        return [
            'poll' => [
                'class' => FoquzPollFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset8/_foquz_poll.php'
            ],
            'questions' => [
                'class' => FoquzQuestionFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset8/_foquz_question.php'
            ],
            'questions_detail' => [
                'class' => FoquzQuestionDetailFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset8/_foquz_question_detail.php'
            ],
            'questions_star_rating_options' => [
                'class' => FoquzQuestionStarRatingOptionsFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset8/_foquz_question_star_rating_options.php'
            ],
            'link_quotes' => [
                'class' => FoquzPollLinkQuotesFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset8/_foquz_poll_link_quotes.php'
            ],
            'answer_quotes' => [
                'class' => FoquzPollAnswerQuotesFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset8/_foquz_poll_answer_quotes.php'
            ],
            'answer_quotes_group' => [
                'class' => FoquzPollAnswerQuotesGroupFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset8/_foquz_poll_answer_quotes_group.php'
            ],
            'answer_quotes_detail' => [
                'class' => FoquzPollAnswerQuotesDetailFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset8/_foquz_poll_answer_quotes_detail.php'
            ],
            'poll_answers' => [
                'class' => FoquzPollAnswerFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset8/_foquz_poll_answer.php'
            ],
            'poll_answers_detail' => [
                'class' => FoquzPollAnswerItemFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset8/_foquz_poll_answer_item.php'
            ],
            'poll_answer_item_quote' => [
                'class' => FoquzPollAnswerItemQuoteFixture::class,
            ],
        ];
    }
}