<?php

namespace unit\quotes;


use app\modules\foquz\models\quotes\criteria\AnswerCounter;
use app\modules\foquz\models\quotes\FoquzPollAnswerQuotes;
use app\modules\foquz\models\quotes\FoquzPollAnswerQuotesDetail;
use app\modules\foquz\models\quotes\FoquzPollLinkQuotes;
use app\modules\foquz\services\quotes\QuoteService;
use tests\unit\quotes\AnswerQuoteBase;
use tests\unit\quotes\fixtures\FoquzPollAnswerFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerItemFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerItemQuoteFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerQuotesDetailFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerQuotesFixture;
use tests\unit\quotes\fixtures\FoquzPollAnswerQuotesGroupFixture;
use tests\unit\quotes\fixtures\FoquzPollFixture;
use tests\unit\quotes\fixtures\FoquzPollLinkQuotesFixture;
use tests\unit\quotes\fixtures\FoquzQuestionDetailFixture;
use tests\unit\quotes\fixtures\FoquzQuestionFixture;

use yii\test\FixtureTrait;


/**
 * Лимит ответов 2
 * Одна квота с логикой по ИЛИ
 * Без групп
 * Два отдельных условия
 */
class AnswerQuote4Test extends AnswerQuoteBase
{
    use FixtureTrait;


    /**
     * Квота срабатывает на втором условии
     */
    public function testAnswerQuote1()
    {
        // Вопрос 1 (Затрудн ответить) условие подходит
        $this->setPostData([
            'FoquzAnswerItem' => 'skipped=1',
        ]);
        $response = $this->controller->runAction('save-answer', [
            'authKey' => 'f0fbcc46eb606e0704b3d1dc3361d453',
            'questionId' => 135735
        ]);
        $this->assertFireQuote($response);


        // Вопрос 2 (Пропуск ответа) (условие подходит)
        $this->setPostData([
            'FoquzAnswerItem' => '',
        ]);
        $response = $this->controller->runAction('save-answer', [
            'authKey' => 'f0fbcc46eb606e0704b3d1dc3361d453',
            'questionId' => 135736
        ]);

        $this->assertFireQuote($response);
    }


    /**
     * Квота не срабатывает при логике по И
     */
    public function testAnswerQuote2()
    {
        $this->changeAnswerLogicOperation(\app\modules\foquz\models\quotes\FoquzPollAnswerQuotes::LOGIC_OPERATION_AND);

        // Вопрос 1 (Затрудн ответить) (условие подходит)
        $response = $this->runAction([
            'FoquzAnswerItem' => 'skipped=1',
        ], 135735);
        $this->assertFireQuote($response);


        // Вопрос 2 (Пропуск ответа) 2 ответа (условие подходит)
        $response = $this->runAction([
            'FoquzAnswerItem' => '',
        ], 135736);
        $this->assertFireQuote($response);

    }

    /**
     * Квота срабатывает при логике по И
     */
    public function testAnswerQuote3()
    {
        $this->changeAnswerLogicOperation(\app\modules\foquz\models\quotes\FoquzPollAnswerQuotes::LOGIC_OPERATION_AND);
        $this->changeAnswerLimit(1);

        // Вопрос 1 (Затрудн ответить) (условие подходит)
        $response = $this->runAction([
            'FoquzAnswerItem' => 'skipped=1',
        ], 135735);
        $this->assertFireQuote($response);


        // Вопрос 2 (Пропуск ответа) (условие подходит)
        $response = $this->runAction([
            'FoquzAnswerItem' => '',
        ], 135736);
        $this->assertFireQuote($response);
    }


    public function testAnswerCounter()
    {
        $ac = new AnswerCounter(42476);

        $count = $ac->getAnswersCount(
            135735,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_DONT_ASK,
            [],
            [
                []
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_OR,
            '2025-01-01 00:00:00',
        );
        $this->assertEquals(1, $count);

        $count = $ac->getAnswersCount(
            135736,
            FoquzPollAnswerQuotesDetail::BEHAVIOR_SKIP,
            [],
            [
                []
            ],
            FoquzPollAnswerQuotes::LOGIC_OPERATION_OR,
            '2025-01-01 00:00:00',
        );
        $this->assertEquals(2, $count);
    }

    public function testCheckQuote()
    {
        $service = new QuoteService(FoquzPollLinkQuotes::findOne(10));
        $this->assertTrue($service->checkQuote(42476, FoquzPollAnswerQuotes::findOne(87)));

        $this->changeAnswerLimit(3);
        $this->assertFalse($service->checkQuote(42476, FoquzPollAnswerQuotes::findOne(87)));
    }

    public function fixtures()
    {
        return [
            'poll' => [
                'class' => FoquzPollFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset4/_foquz_poll.php'
            ],
            'questions' => [
                'class' => FoquzQuestionFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset4/_foquz_question.php'
            ],
            'questions_detail' => [
                'class' => FoquzQuestionDetailFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset4/_foquz_question_detail.php'
            ],
            'link_quotes' => [
                'class' => FoquzPollLinkQuotesFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset4/_foquz_poll_link_quotes.php'
            ],
            'answer_quotes' => [
                'class' => FoquzPollAnswerQuotesFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset4/_foquz_poll_answer_quotes.php'
            ],
            'answer_quotes_group' => [
                'class' => FoquzPollAnswerQuotesGroupFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset4/_foquz_poll_answer_quotes_group.php'
            ],
            'answer_quotes_detail' => [
                'class' => FoquzPollAnswerQuotesDetailFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset4/_foquz_poll_answer_quotes_detail.php'
            ],
            'poll_answers' => [
                'class' => FoquzPollAnswerFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset4/_foquz_poll_answer.php'
            ],
            'poll_answers_detail' => [
                'class' => FoquzPollAnswerItemFixture::class,
                'dataFile' => __DIR__ . '/fixtures/dataset4/_foquz_poll_answer_item.php'
            ],
            'poll_answer_item_quote' => [
                'class' => FoquzPollAnswerItemQuoteFixture::class,
            ],
        ];
    }
}