<?php

use yii\db\Migration;

class m250528_080030_add_ip_filter_mode_column_in_user_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('user', 'ip_filter_mode', $this->tinyInteger()
            ->notNull()
            ->defaultValue(0)
            ->comment('Фильтр по IP (0 - выключен, 1 - черный список, 2 - белый список)'));
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('user', 'ip_filter_mode');
    }
}
