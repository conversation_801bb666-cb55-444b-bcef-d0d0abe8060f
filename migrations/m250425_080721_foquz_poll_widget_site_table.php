<?php

use yii\db\Migration;

/**
 * Class m250425_080721_foquz_poll_widget_site_table
 */
class m250425_080721_foquz_poll_widget_site_table extends Migration
{
    public function safeUp()
    {
        $this->createTable('{{%foquz_poll_widget_site}}', [
            'id' => $this->primaryKey(),
            'send_id' => $this->integer(),
            'company_id' => $this->integer(),
            'site' => $this->string(255),
        ]);


        $this->createIndex(
            'idx-foquz_poll_widget_site-site',
            '{{%foquz_poll_widget_site}}',
            'site'
        );

        $this->addForeignKey('fk-foquz_poll_widget_site-send_id',
            '{{%foquz_poll_widget_site}}',
            'send_id',
            '{{%foquz_poll_mailing_list_send}}',
            'id',
            'CASCADE'
        );

        $this->addForeignKey('fk-foquz_poll_widget_site-company_id',
            '{{%foquz_poll_widget_site}}',
            'company_id',
            '{{%company}}',
            'id',
            'CASCADE'
        );
    }

    public function safeDown()
    {

        $this->dropTable('{{%foquz_poll_widget_site}}');
    }
}
