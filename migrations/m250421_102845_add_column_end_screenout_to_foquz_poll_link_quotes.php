<?php

use yii\db\Migration;

/**
 * Class m250421_102845_add_column_end_screenout_to_foquz_poll_link_quotes
 */
class m250421_102845_add_column_end_screenout_to_foquz_poll_link_quotes extends Migration
{

    public function safeUp()
    {
        $this->addColumn('{{%foquz_poll_link_quotes}}', 'end_screenout', $this->integer()
            ->null()->after('active')->comment('Конечный экран для анкет со статусом Скринаут'));
    }


    public function safeDown()
    {
        $this->dropColumn('{{%foquz_poll_link_quotes}}', 'end_screenout');
    }
}
