<?php

use yii\db\Migration;

class m250528_080100_create_user_ip_filter_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('user_ip_filter', [
            'id' => $this->primaryKey(),
            'user_id' => $this->integer()->notNull()->comment('ID пользователя'),
            'filter' => $this->string(64)->notNull()->comment('IP или CIDR фильтр'),
            'mode' => $this->tinyInteger()->notNull()->defaultValue(1)->comment('Режим фильтрации (1 - черный список, 2 - белый список)'),
        ]);

        $this->createIndex('idx_user_ip_filter_user_id', 'user_ip_filter', 'user_id');

        $this->addForeignKey(
            'fk_user_ip_filter_user_id',
            'user_ip_filter',
            'user_id',
            'user',
            'id',
            'CASCADE',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey('fk_user_ip_filter_user_id', 'user_ip_filter');
        $this->dropIndex('idx_user_ip_filter_user_id', 'user_ip_filter');
        $this->dropTable('user_ip_filter');
    }
}
