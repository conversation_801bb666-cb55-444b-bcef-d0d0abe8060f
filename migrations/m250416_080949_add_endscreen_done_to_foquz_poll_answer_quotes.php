<?php

use yii\db\Migration;

/**
 * Class m250416_080949_add_endscreen_done_to_foquz_poll_answer_quotes
 */
class m250416_080949_add_endscreen_done_to_foquz_poll_answer_quotes extends Migration
{
    public function safeUp()
    {
        $this->addColumn('{{%foquz_poll_answer_quotes}}', 'end_screen_done', $this->integer()
            ->null()->comment('Конечный экран для заполненной анкеты'));
        $this->addCommentOnColumn('{{%foquz_poll_answer_quotes}}', 'end_screen', 'Конечный экран для квотафул анкеты');
    }

    public function safeDown()
    {
        $this->dropColumn('{{%foquz_poll_answer_quotes}}', 'end_screen_done');
    }
}
