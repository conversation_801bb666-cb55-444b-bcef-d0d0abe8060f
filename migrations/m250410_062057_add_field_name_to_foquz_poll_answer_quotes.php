<?php

use yii\db\Migration;
use yii\db\Query;

/**
 * Class m250410_062057_add_field_name_to_foquz_poll_answer_quotes
 */
class m250410_062057_add_field_name_to_foquz_poll_answer_quotes extends Migration
{

    public function safeUp()
    {
        $this->addColumn('{{%foquz_poll_answer_quotes}}', 'name', $this->string(255)->null()
            ->after('logic_operation')->comment('Название для квоты'));

        $linkQuoteId = 0; $idx = 0;
        foreach((new Query)->from('foquz_poll_answer_quotes')->where(['deleted_at' => null])->orderBy('id')->each() as $quote) {
            if ($linkQuoteId != $quote['link_quote_id']) {
                $linkQuoteId = $quote['link_quote_id'];
                $idx = 0;
            }
            $idx ++;
            $this->update('{{%foquz_poll_answer_quotes}}', ['name' => "Квота " . $idx], ['id' => $quote['id']]);
        }
    }


    public function safeDown()
    {
        $this->dropColumn('{{%foquz_poll_answer_quotes}}', 'name');
    }
}
