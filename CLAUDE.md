# CLAUDE.md - Foquz-Core Development Guide

## Project Overview
Foquz-core is a comprehensive, enterprise-grade survey and polling platform built with a dual-layer architecture combining PHP backend views with Knockout.js frontend view models. The system supports complex survey logic, real-time analytics, and automated marketing workflows across multiple communication channels.

## Technology Stack

### Backend (PHP/Yii2)
- **Framework**: Yii 2 (PHP 8.2+)
- **Database**: MySQL with Redis caching
- **Queue System**: RabbitMQ for background processing
- **Architecture**: MVC pattern with console commands and modules
- **Key Features**: OAuth2 server, SAML authentication, Sentry error tracking

### Frontend (JavaScript/TypeScript)
- **Framework**: KnockoutJS with TypeScript
- **Build Tool**: Webpack 5 with Babel transpilation
- **CSS**: LESS preprocessing
- **Architecture**: MVVM pattern with component-based structure
- **UI**: Responsive design with mobile support

## Architecture Overview

### Dual-Layer Architecture
The system uses a **dual-layer architecture** that combines:
- **PHP Views**: Server-side rendering and data preparation
- **Knockout.js ViewModels**: Client-side logic, interactions, and dynamic UI

### Data Flow Pattern
1. **PHP Controller** prepares data and renders view
2. **PHP View** outputs JavaScript globals:
   ```php
   $this->registerJs("
       var QUESTIONS = " . Json::encode($questions) . ";
       var POLL = " . Json::encode($poll) . ";
   ");
   ```
3. **Knockout ViewModel** consumes globals
4. **Components** handle user interactions and state updates

## Directory Structure

### Standard Module Pattern
```
Feature: Statistics
├── PHP View:     modules/foquz/views/foquz-poll/stats.php
└── KO ViewModel: ko/pages/poll/stats/
```

### Frontend Structure
```
ko/
├── pages/           # Page-level view models and components
├── presentation/    # UI Kit and reusable components (auto-included)
├── components/      # Knockout components 
├── models/          # Data models and business logic
├── dialogs/         # Modal dialogs and sidesheets
├── utils/           # Utility functions and helpers
├── bindings/        # Custom Knockout bindings
└── constants/       # Application constants
```

### Page Component Structure
```
ko/pages/poll/stats/
├── index.js         # Main view model entry point
├── external.js      # External access logic
├── widget.js        # Widget-specific logic
├── template.php     # PHP template integration
├── style.less       # Component styles
├── components/      # Sub-components
├── modals/          # Modal dialogs
├── templates/       # Template partials
├── types/           # Type-specific handlers
└── utils/           # Component utilities
```

## Answer Management System - Critical Module

The answer system is the **most complex part** of the codebase with three distinct modules, each serving different purposes and containing extremely complex view models.

### ⚠️ Critical Complexity Warning
Answer view models are **extremely complex** because they:
- Recreate entire view model hierarchies for each operation
- Handle complex sorting/filtering logic from scratch
- Manage state across multiple nested components
- Process large datasets with real-time updates

**Navigation Tip**: When working with answer modules, expect to trace through multiple view model layers and be prepared for intricate state management patterns.

### 1. "answers" - Company-Wide Answers
**Purpose**: All answers across all polls for a company
```
├── PHP Views:     modules/foquz/views/answers/
└── KO ViewModels: ko/pages/answers/
```

**Key Features**:
- Company-wide answer aggregation
- Cross-poll analytics and reporting
- Advanced filtering and segmentation
- Bulk operations and exports
- Multi-poll comparison tools

### 2. "poll-answers" - Single Poll Answers
**Purpose**: Answers from a specific poll (internal access)
```
├── PHP Views:     modules/foquz/views/foquz-poll/answers/
└── KO ViewModels: ko/models/answers/
```

**Key Features**:
- Single poll response management
- Detailed individual response analysis
- Question-specific breakdowns
- Internal user access controls
- Real-time response monitoring

### 3. "answers-external" - External Poll Answers
**Purpose**: Answers from a specific poll via external generated link
```
├── PHP Views:     modules/foquz/views/foquz-poll/answers-external/
└── KO ViewModels: ko/models/answers-external/
```

**Key Features**:
- External stakeholder access
- Limited permission model
- Shareable analytics links
- Branded report interfaces
- Security restrictions for external access

### Answer Module Development Guidelines

**Working with Answer Modules**:
- **Expect complexity** - View models recreate entire hierarchies
- **Test thoroughly** - Sorting/filtering affects multiple layers  
- **Trace carefully** - State management spans multiple components
- **Consider performance** - Large datasets require optimization
- **Plan for hierarchy** - Each operation may rebuild view model chains

**Common Patterns in Answer Modules**:
```javascript
// Answer view models often recreate hierarchies
this.rebuildAnswerHierarchy = () => {
    this.answers.removeAll();
    this.processedData().forEach(answer => {
        // Complex nested view model creation
        const answerViewModel = new AnswerViewModel(answer);
        this.answers.push(answerViewModel);
    });
};

// Sorting affects multiple layers
this.applySorting = (sortField, direction) => {
    // Complex sorting logic that affects nested components
    this.rebuildAnswerHierarchy();
    this.updateFilteredResults();
    this.refreshStatistics();
};
```

## Sidesheets - Primary UI Pattern

**Sidesheets** are the main interface pattern for forms, detailed views, and API interactions. They slide in from the right side of the screen and are the primary way users interact with complex data and forms.

### When to Use Sidesheets vs Dialogs

**Use Sidesheets for**:
- Forms and data entry
- Detailed views and editing
- Multi-step workflows
- API interactions requiring feedback
- Complex configuration screens
- Data review and analysis

**Use Dialogs for**:
- Simple confirmations
- Quick actions
- Small information displays
- Yes/No decisions

### Sidesheet Structure
```
ko/dialogs/[feature]-sidesheet/
├── index.js         # Component registration
├── model.js         # ViewModel with DialogWrapper
├── template.html    # Sidesheet content template
└── style.less       # Sidesheet-specific styles
```

### Opening Sidesheets

**Basic Pattern**:
```javascript
// Enable sidesheet functionality
import { DialogsModule } from "Utils/dialogs-module";

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params);
    DialogsModule(this); // Enables openSidesheet method
  }

  openFeatureSidesheet(dataModel) {
    this.openSidesheet({
      name: "feature-sidesheet",
      params: {
        data: dataModel,
        blocked: this.blocked,
      },
      events: {
        hide: () => { /* cleanup logic */ }
      }
    });
  }
}
```

### Common Sidesheet Usage Patterns

**1. Form Submissions & API Calls**:
```javascript
// Settings and configuration
this.openSidesheet({
  name: "channel-sidesheet",
  params: { channel: channelModel },
  events: {
    hide: () => { /* cleanup logic */ },
    submit: (data) => { /* handle form submission */ }
  }
});
```

**2. Data Review & Details**:
```javascript
// Answer reviews, feedback details
this.openSidesheet({
  name: "feedback-review-sidesheet", 
  params: { data: { review } }
});
```

**3. Collection Management**:
```javascript
// System collections, user management
this.openSidesheet({
  name: "system-collection-sidesheet",
  params: { collection, sidesheetContainer: this },
  events: {
    hide: () => this.updateSystemCollection(collection)
  }
});
```

**4. Answer Processing (Complex Example)**:
```javascript
// Processing individual answers with complex workflows
this.openAnswerProcessingSidesheet = (answerModel) => {
  this.openSidesheet({
    name: "answer-processing-sidesheet",
    params: {
      answer: answerModel,
      processingHistory: this.getProcessingHistory(answerModel.id),
      availableActions: this.getAvailableActions(answerModel),
      parentContext: this
    },
    events: {
      hide: () => {
        // Refresh answer data
        this.refreshAnswerData();
        this.updateStatistics();
      },
      statusChanged: (newStatus) => {
        // Update parent view model
        answerModel.status(newStatus);
        this.rebuildAnswerHierarchy();
      }
    }
  });
};
```

### Sidesheet Implementation

**Base Structure**:
```javascript
// model.js
import { DialogWrapper } from 'Dialogs/wrapper';

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);
    
    // Extract parameters
    this.data = params.data || {};
    this.parentContext = params.parentContext;
    
    // Setup observables
    this.isLoading = ko.observable(false);
    this.errors = ko.observableArray([]);
    
    // Initialize form data
    this.initializeFormData();
  }

  initializeFormData() {
    // Complex initialization logic for sidesheet data
  }

  submitForm() {
    this.isLoading(true);
    // Complex form submission logic
    // API calls, validation, error handling
  }

  onHide() {
    // Cleanup when sidesheet closes
    if (this.params.events && this.params.events.hide) {
      this.params.events.hide();
    }
  }
}

// index.js  
ko.components.register('feature-sidesheet', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('feature-sidesheet');
      return new ViewModel(params, element);
    }
  },
  template: html
});
```

### Key Sidesheet Features

**Built-in Functionality**:
- **Responsive design** - Adapts to screen size
- **Animation system** - Slide in/out transitions
- **Event handling** - Submit, hide, change events
- **Auto-scrolling** - Handles overflow content
- **Close button** - Positioned outside sidesheet
- **Mask overlay** - Click-to-close background
- **Loading states** - Built-in loading indicators
- **Error handling** - Standardized error display

**Advanced Sidesheet Patterns**:
```javascript
// Multi-step sidesheet workflow
export class MultiStepSidesheetViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);
    
    this.currentStep = ko.observable(1);
    this.totalSteps = 4;
    this.stepData = ko.observableArray([]);
    
    // Step validation
    this.canProceedToNextStep = ko.computed(() => {
      return this.validateCurrentStep();
    });
  }

  nextStep() {
    if (this.canProceedToNextStep()) {
      this.currentStep(this.currentStep() + 1);
    }
  }

  previousStep() {
    if (this.currentStep() > 1) {
      this.currentStep(this.currentStep() - 1);
    }
  }
}
```

## Component Registration & Auto-Loading

### Automatic Component Loading
Components in `/presentation` are **automatically included** - no manual imports required:

```javascript
// ✅ Automatically available - no imports needed
<component params="name: 'modal-container'"></component>
<foquz-modals-container></foquz-modals-container>
<dialogs-container></dialogs-container>
```

### Manual Component Registration
```javascript
// For custom components outside /presentation
ko.components.register('custom-component', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('custom-component');
      return new CustomViewModel(params, element);
    }
  },
  template: html
});
```

## Asset Registration Pattern

```php
// @NOTE: Files are generated by webpack and minified. Do not read or edit them.
// Check knockout view models and css files for correct file names.

// CSS Registration
$this->registerCSSFile('/js/poll.stats.css', [
    'depends' => [FoquzAsset::className()]
]);

// JS Registration  
$this->registerJSFile('/js/poll.stats.js', [
    'depends' => [FoquzAsset::className()]
]);
```

## Development Guidelines

### Working with Complex Modules
1. **Answer Modules**: Expect to trace through multiple view model layers
2. **Sidesheet Workflows**: Plan for multi-step interactions and state management
3. **Component Integration**: Leverage automatic loading for presentation components
4. **Data Flow**: Follow PHP → Knockout global variable patterns

### Code Quality Standards
- Use TypeScript for type safety
- Follow KnockoutJS observable patterns
- Implement proper error handling in sidesheets
- Test complex answer module workflows thoroughly
- Document sidesheet parameter contracts

### Performance Considerations
- Answer modules process large datasets - optimize queries
- Sidesheet animations should be smooth - avoid heavy operations during transitions
- Use computed observables for derived data
- Implement proper cleanup in sidesheet hide events

### Security Best Practices
- Validate all sidesheet form inputs
- Sanitize data before displaying in answer modules
- Implement proper permission checks for external answer access
- Use CSRF protection for sidesheet form submissions

## File Naming Conventions

### PHP Views
- `kebab-case` for directories: `foquz-poll/`
- `camelCase` for files: `stats.php`

### Knockout Files
- `kebab-case` for directories: `poll/stats/`
- `camelCase` for JavaScript: `index.js`, `external.js`
- `kebab-case` for styles: `style.less`, `widget.less`

### Component Classes
- BEM methodology: `question-statistics__question-header`
- Feature prefixes: `poll-stats__actions`
- Sidesheet classes: `feature-sidesheet__content`

## Common Patterns Summary

### Answer Module Pattern
```javascript
// Complex hierarchy rebuilding
this.rebuildAnswerHierarchy();
this.updateFilteredResults();
this.refreshStatistics();
```

### Sidesheet Opening Pattern
```javascript
this.openSidesheet({
  name: "feature-sidesheet",
  params: { data, context: this },
  events: { hide: () => this.cleanup() }
});
```

### Component Registration Pattern
```javascript
ko.components.register('component-name', {
  viewModel: { createViewModel: (params, info) => new ViewModel(params, info.element) },
  template: html
});
```

---

**Remember**: This is a mature codebase with established patterns. The answer management system and sidesheets are the most complex parts - approach them with careful planning and thorough testing. Follow existing conventions and be especially careful when modifying answer-related modules due to their intricate state management.